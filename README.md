# CycleVitaApp

A React Native application for managing health tasks, medications, and formula tracking.

## Overview

CycleVitaApp is a comprehensive health management application that helps users track their medications, formula intake, and other health-related tasks. The app provides features for scheduling medications, managing formula intake, and maintaining a detailed health tracking system.

## Features

### Task Management
- **Medication Tracking**
  - Schedule medication intake
  - Set custom reminders
  - Track medication strength and quantity
  - Support for multiple medication categories
  - Custom scheduling options (daily, weekly, monthly, yearly)

- **Formula Management**
  - Schedule formula intake
  - Custom reminder settings
  - Flexible scheduling patterns
  - Track formula consumption

### Core Functionalities

#### Medication Management
- Add new medications with detailed information
- Set medication schedules
- Configure custom alerts
- Track medication history
- Support for various medication types and categories

#### Formula Tracking
- Schedule formula intake times
- Set custom frequency patterns
- Configure alerts and reminders
- Track formula consumption history

#### Time Management
- Custom scheduling options
- Support for different time zones
- UTC time conversion handling
- Flexible date and time formatting

## Technical Architecture

### State Management
- Redux for global state management
- Custom hooks for component-level state
- Form management using React Hook Form

### Data Validation
- Yup validation schemas for form inputs
- Custom validation rules for:
  - Phone numbers
  - Email addresses
  - Names
  - Numeric inputs

### File Handling
- Support for image uploads
- Azure Blob Storage integration
- File type validation
- Size restrictions (5MB limit)

### Date & Time Handling
- Dayjs for date manipulation
- UTC time conversion
- Custom date formatting
- Time slot management

## Setup and Installation

## Project Structure

```plaintext
src/
├── containers/
│   ├── taskManager/          # Task management logic and hooks
│   ├── auth/                 # Authentication related containers
│   └── profile/              # User profile management
├── store/
│   ├── slices/              # Redux slices for state management
│   │   ├── taskManager/     # Task-related state management
│   │   ├── user/           # User-related state management
│   │   └── app/            # App-wide state management
│   └── middleware/          # Redux middleware and async actions
├── types/
│   ├── schemas/             # TypeScript type definitions
│   │   ├── task.ts         # Task-related types
│   │   ├── user.ts         # User-related types
│   │   └── dietTracker.ts  # Diet tracking types
│   └── interfaces/         # Common interfaces
├── utils/
│   ├── helpers.ts           # Utility functions
│   ├── taskManager.ts       # Task management utilities
│   ├── validation.ts        # Validation utilities
│   └── dateTime.ts         # Date and time utilities
├── theme/
│   ├── assets/             # Application assets
│   │   ├── images/         # Image assets
│   │   │   ├── pills/     # Medication pill icons
│   │   │   └── svgs/      # SVG icons
│   │   └── fonts/         # Custom fonts
│   └── styles/            # Global styles and themes
├── components/       # Reusable UI components
├── constants/        # App-wide constants
├── context/          # React Context providers
├── hooks/            # Custom React hooks
├── navigators/       # Navigation configuration
├── screens/          # Screen components
├── services/         # API and service integrations
├── store/            # Redux store configuration
├── theme/            # Styling and theming
├── translations/     # i18n translations
├── types/            # TypeScript type definitions
├── utils/            # Utility functions
```

## 🛠️ Dependencies

### Core Dependencies
- React Native 0.74.1
- React 18.2.0
- TypeScript 5.1.3
- Redux Toolkit & React Redux
- React Navigation
- React Query
- Firebase (Analytics, Messaging, Remote Config)

### UI & Styling
- @gorhom/bottom-sheet
- react-native-reanimated
- react-native-gesture-handler
- react-native-linear-gradient
- react-native-svg
- react-native-calendars
- react-native-gifted-charts

### State & Data Management
- @reduxjs/toolkit
- @tanstack/react-query [Note: Not used due encryption issue ]
- react-native-mmkv
- redux-persist

### Forms & Validation
- react-hook-form
- yup

### Testing & Development
- Jest
- React Testing Library
- ESLint
- Prettier [planned for future]
- Husky [planned for future]
- TypeScript

## 🔧 Development Setup

### Prerequisites
- Node.js >= 18
- Yarn 3.6.4
- iOS: Xcode and CocoaPods
- Android: Android Studio and Android SDK

### Installation
```bash
# Install dependencies
yarn install

# iOS specific setup
cd ios && pod install && cd ..

# Start the development server
yarn start

# Run on iOS
yarn ios

# Run on Android

yarn android
```

### Environment Setup
The app uses different environment configurations:
- `.env.development` - Development environment
- `.env.uat` - User Acceptance Testing environment
- `.env.production` - Production environment

#### Environment Configuration Scripts
Instead of manually modifying environment files directly, use the provided npm scripts to configure the environment and related files:

```bash
# Configure for development environment
yarn configure:dev

# Configure for QA/UAT environment
yarn configure:qa

# Configure for production environment
yarn configure:prod
```

These scripts execute `./scripts/configure-env.sh` with the appropriate environment parameter. The script automatically updates:
- Firebase configuration (analytics collection settings)
- Analytics environment settings
- API base URLs

Always use these scripts instead of directly modifying the environment files to ensure consistent configuration across the development team.

## 🧪 Testing

```bash
# Run tests
yarn test

# Generate test coverage report
yarn test:report
```

## 📦 Build & Deployment

### Android
```bash
# Create release bundle
yarn android:bundle

# Create release APK
yarn android:release
```

### iOS
```bash
# Install pods
yarn pods

# Build through Xcode
```

## 🔐 Security

- Secure storage using MMKV
- Background secure mode
- Environment variable protection

## 📱 Platform Support

- iOS 13.0+
- Android 5.0+ (API level 21+)

## 🤝 Contributing

1. Follow the commit convention
2. Write tests for new features
3. Ensure TypeScript types are properly defined
4. Follow the project's code style guidelines

