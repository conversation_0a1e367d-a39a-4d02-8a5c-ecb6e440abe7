import React, { createContext, useContext, useEffect, useState } from "react";
import { RemoteConfigService, IRemoteConfigValues, defaultValues } from "@/services/RemoteConfigService";

interface RemoteConfigContextProps {
    remoteConfig: IRemoteConfigValues;
    isLoaded: boolean;
}

const RemoteConfigContext = createContext<RemoteConfigContextProps>({
    remoteConfig: defaultValues,
    isLoaded: false,
});

export const RemoteConfigProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [remoteConfig, setRemoteConfig] = useState<IRemoteConfigValues>(defaultValues);
    const [isLoaded, setIsLoaded] = useState(false);

    useEffect(() => {
        const fetchConfig = async () => {
            const fetchedConfig = await RemoteConfigService.fetch();
            setRemoteConfig(fetchedConfig);
            setIsLoaded(true);
        };

        fetchConfig();
    }, []);

    return (
        <RemoteConfigContext.Provider value={{ remoteConfig, isLoaded }}>
            {children}
        </RemoteConfigContext.Provider>
    );
};

export const useRemoteConfig = () => useContext(RemoteConfigContext);
