import { useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '@/store';
import { TimezoneService } from '@/utils/timezoneService';
import { selectProfileTimeZone, selectShowTimeZoneModal, setProfileTzToggle, updateTimeZoneToggleThunk } from '@/store/slices/settingsSlice';

export const useTimezone = () => {
  const dispatch = useAppDispatch();
  const profileTimeZone = useAppSelector(selectProfileTimeZone);
  const showTimeZoneModal = useAppSelector(selectShowTimeZoneModal);

  const initializeTimezone = useCallback(async () => {
    await TimezoneService.initializeTimezone();
  }, []);

  const checkTimezoneOnFocus = useCallback(async () => {
    await TimezoneService.checkTimezoneOnFocus();
  }, []);

  const updateToNewTimeZone = useCallback(async (newTimeZone: string, isModal?: boolean, restrictFetchingUser?: boolean, isTimeOut?: boolean) => {
    await TimezoneService.updateToNewTimeZone(newTimeZone, isModal, restrictFetchingUser, isTimeOut);
  }, []);

  const keepCurrentTimeZone = useCallback(() => {
    TimezoneService.keepCurrentTimeZone();
  }, []);

  const refreshUserAndTimezone = useCallback(async () => {
    await TimezoneService.refreshUserAndTimezone();
  }, []);

  const getDeviceTimeZone = useCallback(() => {
    return TimezoneService.getDeviceTimeZone();
  }, []);

  const handleProfileTzToggle = useCallback(async (value: boolean) => {
    dispatch(setProfileTzToggle(value));
    try {
      await dispatch(updateTimeZoneToggleThunk(value)).unwrap();
      // Only update dependent APIs if the toggle update was successful
      TimezoneService.updateTzDependentAPIs();
    } catch (error) {
      console.error('Failed to update timezone toggle:', error);
      // Revert the toggle state if the API call failed
      dispatch(setProfileTzToggle(!value));
    }
  }, [])

  return {
    profileTimeZone,
    showTimeZoneModal,
    initializeTimezone,
    checkTimezoneOnFocus,
    updateToNewTimeZone,
    keepCurrentTimeZone,
    refreshUserAndTimezone,
    getDeviceTimeZone,
    handleProfileTzToggle
  };
}; 