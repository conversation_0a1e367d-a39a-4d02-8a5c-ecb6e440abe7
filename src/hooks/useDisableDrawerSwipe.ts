import { useNavigation } from "@react-navigation/native";
import { useEffect } from "react";

export function useDisableDrawerSwipe() {
  const navigation = useNavigation();

  useEffect(() => {
    const unsubscribeStart = navigation.addListener("transitionStart", () => {
      const drawer = navigation.getParent()?.getParent();
      drawer?.setOptions({ swipeEnabled: false });
    });

    const unsubscribeEnd = navigation.addListener("beforeRemove", () => {
      const drawer = navigation.getParent()?.getParent();
      drawer?.setOptions({ swipeEnabled: true });
    });

    return () => {
      unsubscribeStart();
      unsubscribeEnd();
    };
  }, [navigation]);
}
