import { useEffect, useCallback } from 'react';
import { Alert, Linking, Platform } from 'react-native';
import notifee, { AndroidImportance, AuthorizationStatus, TriggerType } from '@notifee/react-native';

export const useNotificationScheduler = () => {
  const showNotificationAlert = () => {
    Alert.alert(
      'Enable Notifications',
      'Notifications are currently disabled. Would you like to enable them in the settings?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Go to Settings',
          onPress: () => {
            Linking.openSettings();
          },
        },
      ],
      { cancelable: false }
    );
  };


  const requestNotificationPermission = async () => {
    const authStatus = await notifee.requestPermission();

    if (authStatus.authorizationStatus !== AuthorizationStatus.AUTHORIZED) {
      showNotificationAlert()
      throw new Error('Permission not granted for notifications');
    }
  };

  const createNotificationChannel = async () => {
    await notifee.createChannel({
      id: 'default',
      name: 'Default Channel',
      sound: Platform.OS === 'ios' ? 'pillbox.wav' : 'pillbox',
      importance: AndroidImportance.HIGH,
    });
  };

  const scheduleNotification = async (
    title: string,
    body: string,
    scheduledTime: Date
  ) => {
    if (scheduledTime < new Date()) {
      throw new Error('Scheduled time must be in the future.');
    }

    const notificationContent = {
      title,
      body,
      android: {
        channelId: 'default',
        importance: AndroidImportance.HIGH,
        sound: 'pillbox',
      },
      ios: {
        critical: true,
        sound: 'pillbox.wav',
      },
    };

    try {
      await notifee.createTriggerNotification(notificationContent, {
        type: TriggerType.TIMESTAMP,
        timestamp: scheduledTime.getTime(),
      });
    } catch (error) {
      // console.error('Error scheduling notification:1', error);
      throw new Error('Failed to schedule notification.');
    }
  };

  const handleScheduleNotification = useCallback(async () => {
    try {
      await requestNotificationPermission();
      const scheduledTime = new Date(Date.now() + 2000); // 10 seconds from now
      await scheduleNotification('Hello!', 'This is a scheduled notification.', scheduledTime);
    } catch (error) {
      // console.error('Error scheduling notification:2', error);
    }
  }, []);

  useEffect(() => {
    const initialize = async () => {
      try {
        await createNotificationChannel();
        await requestNotificationPermission();
      } catch (error) {
        console.error('Initialization error:', error);
      }
    };
    initialize();
  }, []);

  return { handleScheduleNotification };
};
