import toastService from "@/shared/toast.service";
import { IS_ANDROID, IS_IOS } from "@/theme/_config";
import notifee, {
  AndroidImportance,
  AuthorizationStatus,
  EventType,
} from "@notifee/react-native";
import messaging, {
  FirebaseMessagingTypes,
} from "@react-native-firebase/messaging";
import { useEffect } from "react";
import { PermissionsAndroid, Platform } from "react-native";
import { navigationRef } from "@/utils/Navigation";
import { Screens } from "@/constants";
import { store } from "@/store";
import { registerDeviceThunk } from "@/store/slices/notificationSlice";
import {
  DisablePushNotification,
  PushNotification,
} from "@/types/schemas/PushNotification";
import DeviceInfo from "react-native-device-info";
import { disableRegisterDeviceThunk } from "@/store/slices/notificationSlice";
import { v4 as uuidv4 } from "uuid"; // or use any other way to generate a unique ID

let fcmToken: string | null = null;

export const getFCMToken = () => {
  // Get the notification token
  console.log({ getFCMToken: fcmToken });
  return fcmToken;
};

export const removeFCMToken = async () => {
  // Get the notification token
  return messaging().deleteToken();
};

export const requestPermissionForNotification = async () => {
  if (IS_ANDROID && Number(Platform.Version) >= 33) {
    // If Android 13 or higher...
    return PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
    );
  } else if (IS_IOS) {
    const settings = await notifee.requestPermission();

    // iOS 12 & below => settings is a boolean

    // iOS 13+ => settings is an object with { authorizationStatus: number, ... }

    if (typeof settings === "boolean") {
      return settings ? "granted" : "denied";
    } else {
      // iOS 13+ returns an object

      // 1 = AUTHORIZED, 2 = PROVISIONAL => both are effectively "granted"

      if (
        settings.authorizationStatus === AuthorizationStatus.AUTHORIZED ||
        settings.authorizationStatus === AuthorizationStatus.PROVISIONAL
      ) {
        return "granted";
      } else {
        return "denied";
      }
    }
  } else {
    // On Android < 13, POST_NOTIFICATIONS doesn’t exist, so treat as granted
    return "granted";
  }
};

export function useNotifee() {
  // getting permission from the device
  useEffect(() => {
    (async () => {
      // console.log("useNotifee Fired ===== >>>>");
      try {
        const isDeviceRegistered = await messaging().isDeviceRegisteredForRemoteMessages;
        console.log("Is device registered:", isDeviceRegistered);

        if (!isDeviceRegistered) {
          await messaging().registerDeviceForRemoteMessages();
          console.log("Device registered successfully");
        }

        fcmToken = await messaging().getToken();
        // console.log(`FCM Token on ${Platform.OS}:`, fcmToken);
      } catch (err) {
        console.log("FCM Registration error:", err);
      }
    })();
  }, []);

  // Subscribe to foreground events
  useEffect(() => {
    return notifee.onForegroundEvent(({ type, detail }) => {
      console.log("onForegroundEvent: ", { type, detail });
      switch (type) {
        case EventType.PRESS:
          console.log("🚀 ~ returnnotifee.onForegroundEvent ~ detail:", detail);
          console.log("🚀 ~ returnnotifee.onForegroundEvent ~ type:", type);
          break;
      }
    });
  }, []);

  // Create channel at startup
  useEffect(() => {
    if (IS_ANDROID) {
      createChannel();
    }
  }, []);

  async function createChannel() {
    console.log("Creating notification channel_1");
    await notifee.createChannel({
      id: "my_pillbox_channel",
      name: "Pillbox Notifications",
      sound: "pillbox",
      importance: AndroidImportance.HIGH,
    });
  }

  // return { isLoading };
}

// Note that an async function or a function that returns a Promise
// is required for both subscribers.
async function onMessageReceived(
  message: FirebaseMessagingTypes.RemoteMessage
) {
  const { sentTime, notification = {}, data } = message || {};
  const { title = "", body = "" } = notification;

  try {
    console.log("🚀 ~ onMessageReceived ~ Notification:", message);

    // Create a unique ID to prevent replacement
    // const notificationId = uuidv4(); // Ensures each notification is unique

    toastService.notify(title, body, data);
  } catch (error) {
    console.log("🚀 ~ onMessageReceived ~ Error:", error);
  }
}

export const initiateBackgroundEvent = () => {
  const routeConfig = {
    screen: "MainTabs",
    params: { screen: "Home" },
  };

  if (IS_IOS) {
    messaging().onNotificationOpenedApp(
      async (remoteMessage: FirebaseMessagingTypes.RemoteMessage) => {
        try {
          navigationRef.current?.navigate(Screens.App, routeConfig);
        } catch (error) {
          console.log("Navigation error:", error);
        }
      }
    );
  } else {
    messaging().setBackgroundMessageHandler(
      async (remoteMessage: FirebaseMessagingTypes.RemoteMessage) => {
        console.log(
          "🚀 ~ messaging ~ remoteMessageSetBackgroundMessageHandler:",
          remoteMessage
        );
        try {
          navigationRef.current?.navigate(Screens.App, routeConfig);
        } catch (error) {
          console.log("Navigation error:", error);
        }
      }
    );
  }

  messaging().onMessage(onMessageReceived);
};

export const registerUnRegisterDevice = async (
  pushNotificationEnabled: boolean
) => {
  console.log("pushNotificationEnabled", pushNotificationEnabled);
};

export const registerDeviceOnHub = async (pushNotificationEnabled: boolean) => {
  const uniqueId = await DeviceInfo.getUniqueId();
  const payload: PushNotification = {
    deviceId: uniqueId,
    platform: IS_ANDROID ? "gcm" : "apns",
    deviceToken: getFCMToken() || "",
    isEnabled: pushNotificationEnabled,
  };
  store.dispatch(registerDeviceThunk(payload));
};

export const disableDeviceOnHub = async (pushNotificationDisabled: boolean) => {
  const payload: DisablePushNotification = {
    tokenId: getFCMToken() || "",
    isEnabled: pushNotificationDisabled,
  };
  store.dispatch(disableRegisterDeviceThunk(payload));
};

export const getAndSetFCMToken = async (): Promise<string | null> => {
   if (fcmToken) {
    // console.log("FCM Token already set:", fcmToken);
    return fcmToken;
  }
  try {
    const isDeviceRegistered = await messaging().isDeviceRegisteredForRemoteMessages;
    if (!isDeviceRegistered) {
      await messaging().registerDeviceForRemoteMessages();
    }

    const token = await messaging().getToken();
    fcmToken = token;
    // console.log(`FCM Token: ${token}`);
    return token;
  } catch (err) {
    console.log("Failed to get FCM Token:", err);
    return null;
  }
};