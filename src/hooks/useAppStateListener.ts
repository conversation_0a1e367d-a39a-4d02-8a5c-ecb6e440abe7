import { AppState, AppStateStatus } from 'react-native';
import { useEffect, useCallback, useRef } from 'react';

const useAppStateListener = (
  onActive?: () => void, 
  onInactive?: () => void,
  delay: number = 300 // default delay of 1000ms (1 second)
) => {
  const appState = useRef<AppStateStatus>(AppState.currentState);

  const handleAppStateChange = useCallback((nextAppState: AppStateStatus) => {
    console.log('🔄 AppState changed:', nextAppState);

    // Trigger callbacks only if state is different
    if (appState.current !== nextAppState) {
      if (nextAppState === 'active' && onActive) {
        onActive();
      } else if ((nextAppState === 'background' || nextAppState === 'inactive') && onInactive) {
        // Add a delay before calling onInactive
        setTimeout(() => {
          onInactive();
        }, delay); // delay in milliseconds
      }
    }
    appState.current = nextAppState;
  }, [onActive, onInactive, delay]);

  useEffect(() => {
    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      subscription.remove();
    };
  }, [handleAppStateChange]);
};

export default useAppStateListener;
