import { useEffect } from "react";
import {
  useSharedValue,
  withTiming,
  interpolateColor,
  useAnimatedStyle,
  Easing,
} from "react-native-reanimated";

type UseAnimatedBorderScaleProps = {
  isActive: boolean;
  activeColor: string;
  inactiveColor: string;
  scaleFactor?: number;
};

export const useAnimatedBorderScale = ({
  isActive,
  activeColor = "white",
  inactiveColor = "black",
  scaleFactor = 1.07,
}: UseAnimatedBorderScaleProps) => {
  const scale = useSharedValue(1);
  const borderColorProgress = useSharedValue(isActive ? 1 : 0);

  useEffect(() => {
    borderColorProgress.value = withTiming(isActive ? 1 : 0, { duration: 250 });
    scale.value = withTiming(isActive ? scaleFactor : 1, {
      duration: 500,
      easing: Easing.inOut(Easing.ease),
    });
  }, [isActive]);

  const animatedBorderStyle = useAnimatedStyle(() => {
    const borderColor = interpolateColor(
      borderColorProgress.value,
      [0, 1],
      [inactiveColor, activeColor]
    );
    return {
      borderColor,
    };
  });

  const animatedScaleStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  return {
    animatedBorderStyle,
    animatedScaleStyle,
  };
};
