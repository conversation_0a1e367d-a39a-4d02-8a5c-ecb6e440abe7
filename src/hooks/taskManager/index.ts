import { useCallback } from 'react';
import { useAppDispatch } from '@/store';
import {
  deleteTask,
  fetchTaskById,
  fetchFormulaById,
  fetchFormulaTasks,
  fetchMedicationTasks
} from '@/store/slices/taskManager/taskManager.middleware';

export const useTaskManager = () => {
  const dispatch = useAppDispatch();

  const deleteMedTask = useCallback(async (taskId: number) => {
    try {
      const result = await dispatch(deleteTask(taskId)).unwrap();
      if (taskId === result) {
        await dispatch(fetchMedicationTasks())
        await dispatch(fetchFormulaTasks())
      }
      return result;
    } catch (error) {
      console.error('Failed to delete task:', error);
      throw error;
    }
  }, [dispatch]);

  const fetchTaskDetails = useCallback(async (taskId: number) => {
    try {
      const task = await dispatch(fetchTaskById(taskId)).unwrap();
      return task;
    } catch (error) {
      console.error('Failed to fetch task details:', error);
      throw error;
    }
  }, [dispatch]);


  const fetchFormulaDetails = useCallback(async (formulaId: number) => {
    try {
      const formula = await dispatch(fetchFormulaById(formulaId)).unwrap();
      return formula;
    } catch (error) {
      // console.error('Failed to fetch formula details:', error);
      throw error;
    }
  }, [dispatch]);

  return { deleteMedTask, fetchTaskDetails, fetchFormulaDetails };
};