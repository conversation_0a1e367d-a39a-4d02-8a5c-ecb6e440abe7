import React, { FC } from 'react';
import { StyleProp, TextInput, TextInputProps, TextStyle } from 'react-native';

import { useTheme } from '@/theme';
import { InputStyles } from './Input.styles';
import { useDynamicStyles } from '@/theme/hooks/useDynamicStyles';

export interface InputProps extends TextInputProps {
  secureTextEntry?: boolean;
  inputRef?: React.Ref<TextInput>;
  style?: StyleProp<TextStyle>;
}

const Input: FC<InputProps> = ({
  secureTextEntry = false,
  inputRef,
  style,
  ...rest
}) => {
  const { colors } = useTheme();
  const styles: any = useDynamicStyles(InputStyles);
  return (
    <TextInput
      {...rest}
      ref={inputRef}
      style={[styles.inputStyle, style]}
      secureTextEntry={secureTextEntry}
      caretHidden={false}
      selectionColor={colors.yellow}
      placeholderTextColor={colors.mediumGray}
    />
  );
};

export default Input;
