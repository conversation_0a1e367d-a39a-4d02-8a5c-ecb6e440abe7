import React, { PropsWithChildren, useMemo } from "react";
import { StyleProp, View, ViewStyle } from "react-native";
import { useTheme } from "@/theme";
import { getCardStyle } from "./Card.style";

interface CardProps extends PropsWithChildren {
  style?: StyleProp<ViewStyle>;
}

const Card: React.FC<CardProps> = ({ children, style }) => {
  const theme = useTheme();
  const cardStyle = getCardStyle(theme);

  const mergedStyles = useMemo(
    () => [cardStyle.container, style],
    [cardStyle, style]
  );

  return <View style={mergedStyles}>{children}</View>;
};

export default Card;
