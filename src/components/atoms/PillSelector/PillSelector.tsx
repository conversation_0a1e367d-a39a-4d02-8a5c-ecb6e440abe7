import React, { FC } from 'react';
import { ms } from 'react-native-size-matters';
import { FlatList, Pressable, StyleProp, View, ViewStyle } from 'react-native';

import Typography from '../Typography/Typography';
import { getPillSelectorStyle } from './PillSelector.style';
import { useDynamicStyles } from '@/theme/hooks/useDynamicStyles';

type PillSelectorProps = {
  menu: object[];
  onSelect: (x: any) => void;
  label: string;
  value: any;
  valueKey?: string;
  inputStyle?: StyleProp<ViewStyle>;
  wrapperStyle?: StyleProp<ViewStyle>;
};

const PillSelector: FC<PillSelectorProps> = ({
  label,
  menu,
  onSelect,
  value
}) => {

  const styles:any = useDynamicStyles(getPillSelectorStyle)

  return (
    <View style={styles.wrapper}>
      <View style={{}}>
        <View style={styles.iconBox}>
          {value?.icon ? (
            <value.icon />
          ) : (
            <Typography.B3 style={styles.iconText}>{label}</Typography.B3>
          )}
        </View>
      </View>

      <View style={styles.inputContainer}>
        <FlatList
          numColumns={5}
          data={menu}
          renderItem={({ item }) => (
            <Pressable style={styles.pillItem} onPress={() => onSelect(item)}>
              <View style={item?.id === value?.id && styles.selectedPillItem}>
                <item.icon
                  width={ms(58)}
                  height={ms(59)}
                />
              </View>
            </Pressable>
          )}
          keyExtractor={(item) => String(item.id)}
        />
      </View>
    </View>
  );
};

export default PillSelector;
