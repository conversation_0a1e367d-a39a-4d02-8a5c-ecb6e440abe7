import { Fonts } from '@/constants';

import { Theme } from '@/types/theme/theme';
import { ms, scale, ScaledSheet, verticalScale } from 'react-native-size-matters';

export const getPillSelectorStyle = (theme: Theme) => ScaledSheet.create({
  wrapper: {
    marginVertical: verticalScale(8),
  },
  pillIcon: {
    width: ms(58),
    height: ms(59)
  },
  iconBox: {
    width: ms(87),
    height: ms(87),
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    borderColor: theme.colors.textPrimary,
    borderWidth: ms(2),
    borderRadius: ms(44),
    marginVertical: ms(16),
    padding: ms(10)
  },
  iconText: {
    fontFamily: Fonts.RALEWAY_BOLD,
    textAlign: 'center'
  },
  pillItem: {
    justifyContent: 'center',
    alignItems: 'center',
    width: '20%',
    borderWidth: ms(2),
    borderRadius: ms(118),
    marginBottom: ms(20),
    height: ms(60),
    borderColor: theme.colors.transparent,
  },
  selectedPillItem: {
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: theme.colors.mediumGray,
    borderWidth: ms(2),
    height: ms(60),
    borderRadius: ms(118),
  },
  inputContainer: {
    marginBottom: scale(2)
  },
});
