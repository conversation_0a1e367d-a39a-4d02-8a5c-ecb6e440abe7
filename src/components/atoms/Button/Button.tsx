import { PropsWithChildren, useMemo } from "react";
import { StyleProp, TouchableOpacity, View, ViewStyle } from "react-native";
import { useTheme } from "@/theme";
import { getButtonStyle } from "./Button.style";
import {
  AuthButtonProps,
  ButtonStyleKeys,
} from "@/types/components/atoms/Button";

interface ButtonProps extends PropsWithChildren<AuthButtonProps> {
  style?: StyleProp<ViewStyle>;
  disabled?: boolean;
  activeOpacity?: number
}

const createBtnComponent = (styleKey: ButtonStyleKeys) => {
  return ({
    children,
    disabled,
    style: customStyle,
    ...props
  }: ButtonProps) => {
    const theme = useTheme();
    const buttonStyle = getButtonStyle(theme);

    const mergedStyles = useMemo(
      () => [buttonStyle[styleKey], customStyle],
      [buttonStyle, styleKey, customStyle]
    );

    return (
      <TouchableOpacity disabled={disabled} {...props}>
        <View style={mergedStyles}>{children}</View>
      </TouchableOpacity>
    );
  };
};

const Auth = createBtnComponent("authStyle");
const Main = createBtnComponent("mainStyle");
const Outline = createBtnComponent("outlineStyle");
const BlackOutline = createBtnComponent("blackOutlineStyle");
const YellowOutline = createBtnComponent("yellowOutlineStyle");
const Yellow = createBtnComponent("yellowStyle");

export default {
  Auth,
  Main,
  Outline,
  BlackOutline,
  YellowOutline,
  Yellow,
};
