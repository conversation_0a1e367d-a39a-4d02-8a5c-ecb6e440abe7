/** @format */

// Button.style.ts
import { ms, ScaledSheet } from "react-native-size-matters";
import { Theme } from "@/types/theme/theme";

export const getButtonStyle = (theme: Theme) =>
  ScaledSheet.create({
    authStyle: {
      width: "100%",
      minHeight: ms(48),
      alignItems: "center",
      borderRadius: ms(10),
      justifyContent: "center",
      paddingVertical: ms(10),
      marginVertical: ms(8),
      paddingHorizontal: ms(24),
      backgroundColor: theme.colors.white,
    },
    mainStyle: {
      backgroundColor: theme.colors.primary,
      borderRadius: ms(16),
      alignItems: "center",
      height: ms(47),
      justifyContent: "center",
    },
    yellowStyle: {
      backgroundColor: theme.colors.yellow,
      borderRadius: ms(16),
      alignItems: "center",
      height: ms(47),
      justifyContent: "center",
    },
    outlineStyle: {
      backgroundColor: theme.colors.transparent,
      borderColor: theme.colors.gray,
      borderWidth: ms(1),
      borderRadius: ms(8),
      minHeight: ms(35),
      justifyContent: "center",
      alignItems: "center",
    },
    blackOutlineStyle: {
      backgroundColor: theme.colors.app_bg,
      borderRadius: ms(8),
      minHeight: ms(35),
      justifyContent: "center",
      alignItems: "center",
    },
    yellowOutlineStyle: {
      backgroundColor: theme.colors.transparent,
      borderColor: theme.colors.yellow,
      borderWidth: ms(1),
      borderRadius: ms(8),
      minHeight: ms(47),
      justifyContent: "center",
      alignItems: "center",
    },
  });
