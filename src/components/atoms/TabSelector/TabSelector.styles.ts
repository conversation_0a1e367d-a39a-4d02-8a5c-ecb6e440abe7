import { Theme } from "@/types/theme/theme";
import { ms, ScaledSheet } from "react-native-size-matters";

const getTabSelectorStyle = (theme:Theme) => ScaledSheet.create({
  tabContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: theme.colors.item_bg,
    borderRadius: 8,
  },
  tabButton: {
    alignItems: "center",
    justifyContent: "center", // Ensure content is centered
    height: ms(32),
    backgroundColor: theme.colors.app_bg
  },
  activeTab: {
    backgroundColor: theme.colors.primary,
  },
  tabText: {
    fontSize: ms(12),
    lineHeight: ms(15),
    textTransform: "capitalize",
  },
  activeTabText: {
    color: theme.colors.white, // Highlighted text color
  },
  leftBorder: {
    borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8,
  },
  rightBorder: {
    borderBottomRightRadius: 8,
    borderTopRightRadius: 8,
  },
});

export default getTabSelectorStyle;
