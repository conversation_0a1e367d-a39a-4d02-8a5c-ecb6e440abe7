import React from "react";
import { TouchableOpacity, View } from "react-native";
import Typography from "@/components/atoms/Typography/Typography";
import Common from "@/theme/common.style";
import getTabSelectorStyle from "./TabSelector.styles";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";

interface TabSelectorProps {
  tabs: string[];
  selectedTab: string;
  style?: any;
  onTabPress: (tab: string) => void;
  customeTabText: any
}

const TabSelector: React.FC<TabSelectorProps> = ({
  tabs,
  selectedTab,
  onTabPress,
  style={},
  customeTabText
}) => {
  const tabWidth = 100 / tabs.length + "%"; // Dynamically calculate width for each tab
  const styles: any = useDynamicStyles(getTabSelectorStyle);
  return (
    <View style={styles.tabContainer}>
      {tabs.map((tab, index) => (
        <TouchableOpacity
          key={tab}
          style={[
            styles.tabButton,
            { width: tabWidth }, // Apply dynamic width
            selectedTab === tab ? styles.activeTab: style,
            index === 0 && styles.leftBorder, // Apply left border for the first tab
            index === tabs.length - 1 && styles.rightBorder, // Apply right border for the last tab
          ]}
          onPress={() => onTabPress(tab)}
        >
          <Typography.H1
            style={[
              Common.textBold,
              styles.tabText,
              selectedTab === tab && styles.activeTabText,
              customeTabText,
            ]}
          >
            {tab}
          </Typography.H1>
        </TouchableOpacity>
      ))}
    </View>
  );
};

export default TabSelector;
