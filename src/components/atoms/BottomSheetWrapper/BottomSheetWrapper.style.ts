import { ms, ScaledSheet } from "react-native-size-matters";

import { Theme } from "@/types/theme/theme";

const getBottomSheetStyles = (theme: Theme) => ScaledSheet.create({
  contentContainer: {
    backgroundColor: theme.colors.tile_bg,
    borderTopLeftRadius: 32,
    borderTopRightRadius: 32,
    paddingHorizontal: "16@ms",
  },
  blurBackground: {
    position: "absolute",
    width: "100%",
    height: "100%",
  },
  closeButtonText: {
    color: theme.colors.textPrimary,
    alignSelf: "flex-end",
    marginTop: ms(15),
  },
  headerContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: ms(10),
  },
  backButtonText: {
    color: theme.colors.textPrimary,
    alignSelf: "flex-start",
    marginTop: ms(15),
  },
  scrollContainer: {
    paddingBottom: 20, // Adjust for spacing at the bottom
    flexGrow: 1, // Ensures content grows for proper scrolling
  },
  hitSlopStyle: {
    top: 20,
    bottom: 20,
    left: 20,
    right: 20
  }
});

export default getBottomSheetStyles;
