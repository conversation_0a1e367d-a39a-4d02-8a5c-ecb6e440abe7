import { Fonts } from '@/constants';

import { Theme } from '@/types/theme/theme';
import {
  ScaledSheet,
  verticalScale,
  ms,
  scale
} from 'react-native-size-matters';

export const getRNDateTimePickerStyle = (theme: Theme) => ScaledSheet.create({
  wrapper: {
    marginVertical: verticalScale(8)
  },
  labelStyle: {
    color: theme.colors.textPrimary,
    lineHeight: ms(20),
    marginBottom: ms(10)
  },
  dateTimePicker: {
    borderColor: theme.colors.yellow,
    borderRadius: ms(10),
    borderWidth: ms(2),
    color: theme.colors.white,
    backgroundColor: 'transparent',
    fontFamily: Fonts.RALEWAY_LIGHT,
    fontSize: ms(22)
  },
  androidDatePicker: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center'
  },
  calendarIcon: {
    width: ms(14),
    height: ms(14)
  },
  androidTimePicker: {
    borderColor: theme.colors.textPrimaryYellow,
    borderRadius: 8,
    borderWidth: 1,
    height: ms(34),
    justifyContent: 'center',
    alignItems: 'center'
  },
  valueStyle: {
    fontFamily: Fonts.RALEWAY_BOLD,
    lineHeight: ms(22),
    fontSize: ms(20),
    color: theme.colors.textPrimaryYellow
  },
  dateValueStyle: {
    flex: 1,
    justifyContent: 'space-between',
    color: theme.colors.textPrimaryYellow,
    marginLeft: ms(10)
  },
  errorMessage: {
    position: 'absolute',
    left: 0,
    top: ms(45),
    fontFamily: Fonts.RALEWAY_REGULAR,
    color: theme.colors.red,
    marginTop: verticalScale(4),
    marginBottom: scale(-16)
  }
});
