import { FC, useEffect, useState } from "react";
import DateTimePicker, {
  IOSNativeProps,
  AndroidNativeProps,
  DateTimePickerAndroid,
} from "@react-native-community/datetimepicker";
import { ms } from "react-native-size-matters";
import {
  AppStateStatus,
  Pressable,
  StyleProp,
  View,
  ViewStyle,
} from "react-native";

import Typography from "../Typography/Typography";
import Icons from "@/theme/assets/images/svgs/icons";
import { config, IS_ANDROID, IS_IOS } from "@/theme/_config";
import { getRNDateTimePickerStyle } from "./RNDateTimePicker.style";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { useTheme } from "@/theme";
import { AppState } from "react-native";
import useAppStateListener from "@/hooks/useAppStateListener";

type BaseProps = Omit<IOSNativeProps, "conflictingProp"> &
  Omit<AndroidNativeProps, "mode"> & {
    mode?: "date" | "time" | "datetime";
  };

interface RNDateTimePickerProps extends BaseProps {
  label: string;
  pickerProps?: StyleProp<ViewStyle>;
  wrapperStyle?: StyleProp<ViewStyle>;
  error?: string | boolean | undefined;
}

const RNDateTimePicker: FC<RNDateTimePickerProps> = ({
  value = new Date(),
  mode = "datetime",
  display,
  label,
  error,
  pickerProps,
  wrapperStyle,
  onChange,
  ...props
}) => {
  const [androidPickerVisibility, setAndroidPickerVisibility] =
    useState<boolean>(false);
  const [status, setStatus] = useState<AppStateStatus>("active");

  function toggleAndroidPicker() {
    setAndroidPickerVisibility((prev) => !prev);
  }
  const { colors } = useTheme();
  const styles: any = useDynamicStyles(getRNDateTimePickerStyle);
  const pickerStyle =
    IS_IOS && mode === "time" ? { width: ms(80), height: ms(20) } : {};

  useAppStateListener(
    () => {
      setStatus(AppState.currentState);
    },
    () => {
      setStatus(AppState.currentState);
      setAndroidPickerVisibility(false);
    }
  );

  return (
    <View style={[styles.wrapper, wrapperStyle]}>
      {Boolean(label) && (
        <Typography.B1 style={[styles.labelStyle]} numberOfLines={1}>
          {label}
        </Typography.B1>
      )}

      <View style={[pickerStyle, { position: "relative" }]}>
        {/* handling for android time picker */}
        {IS_ANDROID && mode === "time" && (
          <Pressable
            style={styles.androidTimePicker}
            onPress={toggleAndroidPicker}
          >
            <Typography.H2 style={[styles.valueStyle]} numberOfLines={1}>
              {new Date(value).toLocaleTimeString("en", {
                minute: "2-digit",
                hour: "2-digit",
              })}
            </Typography.H2>
          </Pressable>
        )}

        {/* handling for android date picker */}
        {IS_ANDROID && mode === "date" && (
          <Pressable
            style={styles.androidDatePicker}
            onPress={toggleAndroidPicker}
          >
            <Icons.Calendar width={ms(14)} height={ms(14)} />
            <Typography.B1 style={[styles.dateValueStyle]} numberOfLines={1}>
              {new Date(value)?.toLocaleDateString?.("en", {}) || ""}
            </Typography.B1>
          </Pressable>
        )}

        {/* picker rendering for android/ios */}
        {(IS_IOS || androidPickerVisibility) && status === "active" && (
          <DateTimePicker
            value={new Date(value)}
            mode={mode}
            themeVariant="dark"
            display={display}
            textColor="red"
            is24Hour={false}
            headerBackground={colors.primary}
            accentColor={colors.yellow}
            onChange={({ nativeEvent: { timestamp }, type }) => {
              onChange?.(timestamp);
              if (IS_ANDROID && (type === "set" || type === "dismissed")) {
                setAndroidPickerVisibility(false);
                DateTimePickerAndroid.dismiss(mode);
              }
            }}
            style={[styles.dateTimePicker, pickerProps]}
            {...props}
          />
        )}

        <View style={{ position: "absolute" }}>
          {!!error && (
            <Typography.B4 style={styles.errorMessage}>{error}</Typography.B4>
          )}
        </View>
      </View>
    </View>
  );
};

export default RNDateTimePicker;
