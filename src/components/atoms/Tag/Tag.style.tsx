import { ms, ScaledSheet } from "react-native-size-matters";
import { Theme } from "@/types/theme/theme";

export const getTagStyle = (theme: Theme) =>
  ScaledSheet.create({
    gradientStyle: {
      width: ms(62),
      height: ms(28),
      borderRadius: ms(25),
      alignItems: "center",
      justifyContent: "center",
      borderWidth: ms(1),
      borderColor: theme.colors.white,
      overflow: "hidden",
    },
    mainStyle: {
      paddingHorizontal: ms(5),
      height: ms(32),
      borderRadius: ms(8),
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: theme.colors.gray,
    },
  });
