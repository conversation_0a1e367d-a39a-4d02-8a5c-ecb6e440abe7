import { PropsWithChildren, useMemo } from "react";
import { StyleProp, TouchableOpacity, View, ViewStyle } from "react-native";
import LinearGradient from "react-native-linear-gradient";
import { useTheme } from "@/theme";
import { getTagStyle } from "./Tag.style";
import { GradientTagProps, TagStyleKeys } from "@/types/components/atoms/Tag";
import Typography from "../Typography/Typography";
import { config } from "@/theme/_config";

interface TagProps extends PropsWithChildren<GradientTagProps> {
  style?: StyleProp<ViewStyle>;
}

const createTagComponent = (styleKey: TagStyleKeys) => {
  return ({ children, style: customStyle, ...props }: TagProps) => {
    const theme = useTheme();
    const tagStyle = getTagStyle(theme);

    const mergedStyles = useMemo(
      () => [tagStyle[styleKey], customStyle],
      [tagStyle, styleKey, customStyle]
    );

    if (styleKey === "gradientStyle") {
      return (
        <TouchableOpacity {...props}>
          <LinearGradient
            colors={[
              config.colors.tertiary,
              config.colors.secondary,
              config.colors.primary,
            ]}
            start={{ x: 0, y: 0.5 }}
            end={{ x: 1, y: 0.5 }}
            style={mergedStyles as StyleProp<ViewStyle>}
          >
            <Typography.B5>{children}</Typography.B5>
          </LinearGradient>
        </TouchableOpacity>
      );
    }

    return (
      <TouchableOpacity {...props}>
        <View style={mergedStyles as StyleProp<ViewStyle>}>
          <Typography.B5>{children}</Typography.B5>
        </View>
      </TouchableOpacity>
    );
  };
};

const Gradient = createTagComponent("gradientStyle");
const Main = createTagComponent("mainStyle");

export default {
  Gradient,
  Main,
};
