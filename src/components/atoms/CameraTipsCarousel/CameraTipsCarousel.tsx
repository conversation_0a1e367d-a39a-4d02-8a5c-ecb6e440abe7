import { config } from '@/theme/_config';
import React, { useRef, useEffect, useState } from 'react';
import { View, FlatList, Animated } from 'react-native';

import Typography from '../Typography/Typography';
import { styles } from './CameraTipsCarousel.styles';
import LinearGradient from 'react-native-linear-gradient';

// Define the Tip type
type Tip = {
  id: string;
  title: string;
  description: string;
};

const TIPS: Tip[] = [
  {
    id: '1',
    title: 'Take Clear Photos for the Best Results',
    description:
      'Make sure all ingredients are visible — avoid covering them with sauces or toppings.',
  },
  {
    id: '2',
    title: 'Personalized Suggestions',
    description:
      "Ingredients you've logged before are more likely to be recognized, as the AI adapts to your preferences.",
  },
  {
    id: '3',
    title: 'Tailored for PKU',
    description:
      'The AI prioritizes low-protein or special dietary items when relevant.',
  },
  {
    id: '4',
    title: 'Please Note',
    description:
      "It's harder for the AI to estimate dishes with mixed ingredients than those with clearly separated items.",
  },
];

interface CameraTipsCarouselProps {
  style?: any;
}

export const CameraTipsCarousel: React.FC<CameraTipsCarouselProps> = (props) => {
  const scrollX = useRef(new Animated.Value(0)).current;
  const flatListRef = useRef<FlatList<Tip>>(null);
  const currentIndex = useRef(0);
  const [activeIndex, setActiveIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      currentIndex.current = (currentIndex.current + 1) % TIPS.length;
      setActiveIndex(currentIndex.current);
      flatListRef.current?.scrollToIndex({
        index: currentIndex.current,
        animated: true,
      });
    }, 7000);

    return () => clearInterval(interval);
  }, []);

  return (
    <View style={[styles.container, props.style]}>
      <View style={styles.indicatorContainer}>
        {TIPS.map((_, index) => (
          <View
            key={index.toString()}
            style={[
              styles.dot,
              {
                backgroundColor: index === activeIndex ? config.colors.primary : config.colors.gray,
                opacity: index === activeIndex ? 0.7 : 0.9
              }
            ]}
          />
        ))}
      </View>
      <Animated.FlatList
        ref={flatListRef}
        data={TIPS}
        keyExtractor={(item) => item.id}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { x: scrollX } } }],
          { useNativeDriver: false },
        )}
        onMomentumScrollEnd={(event) => {
          const newIndex = Math.round(event.nativeEvent.contentOffset.x / (280 + 96)); // 280 is card width, 96 is total horizontal margin
          setActiveIndex(newIndex);
          currentIndex.current = newIndex;
        }}
        scrollEventThrottle={16}
        renderItem={({ item }) => (
          <LinearGradient
          colors={[
            config.colors.primary,
            '#FF5199',
          ]}
          start={{ x: 0, y: 0.5 }}
          end={{ x: 1, y: 0.5 }}
          style={styles.tipCard}
        >
          <View >
            <Typography.B3 style={styles.tipTitle}>{item.title}</Typography.B3>
            <Typography.B3 style={styles.tipDescription}>{item.description}</Typography.B3>
          </View>
        </LinearGradient>
        )}
      />

    </View>
  );
};
