import { Fonts } from "@/constants";
import { config } from "@/theme/_config";
import { ScaledSheet } from "react-native-size-matters";


export const styles = ScaledSheet.create({
  container: {
    height: 120,
    alignItems: 'center',
    justifyContent: 'center'
  },
  tipCard: {
    width: '280@ms',
    marginHorizontal: '48@ms',
    padding: '16@ms',
    backgroundColor: config.colors.primary,
    borderRadius: '20@ms'
  },
  tipTitle: {
    marginBottom: 6,
    fontFamily: Fonts.RALEWAY_BOLD,
    color: config.colors.white,
  },
  tipDescription: {
    color: config.colors.white,
  },
  indicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: '10@ms',
  },
  dot: {
    height: '10@ms',
    width: '10@ms',
    borderRadius: '5@ms',
    marginHorizontal: '4@ms',
  },
});