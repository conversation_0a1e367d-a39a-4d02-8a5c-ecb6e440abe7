import { CurveType } from "gifted-charts-core";
import { LineChart } from "react-native-gifted-charts";

const AreaChart = () => {
  const data = [{ value: 15 }, { value: 30, showLabel: true }, { value: 26 }, { value: 40 }];
  return (
    <LineChart
      areaChart
      data={data}
      curved
      xAxisColor="#FFF"
      yAxisColor="#FFF"
      yAxisLabelContainerStyle={{ color: '#FFF' }}
      curveType={CurveType.CUBIC}
      hideDataPoints={true}
      hideRules={true}

      startFillColor="rgb(46, 217, 255)"
      startOpacity={0.8}
      endFillColor="rgb(203, 241, 250)"
      endOpacity={0.3}
    />
  );
};

export default AreaChart;