import React from 'react';
import LinearGradient from 'react-native-linear-gradient';

import styles from './TaskBadge.style';

const TaskBadge = (props: { children: string | number | boolean | React.ReactElement<any, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | React.ReactPortal | null | undefined; }) => {
  return (
    <LinearGradient
      colors={['#13689E', '#BE4A79', '#E5005F']}
      start={{ x: 0, y: 0.5 }}
      end={{ x: 1, y: 0.5 }}
      style={styles.gradient}
    >
      {props.children}
    </LinearGradient>
  );
};

export default TaskBadge;
