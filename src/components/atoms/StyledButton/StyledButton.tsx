import { StyleSheet, Text, TouchableOpacity } from "react-native"


const StyledButton = ({ label, onPress }: { label: string, onPress: () => void }) => {
  return (
    <TouchableOpacity style={styles.btn} onPress={onPress}>
      <Text style={styles.btnText}>
        {label}
      </Text>
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  btn: {
    backgroundColor: '#F5F5F5',
    padding: 10,
    borderRadius: 5,
    margin: 10,
    width: '90%',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
  },
  btnText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#000',
  }
})

export default StyledButton;