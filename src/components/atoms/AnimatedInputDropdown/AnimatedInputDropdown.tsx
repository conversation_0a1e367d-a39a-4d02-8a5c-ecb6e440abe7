import React, { FC, useState } from 'react';
import {
  StyleProp,
  ViewStyle,
  View,
  Pressable,
  FlatList
} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { SCREEN_HEIGHT } from '@/theme/_config';
import Typography from '../Typography/Typography';
import { useDynamicStyles } from '@/theme/hooks/useDynamicStyles';
import { AnimatedInputDropdownStyle } from './AnimatedInputDropdown.style';

type AnimatedDropdownProps = {
  menu: object[];
  onSelect: (x: any) => void;
  label: string;
  value: any;
  valueKey?: string;
  displayValue?: string;
  keyName?: string;
  multiSelect?: boolean;
  inputStyle?: StyleProp<ViewStyle>;
  visibility?: boolean;
  wrapperStyle?: StyleProp<ViewStyle>;
  menuHeight?: number;
};

const AnimatedInputDropdown: FC<AnimatedDropdownProps> = ({
  label,
  wrapperStyle,
  value,
  inputStyle,
  onSelect,
  displayValue,
  keyName = 'label',
  multiSelect = false,
  visibility = false,
  menuHeight = SCREEN_HEIGHT * 0.15,
  menu
}) => {
  const [dropdownVisibility, setDropdownVisibility] =
    useState<boolean>(visibility);
  const [selectedId, setSelectedId] = useState<number[]>(value?.[keyName]?.id);

  const [dropdownHeight, setDropdownHeight] = useState(0);
  const styles: any = useDynamicStyles(AnimatedInputDropdownStyle)

  function toggleDropdown() {
    // LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setDropdownVisibility((prev) => !prev);
  }

  function handleDropdownSelect(item) {
    if (multiSelect) {
      setSelectedId([...selectedId, item?.id ?? 0]);
    } else {
      setSelectedId(item?.id ?? 0);
    }

    onSelect({ ...value, [keyName]: item });
  }

  return (
    <View style={[styles.wrapper, wrapperStyle]}>
      <Pressable
        style={[styles.inputContainer, inputStyle]}
        onPress={toggleDropdown}
      >
        {Boolean(label) && (
          <Typography.B2 style={[styles.labelStyle]} numberOfLines={1}>
            {label}
          </Typography.B2>
        )}

        <Typography.B2 style={[styles.valueStyle]} numberOfLines={1}>
          {displayValue || value?.[keyName] || value}
        </Typography.B2>
      </Pressable>

      <View style={{ position: 'relative' }}>
        {dropdownVisibility && (
          <View
            style={[
              styles.menuList,
              inputStyle,
              menuHeight ? { height: menuHeight ?? dropdownHeight } : {}
            ]}
          >
            <ScrollView showsVerticalScrollIndicator={false}>
              <FlatList
                nestedScrollEnabled
                showsVerticalScrollIndicator={false}
                onContentSizeChange={(width, height) =>
                  setDropdownHeight(height)
                }
                data={menu}
                renderItem={({ item }) => (
                  <Pressable
                    style={[
                      styles.menuItem,
                      item.id === selectedId && styles.selectedMenuItem
                    ]}
                    onPress={() => {
                      handleDropdownSelect(item);
                    }}
                  >
                    <Typography.B2 style={styles.menuItemText}>
                      {item.label}
                    </Typography.B2>
                  </Pressable>
                )}
                keyExtractor={(item) => String(item.id)}
                ItemSeparatorComponent={() => <View style={styles.separator} />}
              />
            </ScrollView>
          </View>
        )}
      </View>
    </View>
  );
};

export default AnimatedInputDropdown;
