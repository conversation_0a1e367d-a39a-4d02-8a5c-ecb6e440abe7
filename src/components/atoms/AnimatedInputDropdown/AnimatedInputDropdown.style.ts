import { Fonts } from '@/constants';

import { Theme } from '@/types/theme/theme';
import { Dimensions } from 'react-native';
import { ms, ScaledSheet, verticalScale } from 'react-native-size-matters';

const { width: screenWidth } = Dimensions.get('screen')

export const AnimatedInputDropdownStyle = (theme: Theme) => ScaledSheet.create({
  wrapper: {
    marginVertical: verticalScale(8)
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: ms(16),
    borderBottomColor: theme.colors.mediumGray,
    borderBottomWidth: 1,
  },
  labelStyle: {
    fontFamily: Fonts.RALEWAY_REGULAR,
    color: theme.colors.textPrimary,
    marginBottom: ms(10)
  },
  valueStyle: {
    fontFamily: Fonts.RALEWAY_REGULAR,
    color: theme.colors.textPrimaryYellow,
    marginBottom: ms(10)
  },
  menuList: {
    marginVertical: ms(10),
    flex: 1,
    justifyContent: 'center',
  },
  weeklyMenuList: {
    marginVertical: ms(10),
    flex: 1,
    paddingHorizontal: ms(16),

  },
  weeklyMenuItem: {
    flexDirection: 'row',
    borderBottomColor: theme.colors.mediumGray,
    borderBottomWidth: 1,
    paddingVertical: ms(4),
  },
  menuItem: {
    paddingVertical: ms(4),
    alignSelf: 'center',
    paddingHorizontal: screenWidth / 4
  },
  dailyMenuItem: {
    marginHorizontal: screenWidth / 6,
    paddingHorizontal: ms(10),
    flexDirection: 'row'
  },
  contentContainer: {
    flexDirection: 'row',
    flex: 1,
    paddingVertical: ms(4),
    justifyContent: 'space-between'
  },
  selectedMenuItem: {
    backgroundColor: theme.colors.lightMediumGray,
    borderRadius: ms(8),
  },
  menuItemText: {
    textAlign: "center"
  },
  smallText: {
    paddingVertical: ms(10),
    paddingHorizontal: ms(16),
  }
});
