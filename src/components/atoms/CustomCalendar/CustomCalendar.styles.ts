import { Theme } from "@/types/theme/theme";
import { StyleSheet } from "react-native";
import { ms } from "react-native-size-matters";

const getCustomCalendarStyles = (theme: Theme)=> StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    paddingHorizontal: ms(15),
  },
  calendarContainer: {
    width: "100%",
    backgroundColor: theme.colors.calendarBg,
    borderRadius: ms(10),
    padding: ms(16),
  },
  buttonSeparator: {
    height: 1,
    backgroundColor: "#E0E0E0", // Light gray separator
    marginVertical: ms(10),
  },
  buttonRow: {
    flexDirection: "row",
    gap: ms(20), // Space between buttons
    paddingHorizontal: ms(10),
    justifyContent: "space-between",
    alignItems: "center",
  },
  buttonText: {
    color: theme.colors.calendarDayText, // Black text color
  },
  monthYearHeader: {
    textAlign: "center",
    marginBottom: ms(10), // Add some spacing below the header
    color: "#333", // Match the text color with your theme
  },
  rightButtonRow: {
    flexDirection: "row",
  },
});

export default getCustomCalendarStyles