/** @format */

import React, { useEffect, useRef, useState, forwardRef, useImperativeHandle } from "react";
import { FlatList, TouchableOpacity, View, ViewStyle, ActivityIndicator } from "react-native";
import Icons from "@/theme/assets/images/svgs/icons";
import Common from "@/theme/common.style";
import Typography from "../Typography/Typography";
import CustomTextInput from "../CustomTextInput/CustomTextInput";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import searchInputStyles from "./SearchListInput.style";
import { useAppDispatch } from "@/store";
import { clearSearch, searchFoodByFrequency } from "@/store/slices/dietTrackerSlice";
import { useAnalytics, analyticsEventType } from "@/hooks/useAnalytics";
import { ISearchFoodItem } from "@/types/schemas/dietTracker";
import { useTheme } from "@/theme";

export interface SearchListInputRef {
  reset: () => void;
}

interface SearchListInputProps<T> {
  onAddNew?: () => void;
  placeholder?: string;
  onSelectItem?: (item: ISearchFoodItem) => void;
  showTitle?: boolean;
  customMainContainer?: ViewStyle;
  customMainResultView?: ViewStyle;
  customListView?: ViewStyle;
  searchResults?: any;
  customStyle?: ViewStyle;
  setIsLoading?: (loading: boolean) => void;
  isInnerSearch?: boolean;
  onQueryChange?: (text: string) => void;
}

function SearchListInputInner<T>(
  {
    onAddNew,
    placeholder = "Search...",
    onSelectItem,
    showTitle,
    customMainContainer,
    customMainResultView,
    customListView,
    searchResults,
    customStyle,
    setIsLoading,
    isInnerSearch,
    onQueryChange,
  }: SearchListInputProps<T>,
  ref: React.Ref<SearchListInputRef>
) {
  const styles: any = useDynamicStyles(searchInputStyles);
  const dispatch = useAppDispatch();
  const searchEventFired = useRef(false);
  const { setAnalyticsEvent } = useAnalytics();
  const [query, setQuery] = useState<string>("");
  const { variant, colors } = useTheme();
  const [isActivity, setIsActivity] = useState<boolean>(false)

  useImperativeHandle(ref, () => ({
    reset: () => {
      setQuery("");
      dispatch(clearSearch());
      searchEventFired.current = false;
    },
  }));
  useEffect(() => {
    const delay = 100;
    const minSearchLength = 2;

    const handler = setTimeout(() => {
      if (query.trim().length >= minSearchLength) {
        if (!searchEventFired.current) {
          setAnalyticsEvent(analyticsEventType.custom, {
            event: "diet_search_food_started",
            item_id: "diet_search_food_started",
            action: "User started search for food",
          });
          searchEventFired.current = true;
        }
        setIsLoading?.(true); // ✅ tell parent "loading started"
        setIsActivity(true)

        dispatch(searchFoodByFrequency(query)).then(() => {
          setIsLoading?.(false)
          setIsActivity(false);
        });
      } else {
        dispatch(clearSearch());
        searchEventFired.current = false;
      }
    }, delay);

    return () => clearTimeout(handler);
  }, [query, dispatch]);

  const renderSearchItem = ({ item }: { item: ISearchFoodItem }) => {
    return (
      <TouchableOpacity style={styles.searchItem} onPress={() => onSelectItem?.(item)} activeOpacity={0.7}>
        <View style={styles.searchItemRow}>
          <Typography.B2 style={[styles.itemName, Common.textBold]}>{item?.description}</Typography.B2>
          <Typography.B3 style={styles.itemDetails}>{item?.pheText}</Typography.B3>
        </View>
      </TouchableOpacity>
    );
  };

  const handleTextChange = (text: string) => {
    if (text.trim().length === 0) {
      searchEventFired.current = false;
    }

    setQuery(text);
    if (isInnerSearch) {
      onQueryChange?.(text);
    }
  };
  return (
    <View>
      {showTitle && <Typography.B1>Search</Typography.B1>}
      <View style={[styles.relative_position, customMainContainer]}>
        <CustomTextInput
          placeholder={placeholder}
          value={query}
          onChangeText={handleTextChange}
          icon={<Icons.Search />}
          style={customStyle}
        />

        {query?.length > 2 && !isInnerSearch && (
          <View style={[styles.searchResultsView, customMainResultView]}>
            <FlatList
              data={searchResults}
              keyExtractor={(_, index) => index.toString()}
              renderItem={renderSearchItem}
              keyboardShouldPersistTaps="handled"
              scrollEnabled={true}
              ListEmptyComponent={() => (
                !isInnerSearch && <View style={styles.noItemsFoundView}>
                  {
                    isActivity ?
                      <ActivityIndicator size="large" color={colors.primary} />
                      : !searchResults?.length ?
                        <Typography.B2>No food found</Typography.B2> : null
                  }
                </View>
              )}
              style={[styles.searchResultsList, customListView]}
              nestedScrollEnabled={true}
            />

            {onAddNew && (
              <TouchableOpacity onPress={onAddNew}>
                <View style={styles.searchAddItemView}>
                  <Typography.B2 style={Common.textBold}>Add new</Typography.B2>
                  <Icons.CirclePlus stroke={variant === "dark" ? "white" : "black"} width={15} height={15} />
                </View>
              </TouchableOpacity>
            )}
          </View>
        )}
      </View>
    </View>
  );
}

const SearchListInput = forwardRef(SearchListInputInner) as <T>(
  props: SearchListInputProps<T> & { ref?: React.Ref<SearchListInputRef> }
) => ReturnType<typeof SearchListInputInner>;

export default SearchListInput;
