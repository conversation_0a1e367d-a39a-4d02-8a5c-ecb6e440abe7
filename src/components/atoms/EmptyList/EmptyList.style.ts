/** @format */

import { Fonts } from "@/constants";
import { Theme } from "@/types/theme/theme";
import { ScaledSheet, ms } from "react-native-size-matters";

const getEmptyListStyles = (theme: Theme) =>
  ScaledSheet.create({
    containerStyles: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      alignSelf: "center",
      minHeight: ms(300),
    },
    emptyListText: {
      fontSize: ms(15),
      color: theme.colors.textPrimary,
      marginBottom: ms(12),
      fontFamily: Fonts.RALEWAY_REGULAR,
      lineHeight: ms(20),
      textAlign: "center",
    },
  });

export default getEmptyListStyles;
