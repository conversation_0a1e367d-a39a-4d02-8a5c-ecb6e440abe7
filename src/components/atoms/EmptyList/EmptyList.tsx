/** @format */

import { View, Text } from "react-native";
import React from "react";
import Typography from "../Typography/Typography";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import getEmptyListStyles from "./EmptyList.style";
import { ViewStyle } from "react-native-size-matters";

const EmptyList = ({ text, style }: { text: string, style: ViewStyle}) => {
  const styles: any = useDynamicStyles(getEmptyListStyles);

  return (
    <View style={{...styles.containerStyles, ...style}}>
      <Typography.H4 style={styles.emptyListText}>{text}</Typography.H4>
    </View>
  );
};

export default EmptyList;
