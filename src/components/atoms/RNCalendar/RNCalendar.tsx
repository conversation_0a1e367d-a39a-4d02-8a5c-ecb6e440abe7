import React, { FC, useEffect, useState } from 'react';
import { Calendar, CalendarProps, DateData } from 'react-native-calendars';
import { ImageBackground, Pressable, StyleProp, StyleSheet, View, ViewStyle } from 'react-native';
import { getRNCalendarStyles } from './RNCalendar.styles';
import Modal from 'react-native-modal';
import Typography from '../Typography/Typography';
import Icons from '@/theme/assets/images/svgs/icons';
import { ms } from 'react-native-size-matters';
import dayjs from 'dayjs';
import { useDynamicStyles } from '@/theme/hooks/useDynamicStyles';
import { useTheme } from '@/theme';
import icons from '@/theme/assets/images/svgs/icons';
import { BlurView } from '@react-native-community/blur';

interface RNCalendarProps extends CalendarProps {
  wrapperStyle?: StyleProp<ViewStyle>;
  label: string;
  value: Date;
  error?: string | boolean | undefined;
  disableBackdrop?: boolean;
  onSelect: (x: any) => void;
}

const RNCalendar: FC<RNCalendarProps> = ({
  wrapperStyle,
  value,
  label,
  onSelect,
  error,
  disableBackdrop = true,
  minDate = new Date(),
  ...rest
}) => {
  const [visibility, setVisibility] = useState<boolean>(false);

  const styles: any = useDynamicStyles(getRNCalendarStyles);

  const { colors } = useTheme();

  const [selectedDate, setSelectedDate] = useState(
    value ? dayjs(new Date(value)).format('YYYY-MM-DD') : ''
  );

  useEffect(() => {
    if (value) {
      setSelectedDate(dayjs(new Date(value)).format('YYYY-MM-DD'));
    }
  }, [visibility, value]);

  function toggleCalendar() {
    setVisibility((prev) => !prev);
  }

  function handleDayPress({ dateString }: DateData) {
    setSelectedDate(dayjs(new Date(dateString)).format('YYYY-MM-DD'));
  }

  const markedDates = {
    [selectedDate]: { selected: true }
  };

  return (
    <View style={[styles.wrapper, wrapperStyle]}>
      {Boolean(label) && (
        <Typography.B1 style={styles.labelStyle} numberOfLines={1}>
          {label}
        </Typography.B1>
      )}

      <View>
        <Pressable style={styles.androidDatePicker} onPress={toggleCalendar}>
          <Icons.Calendar width={ms(14)} height={ms(14)} fill={colors.textPrimary} />
          <Typography.B1 style={[styles.dateValueStyle]} numberOfLines={1}>
            {value
              ? new Date(value)?.toLocaleDateString?.('en', {}) || ''
              : 'Add Date if Required'}
          </Typography.B1>
        </Pressable>
      </View>

      {!!error && (
        <Typography.B4 style={styles.errorMessage}>{error}</Typography.B4>
      )}

      <Modal
        isVisible={visibility}
        onBackdropPress={() => !disableBackdrop && toggleCalendar()}
      >
        <>
        {/* <BlurView style={StyleSheet.absoluteFillObject} blurType="dark" blurAmount={4} /> */}
        <View style={styles.calendarContainer}>
          <Calendar
            initialDate={selectedDate ?? new Date()}
            current={selectedDate ?? new Date()}
            minDate={dayjs(new Date(minDate)).format('YYYY-MM-DD')}
            onDayPress={handleDayPress}
            theme={styles.calendarTheme}
            style={styles.calendarView}
            markedDates={markedDates}
            renderHeader={(date: Date) => {
              const formattedDate = new Date(date).toLocaleDateString('en-US', {
                month: 'long',
                year: 'numeric'
              });
              return (
                <Typography.H4 style={styles.monthYearHeader}>
                  {formattedDate}
                </Typography.H4>
              );
            }}
            {...rest}
          />

          <View style={styles.footerContainer}>
            <Pressable
              onPress={() => {
                setSelectedDate(''); // Reset selected date
                onSelect(''); // Notify parent form that date is cleared
              }}
            >
              <Typography.B3 style={styles.button}>Clear</Typography.B3>
            </Pressable>

            <View style={styles.rightContainer}>
              <Pressable onPress={toggleCalendar}>
                <Typography.B3 style={styles.button}>Cancel</Typography.B3>
              </Pressable>

              <Pressable
                onPress={() => {
                  toggleCalendar();
                  onSelect(selectedDate ? new Date(selectedDate) : '');
                }}
              >
                <Typography.B3 style={styles.button}>OK</Typography.B3>
              </Pressable>
            </View>
          </View>
        </View>
        </>
      </Modal>
    </View>
  );
};

export default RNCalendar;
