import { Fonts } from '@/constants';
import { Theme } from '@/types/theme/theme';
import {
  ms,
  scale,
  ScaledSheet,
  verticalScale
} from 'react-native-size-matters';

export const getRNCalendarStyles = (theme: Theme) => ScaledSheet.create({
  wrapper: {
    marginVertical: verticalScale(8)
  },
  calendarContainer: {
    backgroundColor: theme.colors.calendarBg,
    borderRadius: ms(8),
    paddingBottom: ms(16)
  },
  calendarView: {
    backgroundColor: theme.colors.calendarBg,
    borderRadius: ms(8),
    padding: ms(10)
  },
  monthYearHeader: {
    textAlign: 'center',
    marginBottom: ms(10),
    color: theme.colors.black
  },
  calendarTheme: {
    backgroundColor: theme.colors.calendarBg,
    calendarBackground: theme.colors.calendarBg,
    selectedDayBackgroundColor: theme.colors.primary,
    selectedDayTextColor: theme.colors.white,
    todayTextColor: theme.colors.primary,
    arrowColor: theme.colors.primary,
    dayTextColor: theme.colors.black,
    textDisabledColor: theme.colors.calendarDisableText,
    textDayFontFamily: Fonts.RALEWAY_REGULAR,
    textMonthFontFamily: Fonts.RALEWAY_BOLD,
    textDayFontWeight: '400',
    textMonthFontWeight: '700',
    textSectionTitleColor: theme.colors.calendarDayText,
  },
  labelStyle: {
    color: theme.colors.textPrimary,
    lineHeight: ms(20),
    marginBottom: ms(10)
  },
  dateTimePicker: {
    borderColor: theme.colors.yellow,
    borderRadius: ms(10),
    borderWidth: ms(2),
    color: theme.colors.white,
    backgroundColor: 'transparent',
    fontFamily: Fonts.RALEWAY_LIGHT,
    fontSize: ms(22),
    alignSelf: 'flex-start'
  },
  androidDatePicker: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center'
  },
  calendarIcon: {
    width: ms(14),
    height: ms(14)
  },
  androidTimePicker: {
    borderColor: theme.colors.yellow,
    borderRadius: 8,
    borderWidth: 1,
    height: ms(34),
    justifyContent: 'center',
    alignItems: 'center'
  },
  valueStyle: {
    fontFamily: Fonts.RALEWAY_BOLD,
    lineHeight: ms(22),
    fontSize: ms(20),
    color: theme.colors.yellow
  },
  dateValueStyle: {
    flex: 1,
    justifyContent: 'space-between',
    color: theme.colors.textPrimaryYellow,
    marginLeft: ms(10)
  },
  footerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: ms(12)
  },
  button: {
    fontFamily: Fonts.RALEWAY_BOLD,
    color: theme.colors.calendarDayText,
    marginHorizontal: ms(5)
  },
  rightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center'
  },
  errorMessage: {
    position: 'absolute',
    left: 0,
    top: ms(45),
    fontFamily: Fonts.RALEWAY_REGULAR,
    color: theme.colors.red,
    marginTop: verticalScale(4),
    marginBottom: scale(-16)
  }
});
