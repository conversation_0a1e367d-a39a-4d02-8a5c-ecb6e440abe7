import React, { useEffect, useState } from "react";
import { Animated, Image, Platform, View } from "react-native";
import { BlurView } from "@react-native-community/blur";
import Icons from "@/theme/assets/images/svgs/icons";
import LottieView from "lottie-react-native";

import styles from "./Loading.style";
import Typography from "../Typography/Typography";
import analyzingMealAnimation from '../../../theme/assets/lotties/analyzing_meal.lottie';
import matchingMealAnimation from '../../../theme/assets/lotties/waiting_match_food.lottie';
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { IS_IOS } from "@/theme/_config";



type ScanMealProps = {
  photoUri: string | null;
};

type LoadingComponent = React.FC & {
  ScanMeal: React.FC<ScanMealProps>;
};

const Loading: LoadingComponent = () => {
  return (
    <Animated.View style={styles.container}>
      <BlurView style={styles.blurView} blurType="dark" blurAmount={10} />
      <View style={styles.loaderContainer}>
        <Icons.LogoIcon />
        <Typography.B1 style={styles.text}>Loading...</Typography.B1>
      </View>
    </Animated.View>
  );
};

Loading.ScanMeal = ({ photoUri }: ScanMealProps) => {
  const orientaion = useSelector((state: RootState) => state.dietTracker.orientaion);
  if (!photoUri) return null;
  const photoSrc = { uri: `file://${photoUri}` }

  const lottiesAndlabels = {
    analyzing_meal: {
      lottie: analyzingMealAnimation,
      label: `Hang on Tight \n We are analyzing your meal`,
    },
    matching_meal: {
      lottie: matchingMealAnimation,
      label: `Almost There! \n Loading nutritional information`,
    },
  };

  const [currentLottie, setCurrentLottie] = useState(lottiesAndlabels.analyzing_meal);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setCurrentLottie(lottiesAndlabels.matching_meal);
    }, 6500);

    return () => clearTimeout(timeoutId);
  }, []);

const resizeMode =
  IS_IOS
    ? orientaion === "portrait" || orientaion === 'landscape'
      ? "cover"
      : "contain"
    : orientaion === "portrait"
    ? "contain"
    : "cover";
  return (
    <View style={styles.container}>
      <Image source={photoSrc} resizeMode={resizeMode} style={styles.srcFoodImage} />
      <View style={styles.scanMealloaderContainer}>
        <LottieView
          source={currentLottie.lottie}
          style={styles.lottieView}
          autoPlay
          loop
        />
        <Typography.B2 style={styles.scanMealText}>{currentLottie?.label}</Typography.B2>
      </View>
    </View>

  );
};

export default Loading;
