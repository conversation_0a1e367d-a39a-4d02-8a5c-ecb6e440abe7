import { Fonts } from "@/constants";
import { config } from "@/theme/_config";
import {
  ms,
  scale,
  ScaledSheet,
  verticalScale,
} from "react-native-size-matters";

export const RNCheckboxStyle = ScaledSheet.create({
  wrapper: {
    marginVertical: verticalScale(8),
  },
  rowContainer: {
    flexDirection: "row",
    alignItems: "center",
    width: '100%',
    overflow:'hidden'
  },
  labelStyle: {
    color: config.colors.textPrimary,
    lineHeight: ms(20),
    marginBottom: ms(5),
  },
  labelStyle2: {
    flex:1,
    color: config.colors.textPrimary,
    lineHeight: ms(20),
    marginBottom: ms(5),
    flexWrap:'wrap',
  },
  errorMessage: {
    position: "absolute",
    left: 0,
    top: ms(45),
    fontFamily: Fonts.RALEWAY_REGULAR,
    color: config.backgrounds.red,
    marginTop: verticalScale(4),
    marginBottom: scale(-16),
  },
  checkbox: {
    width: scale(24),
    height: scale(24),
    marginRight: ms(5),
  },
});
