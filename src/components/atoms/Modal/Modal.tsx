import React, { PropsWithChildren, useMemo } from "react";
import {
  Modal as RNModal,
  StyleProp,
  TouchableOpacity,
  View,
  ViewStyle,
} from "react-native";
import { useTheme } from "@/theme";
import { getModalStyle } from "./Modal.style";
import { BlurView } from "@react-native-community/blur";

interface ModalProps extends PropsWithChildren {
  isVisible: boolean;
  onClose?: () => void;
  style?: StyleProp<ViewStyle>;
  hasNoOverlay?: boolean;
  hasNoBackground?: boolean;
  enableBlur?: boolean;
}

const Modal: React.FC<ModalProps> = ({
  isVisible: visible,
  onClose,
  children,
  style,
  hasNoOverlay = false,
  hasNoBackground = false,
  enableBlur = false,
}) => {
  const theme = useTheme();
  const { variant } = useTheme();
  const modalStyle = getModalStyle(theme);

  const mergedStyles = useMemo(
    () => [
      modalStyle.container,
      !hasNoBackground && modalStyle.background,
      style,
    ],
    [modalStyle, style]
  );

  return (
    <RNModal
      transparent
      visible={visible}
      animationType="fade"
      onRequestClose={onClose}
    >
      {enableBlur && (
        <BlurView
          style={{
            position: "absolute",
            width: "100%",
            height: "100%"
          }}
          blurType={variant === "dark" ? "dark" : "light"}
          blurAmount={8}
          reducedTransparencyFallbackColor="black"
        />
      )}
      <View style={!hasNoOverlay && modalStyle.overlay}>
        <TouchableOpacity
          style={modalStyle.background}
          activeOpacity={1}
          onPress={onClose}
        />
        <View style={mergedStyles}>{children}</View>
      </View>
    </RNModal>
  );
};

export default Modal;
