import { ms, ScaledSheet } from "react-native-size-matters";
import { Theme } from "@/types/theme/theme";
import { config } from "@/theme/_config";

export const getModalStyle = (theme: Theme) =>
  ScaledSheet.create({
    overlay: {
      flex: 1,
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      justifyContent: "center",
      alignItems: "center",
    },
    noOverlay: {
      flex: 1,
      backgroundColor: "rgba(0, 0, 0, 0.0)",
      justifyContent: "center",
      alignItems: "center",
    },
    background: {
      backgroundColor: theme.colors.tile_bg,
    },
    container: {
      width: "90%",
      borderRadius: ms(12),
    },
  });
