import React, { useMemo, useState } from "react";
import {
  StyleProp,
  TextInput,
  TextStyle,
  TextInputProps as RNTextInputProps,
  View,
  ViewStyle,
  TouchableOpacity,
} from "react-native";
import { useTheme } from "@/theme";
import { getTextInputStyle } from "./CustomTextInput.style";
import Typography from "../Typography/Typography";
import { config } from "@/theme/_config";

interface CustomTextInputProps extends RNTextInputProps {
  style?: StyleProp<ViewStyle>;
  inputStyle?: StyleProp<TextStyle>;
  disabled?: boolean;
  errorMessage?: string;
  unit?: string; // Unit to display inside the input
  icon?: React.ReactNode; // Icon to display inside the input
  onIconPress?: () => void; // Function to handle icon press
}

const CustomTextInput: React.FC<CustomTextInputProps> = ({
  style: customStyle,
  disabled = false,
  errorMessage,
  unit,
  icon,
  onIconPress,
  onFocus,
  onBlur,
  inputStyle: textInputStyle,
  ...props
}) => {
  const theme = useTheme();
  const inputStyle = getTextInputStyle(theme);

  const [isFocused, setIsFocused] = useState(false);

  const handleFocus = (e: any) => {
    setIsFocused(true);
    onFocus?.(e);
  };

  const handleBlur = (e: any) => {
    setIsFocused(false);
    onBlur?.(e);
  };

  return (
    <>
      <View
        style={[
          inputStyle.inputWrapper,
          isFocused && !disabled && inputStyle.focusStyle,
          disabled && inputStyle.disabledStyle,
          errorMessage ? inputStyle.errorView : {},
          customStyle,
        ]}
      >
        <TextInput
          style={[inputStyle.enabledStyle, textInputStyle]}
          editable={!disabled}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholderTextColor={theme.colors.mediumGray}
          {...props}
        />

        {/* Unit or Pressable Icon */}
        {unit ? (
          <Typography.B1
            style={[
              inputStyle.unitText,
              {color: theme.colors.textPrimaryYellow},
              disabled && { color: theme.colors.darkGray },
            ]}
          >
            {unit}
          </Typography.B1>
        ) : icon ? (
          <TouchableOpacity
            hitSlop={{ top: 5, bottom: 5, left: 20, right: 5 }}
            onPress={onIconPress}
            activeOpacity={0.7}
          >
            <View>{icon}</View>
          </TouchableOpacity>
        ) : null}
      </View>

      {/* Error Message */}
      {errorMessage ? (
        <Typography.B1 style={inputStyle.errorText}>
          {errorMessage}
        </Typography.B1>
      ) : null}
    </>
  );
};

export default CustomTextInput;
