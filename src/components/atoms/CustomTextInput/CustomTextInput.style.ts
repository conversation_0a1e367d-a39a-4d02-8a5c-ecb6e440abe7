// CustomTextInput.style.ts
import { ms, ScaledSheet, vs } from "react-native-size-matters";
import { Theme } from "@/types/theme/theme";
import { Fonts } from "@/constants";
import { config } from "@/theme/_config";

export const getTextInputStyle = (theme: Theme) =>
  ScaledSheet.create({
    inputWrapper: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: ms(10),
      borderRadius: ms(8),
      borderColor: theme.colors.mediumGray,
      borderWidth: ms(1),
      marginVertical: ms(8),
      height: ms(45),
    },
    errorView:{
      marginVertical: ms(0),
      marginTop: ms(8),
      marginBottom: ms(2),
    },
    enabledStyle: {
      flex: 1,
      fontSize: ms(14),
      color: theme.colors.textPrimary,
      fontFamily: Fonts.RALEWAY_REGULAR,
    },
    focusStyle: {
      borderColor: theme.colors.yellow,
    },
    disabledStyle: {
      color: theme.colors.darkGray,
      borderColor: theme.colors.borderDisable,
      borderWidth: ms(1),
    },
    unitText: {
      color: theme.colors.yellow,
      marginLeft: ms(10),
      fontSize: ms(14),
    },
    errorText: {
      color: config.colors.red,
      fontSize: ms(12),
      marginBottom: ms(8),
    },
  });
