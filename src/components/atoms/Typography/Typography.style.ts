import { Fonts } from "@/constants";
import { Theme } from "@/types/theme/theme";
import { RFValue } from "react-native-responsive-fontsize";
import { ms, ScaledSheet } from "react-native-size-matters";

export const getTypography = (theme: Theme) =>
  ScaledSheet.create({
    h0: {
      // lineHeight: "40@vs",
      fontSize: RFValue(32),
      color: theme.colors.textPrimary,
      fontFamily: Fonts.RALEWAY_REGULAR,
    },
    h1: {
      // lineHeight: "26@vs",
      fontSize: RFValue(26),
      color: theme.colors.textPrimary,
      fontFamily: Fonts.RALEWAY_REGULAR,
    },
    h2: {
      // lineHeight: ms(20),
      fontSize: RFValue(20),
      color: theme.colors.textPrimary,
      fontFamily: Fonts.RALEWAY_REGULAR,
    },
    h3: {
      lineHeight: "20@vs",
      fontSize: ms(18),
      color: theme.colors.textPrimary,
      fontFamily: Fonts.RALEWAY_REGULAR,
    },
    h4: {
      lineHeight: "21@ms",
      fontSize: ms(16),
      color: theme.colors.textPrimary,
      fontFamily: Fonts.RALEWAY_REGULAR,
    },
    h5: {
      lineHeight: "21@ms",
      fontSize: ms(15),
      color: theme.colors.textPrimary,
      fontFamily: Fonts.RALEWAY_REGULAR,
    },
    b1: {
      lineHeight: ms(16),
      fontSize: ms(15),
      color: theme.colors.textPrimary,
      fontFamily: Fonts.RALEWAY_REGULAR,
    },
    b2: {
      lineHeight: ms(18),
      fontSize: ms(13),
      color: theme.colors.textPrimary,
      fontFamily: Fonts.RALEWAY_REGULAR,
    },
    b3: {
      lineHeight: ms(13),
      fontSize: ms(11),
      color: theme.colors.textPrimary,
      fontFamily: Fonts.RALEWAY_REGULAR,
    },
    b4: {
      lineHeight: ms(12),
      fontSize: ms(10),
      color: theme.colors.textPrimary,
      fontFamily: Fonts.RALEWAY_REGULAR,
    },
    b5: {
      lineHeight: ms(12),
      fontSize: ms(10),
      color: theme.colors.textPrimary,
      fontFamily: Fonts.RALEWAY_REGULAR,
    },
  });
