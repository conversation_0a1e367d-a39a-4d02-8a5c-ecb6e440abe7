import React, { <PERSON> } from 'react';
import { Pressable, Text, View } from 'react-native';
import Toast, { ToastConfigParams } from 'react-native-toast-message';
import { ToastBaseStyle as styles } from './ToastBase.styles';
import Icons from '@/theme/assets/images/svgs/icons';

interface ToastNotificationProps extends ToastConfigParams<any> {
  isFormula: boolean;
  medicineType: string;
  inTakeTime: string;
}

const ToastNotification: FC<ToastNotificationProps> = (props) => {
  const {
    props: { isFormula }
  } = props;

  return (
    <View style={[styles.parentContainer]}>
      <View style={[styles.container]}>
        <View style={styles.leftIconStyle}>
          <Icons.BellIcon />
        </View>
        <View style={styles.contentContainer}>
          <Text
            style={styles.heading}
          >{`${isFormula === '1' ? 'Formula' : 'Medication'} Reminder!`}</Text>
          {/* {Boolean(text2) && (
            <Text style={styles.subHeading} numberOfLines={2}>
              {text2}
            </Text>
          )} */}
        </View>
        <Pressable style={styles.rightIconStyle} onPress={() => Toast.hide()}>
          <Icons.CloseWhite />
        </Pressable>
      </View>
    </View>
  );
};

export default ToastNotification;
