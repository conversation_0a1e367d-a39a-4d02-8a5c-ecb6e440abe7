import { Fonts } from '@/constants';
import { config } from '@/theme/_config';
import { ms, scale, ScaledSheet } from 'react-native-size-matters';

export const ToastBaseStyle = ScaledSheet.create({
  parentContainer: {
    borderRadius: scale(15),
    borderWidth: ms(0.5),
    borderColor: config.backgrounds.primary,
    width: '85%',
    paddingLeft: scale(8),
    backgroundColor: config.backgrounds.primary,
    flexDirection: 'row',
    alignItems: 'center',
    minHeight: ms(73)
  },
  container: {
    padding: scale(10),
    borderRadius: scale(15),
    width: '100%',
    backgroundColor: config.backgrounds.textPrimary,
    flexDirection: 'row',
    alignItems: 'center',
    minHeight: ms(73)
  },
  leftIconStyle: {
    marginRight: ms(16)
  },
  rightIconStyle: {
    marginLeft: ms(16)
  },
  contentContainer: {
    flex: 1
  },
  heading: {
    fontFamily: Fonts.RALEWAY_BOLD,
    color: config.backgrounds.white,
    marginBottom: ms(2),
    fontSize: ms(13)
  },
  subHeading: {
    fontFamily: Fonts.RALEWAY_MEDIUM,
    color: config.backgrounds.white,
    fontSize: ms(13)
  }
});
