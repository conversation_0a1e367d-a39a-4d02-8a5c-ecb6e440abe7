import toastService from '@/shared/toast.service';
import { config } from '@/theme/_config';
import React, { FC } from 'react';
import { Pressable, Text, View } from 'react-native';
import Toast, { ToastConfigParams } from 'react-native-toast-message';
import { ToastBaseStyle as styles } from './ToastBase.styles';
import Icons from '@/theme/assets/images/svgs/icons';

const ToastBase: FC<ToastConfigParams<any>> = (props) => {
  const { messageType } = toastService;
  const { text1, text2, type } = props;

  const Type:
    | { backgroundColor: string; iconColor: string; icon: string }
    | undefined = {
    [messageType.SUCCESS]: {
      accentColor: config.backgrounds.green,
      iconColor: config.backgrounds.green,
      icon: 'checkmark-circle-sharp'
    },
    [messageType.INFO]: {
      accentColor: config.backgrounds.yellow,
      iconColor: config.backgrounds.yellow,
      icon: 'information-circle-sharp'
    },
    [messageType.ERROR]: {
      accentColor: config.backgrounds.red,
      iconColor: config.backgrounds.red,
      icon: 'alert-circle-sharp'
    }
  }[type as string];

  return (
    <View
      style={[styles.parentContainer, { backgroundColor: Type?.accentColor }]}
    >
      <View style={[styles.container]}>
        <View style={styles.leftIconStyle}>
          <Icons.Check />
        </View>
        <View style={styles.contentContainer}>
          <Text style={styles.heading}>{text1}</Text>
          {Boolean(text2) && (
            <Text style={styles.subHeading} numberOfLines={2}>
              {text2}
            </Text>
          )}
        </View>
        <Pressable style={styles.rightIconStyle} onPress={() => Toast.hide()}>
          <Icons.CloseWhite />
        </Pressable>
      </View>
    </View>
  );
};

export default ToastBase;
