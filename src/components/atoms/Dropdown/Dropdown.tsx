import React, { useState, useRef, useEffect, useCallback } from "react";
import {
  View,
  Modal,
  FlatList,
  Keyboard,
  AppState,
  StyleProp,
  ViewStyle,
  TextStyle,
  ScrollView,
  Dimensions,
  TouchableOpacity,
  LayoutChangeEvent,
  TouchableWithoutFeedback,
} from "react-native";
import { ms } from "react-native-size-matters";

import { useTheme } from "@/theme";
import getDropdownStyles from "./Dropdown.styles";
import Typography from "../Typography/Typography";
import { IS_ANDROID, IS_IOS } from "@/theme/_config";
import Icons from "@/theme/assets/images/svgs/icons";
import { DateRangeLabel } from "@/types/schemas/labs";
import CustomTextInput from "../CustomTextInput/CustomTextInput";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";

interface DropdownProps<T> {
  isVisible: boolean;
  data: T[];
  selectedValue: string;
  setSelectedValue: (selected: string) => void;
  onToggle: () => void;
  dropdownStyle?: StyleProp<ViewStyle>;
  buttonStyle?: StyleProp<ViewStyle>;
  optionStyle?: StyleProp<ViewStyle>;
  dropdownOptionTextStyle?: StyleProp<TextStyle>;
  placeHolder?: string;
  chevronColor?: string;
  fullHeight?: boolean;
  disabled?: boolean;
  optionTextStyle?: StyleProp<TextStyle>;
  buttonContent?: React.ReactNode;
  searchable?: boolean;
  searchPlaceholder?: string;
  isCurrent?: boolean;
  isTimezone?: boolean;
  useFlatList?: boolean;
}

interface SearchableInputProps {
  searchable?: boolean;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  searchPlaceholder?: string;
}

const { height: screenHeight } = Dimensions.get("window");

const SearchableInput = ({
  searchable = false,
  searchQuery,
  setSearchQuery,
  searchPlaceholder,
}: SearchableInputProps) => {
  if (!searchable) return null;
  return (
    <View style={{padding: ms(8)}}>
      <CustomTextInput
        value={searchQuery}
        icon={<Icons.Search />}
        onChangeText={setSearchQuery}
        placeholder={searchPlaceholder || "Search for timezones..."}
      />
    </View>
  );
};

const Dropdown = <T extends { id: string | number; text: string }>({
  isVisible,
  data,
  selectedValue: selectedData,
  setSelectedValue,
  onToggle,
  dropdownStyle,
  buttonStyle,
  optionStyle,
  placeHolder = "Select an option",
  fullHeight,
  dropdownOptionTextStyle,
  disabled,
  isCurrent,
  chevronColor,
  optionTextStyle,
  buttonContent,
  searchable,
  isTimezone,
  searchPlaceholder,
  useFlatList = false,
}: DropdownProps<T>) => {
  const dropdownRef = useRef<TouchableOpacity>(null);
  const [dropdownPosition, setDropdownPosition] = useState({
    x: -10,
    y: 0,
    width: 0,
    dropdownHeight: ms(150),
  });

  const { colors } = useTheme();
  const appStateRef = useRef(AppState.currentState);
  const styles: any = useDynamicStyles(getDropdownStyles);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [searchQuery, setSearchQuery] = useState<string>("");

  useEffect(() => {
    // Handle app state changes
    const subscription = AppState.addEventListener("change", (nextAppState) => {
      if ((nextAppState === "background" || nextAppState === "inactive") && isVisible) {
        onToggle();
      }

      appStateRef.current = nextAppState;
    });

    return () => {
      subscription.remove();
    };
  }, [isVisible, onToggle]);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener("keyboardDidShow", (event) => {
      setKeyboardHeight(event.endCoordinates.height);
    });
    const keyboardDidHideListener = Keyboard.addListener("keyboardDidHide", () => {
      setKeyboardHeight(0);
    });
    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  const updateDropdownPosition = (px: number, py: number, width: number, height: number) => {
    const safeY = py + height;
    const maxDropdownHeight = screenHeight - safeY - keyboardHeight - 20;

    setDropdownPosition({
      x: px,
      y: safeY,
      width,
      dropdownHeight: Math.max(ms(150), maxDropdownHeight),
    });
  };

  const checkAndMeasureDropdown = () => {
    dropdownRef.current?.measure((fx, fy, width, height, px, py) => {
      updateDropdownPosition(px, py, width, height);
    });
  };

  useEffect(() => {
    if (isVisible) {
      setTimeout(() => {
        checkAndMeasureDropdown();
      }, 10);
    } else {
      setSearchQuery("");
    }
  }, [isVisible, keyboardHeight]);

  const handleLayout = (event: LayoutChangeEvent) => {
    if (isVisible) {
      checkAndMeasureDropdown();
    }
  };

  // const filteredData =
  //   selectedData === DateRangeLabel.Custom ? data : data.filter((item) => item.text !== selectedData);
  const filteredData =
    selectedData === DateRangeLabel.Custom
      ? data.filter((item) => item.text.toLowerCase().includes(searchQuery.toLowerCase()))
      : data.filter(
        (item) => item.text.toLowerCase().includes(searchQuery.toLowerCase()) && item.text !== selectedData
      );

  const sharedStyle = useCallback((index: number) => ([
    styles.dropdownOption,
    index !== filteredData.length - 1 && {
      borderBottomColor: colors.white,
      borderBottomWidth: 0.5,
    },
    optionStyle,
  ]), [filteredData.length, colors, optionStyle])

  const itemSelectPress = useCallback((item: T) => () => {
    setSelectedValue(item.text);
    onToggle();
  }, [setSelectedValue, onToggle]);

  const renderFlatList = useCallback((data) => {
    return (
      <FlatList
        style={IS_IOS ? { maxHeight: dropdownPosition.dropdownHeight } : {}}
        persistentScrollbar
        showsVerticalScrollIndicator
        indicatorStyle="white"
        data={data}
        renderItem={({ item, index }) => (
          <TouchableOpacity
            key={`${item.id}-${index}`}
            style={sharedStyle(index)}
            onPress={itemSelectPress(item)}
          >
            <Typography.B2 style={[styles.dropdownOptionText, optionTextStyle]}>
              {item.text}
            </Typography.B2>
          </TouchableOpacity>
        )}
        initialNumToRender={10}
        maxToRenderPerBatch={40}
        keyExtractor={(item) => String(item.id)}
      />
    )
  }, [IS_IOS, dropdownPosition.dropdownHeight, sharedStyle, itemSelectPress, optionTextStyle]);

  return (
    <>
      {/* Native Modal Dropdown */}

      {IS_ANDROID && isTimezone ? (
        <View style={styles.relativeContainer}>
          {/* Dropdown Button */}
          {isVisible && (
            <TouchableOpacity
              activeOpacity={1}
              style={styles.androidOverlay}
              onPress={() => {
                Keyboard.dismiss();
                onToggle(); // Close the dropdown
              }}
            />
          )}
          <TouchableOpacity
            ref={dropdownRef}
            style={[styles.dropdownContainer, buttonStyle]}
            onPress={onToggle}
            disabled={disabled}
          >
            {buttonContent ? (
              buttonContent
            ) : (
              <Typography.B2
                numberOfLines={1}
                ellipsizeMode="tail"
                style={[styles.dropdownOptionText, dropdownOptionTextStyle]}
              >
                {selectedData || placeHolder}
              </Typography.B2>
            )}
            {!isCurrent && data.length > 1 && (
              <Icons.Chevron
                color={chevronColor || colors.textPrimary}
                height={12}
                width={12}
                style={isVisible ? styles.invertIcon : {}}
              />
            )}
          </TouchableOpacity>

          {/* Inline Dropdown Options */}
          {isVisible && (
            <View
              pointerEvents="auto"
              style={[
                styles.dropdownOptions,
                dropdownStyle,
                styles.androidDropdownOptions,
                {
                  maxHeight: fullHeight ? "100%" : searchable ? ms(200) : ms(95),
                  overflow: "hidden",
                },
              ]}
            >
              {/* Searchable Input */}

              <SearchableInput
                searchable={searchable}
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
                searchPlaceholder={searchPlaceholder}
              />

              {/* Options List */}

              {useFlatList ?
                renderFlatList(filteredData)
                :
                <ScrollView
                  persistentScrollbar
                  showsVerticalScrollIndicator
                  indicatorStyle="white">
                  {filteredData.map((item, index) => (
                    <TouchableOpacity
                      key={`${item.id}-${index}`}
                      style={sharedStyle(index)}
                      onPress={itemSelectPress(item)}
                    >
                      <Typography.B2 style={[styles.dropdownOptionText, optionTextStyle]}>{item.text}</Typography.B2>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              }
            </View>
          )}
        </View>
      ) : (
        <View onLayout={handleLayout}>
          {/* Dropdown Button */}

          <TouchableOpacity
            ref={dropdownRef}
            style={[styles.dropdownContainer, buttonStyle]}
            onPress={() => {
              checkAndMeasureDropdown();
              onToggle();
            }}
            disabled={disabled}
          >
            {buttonContent ? (
              buttonContent
            ) : (
              <Typography.B2
                numberOfLines={1}
                ellipsizeMode="tail"
                style={[styles.dropdownOptionText, dropdownOptionTextStyle]}
              >
                {selectedData || placeHolder}
              </Typography.B2>
            )}
            {!isCurrent && data.length > 1 && (
              <Icons.Chevron
                color={chevronColor || colors.textPrimary}
                height={12}
                width={12}
                style={isVisible ? styles.invertIcon : {}}
              />
            )}
          </TouchableOpacity>
          <Modal visible={isVisible} transparent animationType="none" onRequestClose={onToggle}>
            {/* Dismiss dropdown when tapping outside */}
            <TouchableWithoutFeedback onPress={onToggle}>
              <View style={{ flex: 1 }} />
            </TouchableWithoutFeedback>

            {/* Dropdown Options */}
            <View
              style={[
                styles.dropdownOptions,
                dropdownStyle,
                styles.modalDropdownOptions,
                {
                  top: dropdownPosition.y,
                  left: dropdownPosition.x,
                  width: dropdownPosition.width,
                  maxHeight: fullHeight ? "100%" : searchable ? ms(180) : ms(95),
                },
              ]}
            >
              {/* Searchable Input for dropdown search */}
              <SearchableInput
                searchable={searchable}
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
                searchPlaceholder={searchPlaceholder}
              />

              {useFlatList ?
                renderFlatList(filteredData) :
                <ScrollView
                  style={{ maxHeight: dropdownPosition.dropdownHeight }}
                  persistentScrollbar={true}
                  showsVerticalScrollIndicator={true}
                  indicatorStyle="white"
                >
                  {filteredData.map((item, index) => (
                    <TouchableOpacity
                      key={`${item.id}-${index}`}
                      style={sharedStyle(index)}
                      onPress={itemSelectPress(item)}
                    >
                      <Typography.B2 style={[styles.dropdownOptionText, optionTextStyle]}>{item.text}</Typography.B2>
                    </TouchableOpacity>
                  ))}
                </ScrollView>}
            </View>
          </Modal>
        </View>
      )}
    </>
  );
};

export default Dropdown;
