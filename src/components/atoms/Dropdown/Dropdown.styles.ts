/** @format */

// styles/DropdownStyles.ts
import { ms, ScaledSheet } from "react-native-size-matters";

import { Fonts } from "@/constants";
import { Theme } from "@/types/theme/theme";

const getDropdownStyles = (theme: Theme) =>
  ScaledSheet.create({
    dropdownContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: ms(10),
      height: ms(44),
      backgroundColor: theme.colors.dropDownGray,
      minWidth: ms(115),
    },
    androidOverlay: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: "transparent",
      zIndex: 998,
    },
    relativeContainer: {
      position: "relative",
    },
    dropdownOptions: {
      position: "absolute",
      right: 0,
      top: ms(40),
      backgroundColor: theme.colors.dropDownGray,
      width: ms(150),
      maxHeight: ms(110),
      zIndex: 10000,
    },
    androidDropdownOptions: {
      position: "absolute",
      top: "100%",
      left: 0,
      right: 0,
      width: "100%",
      zIndex: 999,
      backgroundColor: theme.colors.item_secondary_bg,
      paddingHorizontal: ms(4),
    },
    modalDropdownOptions: {
      position: "absolute",
      overflow: "hidden",
      paddingHorizontal: ms(4),
    },
    dropdownOption: {
      paddingVertical: ms(8),
      paddingHorizontal: ms(10),
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.lightMediumGray,
    },
    dropdownOptionText: {
      marginRight: ms(5),
      lineHeight: ms(15),
    },
    staticUnitText: {
      color: theme.colors.yellow,
      marginHorizontal: ms(10),
      fontFamily: Fonts.RALEWAY_BOLD,
    },
    modalBackground: {
      flex: 1,
      backgroundColor: "rgba(0,0,0,0.0)", // Semi-transparent background
      justifyContent: "flex-start",
    },
    invertIcon: {
      transform: [{ scaleY: -1 }],
    },
  });

export default getDropdownStyles;
