import { BottomSheetModal, BottomSheetView } from "@gorhom/bottom-sheet";
import React, {
  forwardRef,
  ReactNode,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
} from "react";
import { BlurView } from "@react-native-community/blur";
import { useNavigation } from "@react-navigation/native";
import { StyleProp, ViewStyle, Dimensions, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { getBottomSheetWrapperStyles } from "./RNBottomSheetWrapper.style";

const { height: screenHeight } = Dimensions.get("window");

export type RNBottomSheetWrapperProps = {
  children: ReactNode;
  height?: number;
  snapPoint?: Array<number | "CONTENT_HEIGHT">;
  style?: StyleProp<ViewStyle>;
  loading?: boolean;
  loadingComponent?: ReactNode;
  [key: string]: any; // For additional props passed via `...rest`
};

const RNBottomSheetWrapper = forwardRef<
  BottomSheetModal,
  RNBottomSheetWrapperProps
>(
  (
    {
      children,
      height = screenHeight * 0.85,
      snapPoint = ["CONTENT_HEIGHT"],
      style,
      loading = false,
      loadingComponent,
      ...rest
    },
    ref
  ) => {
    const { bottom } = useSafeAreaInsets();
    const { addListener } = useNavigation();
    const bottomRef = useRef<BottomSheetModal>(null);
    const initialSnapPoints = useMemo(() => snapPoint, [snapPoint]);
    const styles: any = useDynamicStyles(getBottomSheetWrapperStyles);
    useImperativeHandle(ref, () => bottomRef.current, []);

    useEffect(() => {
      const unsubcribe = addListener("state", () => {
        bottomRef.current?.dismiss?.();
        bottomRef.current?.close?.();
      });
      return () => {
        unsubcribe();
      };
    }, []);

    return (
      <BottomSheetModal
        ref={bottomRef}
        stackBehavior="push"
        enableDynamicSizing={false}
        enablePanDownToClose={false}
        snapPoints={initialSnapPoints}
        enableHandlePanningGesture={false}
        backgroundStyle={loading ? styles.opacity_zero : styles.container}
        handleIndicatorStyle={loading ? styles.opacity_zero : styles.indicatorStyle}
        backdropComponent={() => (
          <BlurView
            style={styles.blurBackground}
            blurType="dark"
            blurAmount={10}
            reducedTransparencyFallbackColor="black"
          />
        )}
        {...rest}
      >
        <BottomSheetView
          pointerEvents="box-none"
          style={[
            styles.containerStyle,
            { marginBottom: bottom, height },
            style,
          ]}
        >
          {children}
        </BottomSheetView>
        {loading ? (
          <View
            style={styles.loadingContainer}
            pointerEvents="auto"
          >
            {loadingComponent || null}
          </View>
        ) : null}
      </BottomSheetModal>
    );
  }
);

export default RNBottomSheetWrapper;
