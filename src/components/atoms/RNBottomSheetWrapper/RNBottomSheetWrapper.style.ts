import { config } from "@/theme/_config";
import { Theme } from "@/types/theme/theme";
import { ScaledSheet, ms } from "react-native-size-matters";

export const getBottomSheetWrapperStyles = (theme: Theme) =>
  ScaledSheet.create({
    container: {
      backgroundColor: theme.colors.tile_bg,
      flex: 1,
      borderRadius: ms(32),
    },
    containerStyle: {
      backgroundColor: theme.colors.tile_bg,
    },
    blurBackground: {
      position: "absolute",
      width: "100%",
      height: "100%",
    },
    indicatorStyle: { backgroundColor: theme.colors.tile_bg },
    loadingContainer: {
      position: "absolute",
      top: -ms(60),
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: 9999,
      backgroundColor: "rgba(0,0,0,0.3)",
      justifyContent: "center",
      alignItems: "center",
    },
    opacity_zero: {
      opacity: 0,
    },
  });
