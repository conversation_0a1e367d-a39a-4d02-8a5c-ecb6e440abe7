import { config } from "@/theme/_config";
import { ScaledSheet } from "react-native-size-matters";

export default ScaledSheet.create({
  row: {
    padding: 20,
    backgroundColor: config.colors.white,
    borderBottomColor: '#ccc',
  },
  rightAction: {
    backgroundColor: config.colors.delete,
    justifyContent: 'center',
    alignItems: 'center',
    borderTopEndRadius: "14@s",
    borderBottomEndRadius: "14@s",
    flex: 1,
    width: '80@ms',
    paddingHorizontal: '12@ms'
  },
  actionText: {
    color: '#fff',
    fontWeight: 'bold',
  },
});