import React, { ReactNode } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import Swipeable from 'react-native-gesture-handler/ReanimatedSwipeable';

import styles from './SwipeableRow.styles';
import Icons from '@/theme/assets/images/svgs/icons';

type SwipeableRowProps = {
  onDelete: () => void;
  children: ReactNode;
};

const SwipeableRow: React.FC<SwipeableRowProps> = ({ onDelete, children }) => {
  const renderRightActions = () => {
    return (
      <TouchableOpacity onPress={onDelete}>
        <View style={styles.rightAction}>
          <Icons.Delete />
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <Swipeable
      // onSwipeableWillOpen={() => alert("sdsdsw")}
      friction={1.75}
      overshootFriction={10}
      overshootLeft={false}
      renderRightActions={renderRightActions}
    >
      {children}
    </Swipeable>
  );
};

export default SwipeableRow;
