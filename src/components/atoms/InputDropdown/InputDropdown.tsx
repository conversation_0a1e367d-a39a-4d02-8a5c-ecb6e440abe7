import React, { FC, useState } from 'react';
import { Pressable, StyleProp, TextStyle, View, ViewStyle } from 'react-native';
import Typography from '../Typography/Typography';
import { getInputDropdownStyle } from './InputDropdown.style';
import Icons from '@/theme/assets/images/svgs/icons';
import { useTranslation } from 'react-i18next';
import { ScrollView } from 'react-native-gesture-handler';
import { ms } from 'react-native-size-matters';
import { useDynamicStyles } from '@/theme/hooks/useDynamicStyles';

type InputDropdownProps = {
  menu: object[];
  onSelect: (x: any) => void;
  label: string;
  error?: string | undefined | boolean;
  fromUpdate?: boolean;
  value: any;
  valueKey?: string;
  itemHeight?: number;
  menuHeight?: number;
  inputStyle?: StyleProp<ViewStyle>;
  wrapperStyle?: StyleProp<ViewStyle>;
  dropdownTextStyle?: StyleProp<TextStyle>;
  showsVerticalScrollIndicator?: boolean;
};

const InputDropdown: FC<InputDropdownProps> = ({
  menu = [],
  onSelect,
  label,
  value,
  wrapperStyle,
  inputStyle,
  itemHeight,
  menuHeight,
  error,
  valueKey = 'label',
  dropdownTextStyle,
  fromUpdate,
  showsVerticalScrollIndicator = false
}) => {
  const [dropdownVisibility, setDropdownVisibility] = useState<boolean>(false);
  const [dropdownHeight, setDropdownHeight] = useState(0);
  const styles: any = useDynamicStyles(getInputDropdownStyle)
  const { t } = useTranslation('taskManager');

  function toggleDropdown() {
    setDropdownVisibility((prev) => !prev);
  }
  return (
    <View style={[styles.wrapper, wrapperStyle]}>
      {Boolean(label) && (
        <Typography.B2 style={[styles.labelStyle]} numberOfLines={1}>
          {label}
        </Typography.B2>
      )}

      <Pressable
        style={[
          styles.dropdown,
          inputStyle,
          itemHeight ? { height: itemHeight } : {}
        ]}
        onPress={toggleDropdown}
      >
        <Typography.B2 style={[styles.dropdownText, dropdownTextStyle]}>
          {value?.[valueKey] ?? t('select')}
        </Typography.B2>
        <Icons.Chevron style={{ marginLeft: ms(6) }} />
      </Pressable>

      <View style={{ position: 'relative' }}>
        {dropdownVisibility && (
          <View style={[styles.menuList, inputStyle]}>
            <ScrollView
              showsVerticalScrollIndicator={showsVerticalScrollIndicator}
              onContentSizeChange={(width, height) => setDropdownHeight(height)}
              style={menuHeight && { height: menuHeight ?? dropdownHeight }}
            >
              {menu?.map((item) => {
                return (
                  <>
                    <Pressable
                      style={[
                        styles.menuItem,
                        itemHeight ? { height: itemHeight } : {}
                      ]}
                      onPress={() => {
                        toggleDropdown();
                        onSelect?.(item);
                        if (fromUpdate) {
                          fromUpdate?.(item);
                        }
                      }}
                    >
                      <Typography.B2>{item?.label ?? ""}</Typography.B2>
                    </Pressable>
                    <View style={styles.separator} />
                  </>
                );
              })}
            </ScrollView>
          </View>
        )}
      </View>

      {!!error && (
        <Typography.B4 style={styles.errorMessage}>{error}</Typography.B4>
      )}
    </View>
  );
};

export default InputDropdown;
