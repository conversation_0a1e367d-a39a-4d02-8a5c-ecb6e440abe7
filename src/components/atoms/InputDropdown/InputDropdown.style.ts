import { Fonts } from "@/constants";
import { Theme } from "@/types/theme/theme";
import {
  ms,
  scale,
  ScaledSheet,
  verticalScale,
} from "react-native-size-matters";

export const getInputDropdownStyle = (theme: Theme) =>
  ScaledSheet.create({
    wrapper: {
      marginVertical: verticalScale(8),
      position: "relative",
      zIndex: 10000,
      elevation: 10,
      overflow: "visible",
    },
    dropdown: {
      justifyContent: "space-between",
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: ms(16),
      borderRadius: ms(8),
      borderColor: theme.colors.mediumGray,
      borderWidth: ms(1),
      height: ms(45),
    },
    dropdownText: {
      color: theme.colors.mediumGray,
    },
    labelStyle: {
      color: theme.colors.textPrimary,
      lineHeight: ms(20),
      marginBottom: ms(10),
    },
    menuList: {
      justifyContent: "space-between",
      position: "absolute",
      flex: 1,
      top: ms(6),
      width: "100%",
      zIndex: 99999,
      elevation: 10,
      paddingHorizontal: ms(10),
      backgroundColor: theme.colors.darkGray,
      borderRadius: ms(8),
      borderColor: theme.colors.mediumGray,
      borderWidth: ms(1),
    },
    menuItem: {
      height: ms(45),
      justifyContent: "center",
      width: "100%",

      zIndex: 99999,
      elevation: 10,
    },
    separator: {
      borderBottomColor: theme.colors.mediumGray,
      borderBottomWidth: ms(1),
    },
    errorMessage: {
      fontFamily: Fonts.RALEWAY_REGULAR,
      color: theme.colors.red,
      marginTop: verticalScale(4),
      marginBottom: scale(-16),
      zIndex: -1,
    },
  });
