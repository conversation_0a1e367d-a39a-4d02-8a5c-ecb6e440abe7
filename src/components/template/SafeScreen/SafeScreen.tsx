/** @format */

import { KeyboardAvoidingView, Platform, StatusBar, ViewStyle } from "react-native";
import type { PropsWithChildren } from "react";
import { useSafeAreaInsets } from "react-native-safe-area-context";

import { useTheme } from "@/theme";

interface SafeScreenProps extends PropsWithChildren {
  containerStyle?: ViewStyle;
  backgroundColor?: any;
  styles?: ViewStyle;
}

function SafeScreen({ children, containerStyle, backgroundColor = "", styles }: SafeScreenProps) {
  const insets = useSafeAreaInsets();
  const { variant, navigationTheme } = useTheme();

  return (
    <KeyboardAvoidingView
      style={
        styles
          ? styles
          : [
              {
                flex: 1,
                backgroundColor: backgroundColor ? backgroundColor : navigationTheme.colors.background,
                paddingTop: insets.top,
                paddingLeft: insets.left,
                paddingRight: insets.right,
                paddingBottom: insets.bottom,
              },
              containerStyle,
            ]
      }
      behavior={Platform.OS === "ios" ? "padding" : undefined}
    >
      <StatusBar
        backgroundColor={navigationTheme.colors.background}
        barStyle={variant === "dark" ? "light-content" : "dark-content"}
      />
      {children}
    </KeyboardAvoidingView>
  );
}

export default SafeScreen;
