import { Fonts } from "@/constants";
import { SCREEN_WIDTH } from "@/theme/_config";
import { Theme } from "@/types/theme/theme";
import { StyleSheet } from "react-native";
import { moderateScale, ms, vs } from "react-native-size-matters";

const getAiSuggestionsStyles = (theme: Theme) => StyleSheet.create({
  container: {
    flex: 1,
  },
  capturedPhoto: {
    width: "110%",
    height: moderateScale(118),
    marginBottom: 20,
    resizeMode: "cover",
    marginLeft: '-5%'
  },
  header: {
    fontSize: ms(16),
    fontWeight: "bold",
    color: theme.colors.textPrimary,
    marginBottom: ms(10),
  },
  suggestionContent: {
    paddingVertical: ms(10),
    paddingHorizontal: ms(20),
    backgroundColor: theme.colors.gray,
    borderRadius: ms(10),
    marginBottom: ms(10),
  },
  aiMealName: {
    fontSize: ms(14),
    fontWeight: "bold",
    marginBottom: ms(10),
    color: theme.colors.textPrimary,
  },
  mealHeading: {
    marginTop: ms(24),
    marginBottom: ms(10),
    textAlign: "center",
    fontFamily: Fonts.RALEWAY_BOLD,
  },
  nutrientsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
    marginBottom: vs(12),
  },
  nutrientBox: {
    alignItems: "center",
    flex: 1,
  },
  nutrientValue: {
    color: theme.colors.textPrimaryYellow,
    fontSize: ms(20),
  },
  nutrientLabel: {
    color: theme.colors.mediumGray,
    marginTop: vs(4),
  },
  footer: {
    marginTop: ms(30),
    marginBottom: ms(30),
    paddingHorizontal: ms(28)
  },
  solidBg: {
    flex: 1,
    backgroundColor: theme.colors.gray,
    width: SCREEN_WIDTH,
    marginLeft: -ms(16)
  }
});

export default getAiSuggestionsStyles;
