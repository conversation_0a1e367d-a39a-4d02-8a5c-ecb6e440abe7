import { Button, Typography } from "@/components/atoms";
import React, { useEffect, useMemo } from "react";
import { Image, Pressable, ScrollView, View } from "react-native";
import { ms } from "react-native-size-matters";

import { AISuggestionsItem } from "@/components/molecules";
import axiosInstance from "@/services/axiosInstance";
import { useAppDispatch, useAppSelector } from "@/store";
import {
  clearScanData,
  editingMatchFood,
  fetchFoodEntries,
  selectDietTracker,
  updateScanStatus,
} from "@/store/slices/dietTrackerSlice";
import { SCREEN_WIDTH } from "@/theme/_config";
import Common from "@/theme/common.style";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { FoodEntryItem } from "@/types/schemas/dietTracker";
import { updateDateWithTimeSlot } from "@/utils/helpers";
import getAiSuggestionsStyles from "./AiSuggestions.style";

//Food items suggestion
interface MatchItem {
  matches: Array<{
    food?: {
      description: string;
      quantity: number;
      unit: string;
      gram_weight: number;
      phe: number;
      protein: number;
      food_id: string;
    };
  }>;
}

interface AiSuggestionsProps {
  photoUri: string | null;
  onClose?: () => void;
}

const AiSuggestions = (props: AiSuggestionsProps) => {
  const dispatch = useAppDispatch();
  const { scanStatus, matchFoods, selectedDietDate, selectedTimeSlot } =
    useAppSelector(selectDietTracker);
  const { photoUri, onClose } = props || {};

  const styles: any = useDynamicStyles(getAiSuggestionsStyles);

  const summary = useMemo(() => {
    let totalPhe = 0;
    let totalProtein = 0;

    if (!matchFoods) return { phe: 0, protein: 0 };

    matchFoods?.forEach((food: FoodByIdResponse) => {
      if (food?.user_flags?.is_free) {
        return;
      }
      totalPhe += Number(food.phe || 0);
      totalProtein += Number(food.protein || 0);
    });

    const areAllFoodsFree = matchFoods?.every(
      (food: FoodByIdResponse) => food?.user_flags?.is_free
    );
    return {
      phe: totalPhe?.toFixed(2),
      protein: totalProtein?.toFixed(2),
      areAllFoodsFree,
    };
  }, [JSON.stringify(matchFoods)]);

  useEffect(() => {
    return () => {
      dispatch(clearScanData());
    };
  }, []);

  const handleRemoveItem = (indexToRemove: number) => {
    const removedArray = matchFoods?.filter(
      (_: any, index: number) => index !== indexToRemove
    );
    dispatch(editingMatchFood(removedArray));
  };

  const handleItemUpdate = (index: number, item: FoodEntryItem) => {
    const updatedItems = [...(matchFoods || [])];

    updatedItems[index] = {
      ...updatedItems[index],
      ...item,
      user_flags: { ...updatedItems[index].user_flags, ...item.user_flags },
    };

    dispatch(editingMatchFood(updatedItems));
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={{ width: SCREEN_WIDTH, marginLeft: -ms(16) }}
        contentContainerStyle={{ paddingBottom: ms(78) }}
      >
        <Pressable style={{ flex: 1 }}>
          <Image
            source={{ uri: `file://${photoUri}` }}
            style={styles.capturedPhoto}
          />

          {/*  AI Suggestions */}
          <View style={[Common.appMarginHorizontal, styles.container]}>
            <Typography.H1 style={styles.header}>AI Suggestions</Typography.H1>
            {matchFoods?.length ? React.Children.toArray(
              (matchFoods?.map(
                (item: { food_id?: string | number }, index: number) => (
                  <AISuggestionsItem
                    key={item?.food_id?.toString?.()}
                    item={item} // Pass item to the component
                    onRemove={() => handleRemoveItem(index)} // Handle item removal
                    onUpdate={(updatedFields: any) =>
                      handleItemUpdate(index, updatedFields)
                    }
                  />
                )
              ))
            ) : (scanStatus !== "idle" && scanStatus === "error") ||
              scanStatus === "success" ? (
              <Typography.B1>No items to display.</Typography.B1>
            ) : null}
          </View>

          {matchFoods?.length ? (
            <>
              <Typography.B1 style={styles.mealHeading}>
                Your meal has
              </Typography.B1>

              <View style={styles.nutrientsContainer}>
                {/* Total Phe */}
                <View style={styles.nutrientBox}>
                  <Typography.H1 style={styles.nutrientValue}>
                    {summary?.areAllFoodsFree
                      ? "FREE"
                      : `${summary?.phe || "0"} mg`}
                  </Typography.H1>
                  <Typography.B2 style={styles.nutrientLabel}>
                    Phe
                  </Typography.B2>
                </View>

                {/* Total Protein */}
                <View style={styles.nutrientBox}>
                  <Typography.H1 style={styles.nutrientValue}>
                    {summary?.areAllFoodsFree
                      ? "FREE"
                      : `${summary?.protein || "0"} g`}
                  </Typography.H1>
                  <Typography.B2 style={styles.nutrientLabel}>
                    Protein
                  </Typography.B2>
                </View>
              </View>

              <View style={styles.footer}>
                <Button.Main
                  onPress={async () => {
                    onClose?.();
                    try {
                      dispatch(updateScanStatus("loading"));

                      const delay = (ms: number) =>
                        new Promise((resolve) => setTimeout(resolve, ms));

                      const promises = matchFoods?.map(
                        async (item: any, index: number) => {
                          // Remove .foods
                          // Add delay of 0.5 seconds (500ms) for each request
                          await delay(500 * index);

                          // Get base time for the first entry
                          let baseTime = updateDateWithTimeSlot(
                            `${selectedDietDate}`,
                            selectedTimeSlot
                          );
                          if (baseTime) {
                            // Increment milliseconds for uniqueness
                            baseTime.setUTCMilliseconds(
                              baseTime.getUTCMilliseconds() + index
                            );
                          }

                          const payload = {
                            description: item?.description,
                            items: [
                              {
                                description: item?.description,
                                quantity: parseFloat(item.quantity),
                                unit: item.unit,
                                gram_weight: parseFloat(item.gram_weight),
                                phe: parseFloat(item.phe),
                                protein: parseFloat(item.protein),
                                food_id: item._id,
                                user_flags: {
                                  is_free: !!item?.user_flags?.is_free,
                                },
                              },
                            ],
                            time: baseTime?.toISOString(),
                            analysis_id: item.analysis_id,
                          };
                          const response = await axiosInstance.post(
                            "/Food/CreateEntry",
                            payload
                          );
                          return (
                            response.status >= 200 && response.status < 300
                          );
                        }
                      );

                      await Promise.all(promises || []);

                      dispatch(updateScanStatus("success"));
                      onClose?.();
                    } catch (error) {
                      console.error("Error occurred:", error);
                      dispatch(updateScanStatus("error"));
                    } finally {
                      dispatch(updateScanStatus("idle"));
                      dispatch(
                        fetchFoodEntries({
                          date: new Date(selectedDietDate).toISOString(),
                        })
                      );
                    }
                  }}
                >
                  <Typography.B1 style={[Common.textBold, { color: "white" }]}>
                    Log Food
                  </Typography.B1>
                </Button.Main>
              </View>
            </>
          ) : null}
        </Pressable>
      </ScrollView>
    </View>
  );
};

export default AiSuggestions;
