import React from "react";
import { View, TouchableOpacity } from "react-native";
import { BottomTabBarProps } from "@react-navigation/bottom-tabs";
import { Typography } from "@/components/atoms";
import { getFocusedRouteNameFromRoute } from "@react-navigation/native";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import getCustomTabBarStyle from "./CustomTabBar.styles";
import { useTheme } from "@/theme";

const CustomTabBar = ({
  state,
  descriptors,
  navigation,
}: BottomTabBarProps) => {
  const styles: any = useDynamicStyles(getCustomTabBarStyle);
  const currentRoute = state.routes[state.index];
  const nestedRouteName =
    getFocusedRouteNameFromRoute(currentRoute) ?? "DietScreen"; 
  const { colors } = useTheme();

  const shouldHideTabBar = () => {
    if (currentRoute.name === "Diet" && nestedRouteName !== "DietScreen") {
      return true;
    }
    return false;
  };

  if (shouldHideTabBar()) {
    return null;
  }

  return (
    <View style={styles.container}>
      <View style={styles.topLine} />

      <View style={styles.row}>
        {state?.routes?.map((route, index) => {
          const { options } = descriptors[route.key];

          const isFocused = state.index === index;

          const onPress = () => {
            const event = navigation.emit({
              type: "tabPress",
              target: route.key,
              canPreventDefault: true,
            });

            if (!isFocused && !event.defaultPrevented) {
              navigation.navigate(route.name);
            }
          };

          return (
            <TouchableOpacity
              key={route.key}
              accessibilityRole="button"
              accessibilityState={isFocused ? { selected: true } : {}}
              onPress={onPress}
              style={styles.tab}
            >
              <View style={[styles.tabInner, isFocused && styles.activeTab]}>
                {options.tabBarIcon &&
                  options.tabBarIcon({
                    focused: isFocused,
                    color: isFocused ? colors.white : colors.textPrimary,
                    size: 0,
                  })}
                <Typography.B5
                  style={[
                    { color: isFocused ? colors.white : colors.textPrimary },
                    styles.label,
                  ]}
                >
                  {route.name}
                </Typography.B5>
              </View>
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
};

export default CustomTabBar;
