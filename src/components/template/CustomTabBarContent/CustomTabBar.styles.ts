import { Fonts } from "@/constants";
import { Theme } from "@/types/theme/theme";
import { SCREEN_WIDTH } from "@gorhom/bottom-sheet";
import { StyleSheet } from "react-native";
import { ms, vs } from "react-native-size-matters";

const getCustomTabBarStyle = (theme: Theme) =>
  StyleSheet.create({
    container: {
      backgroundColor: theme.colors.bottomTabColor,
    },
    topLine: {
      height: ms(4),
      backgroundColor: theme.colors.pink,
      marginHorizontal: ms(12),
      borderRadius: ms(10),
    },
    row: {
      flexDirection: "row",
      height: SCREEN_WIDTH * 0.17,
      marginBottom: ms(40),
      marginHorizontal: ms(20),
    },
    tab: {
      flex: 1,
      alignItems: "center",
    },
    tabInner: {
      alignItems: "center",
      justifyContent: "center",
      paddingTop: ms(10),
      paddingBottom: ms(8),
      paddingHorizontal: ms(14),
      borderBottomRightRadius: ms(10),
      borderBottomLeftRadius: ms(10),
      marginTop: (-2),
    },
    activeTab: {
      backgroundColor: theme.colors.pink,
    },
    label: {
      fontSize: ms(11),
      fontFamily: Fonts.RALEWAY_MEDIUM,
      marginTop: vs(7),
    },
  });

export default getCustomTabBarStyle;
