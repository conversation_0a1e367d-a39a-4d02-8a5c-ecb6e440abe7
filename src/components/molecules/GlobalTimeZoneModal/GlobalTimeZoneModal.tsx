import { View } from "react-native";
import React, { useLayoutEffect } from "react";
import { useTranslation } from "react-i18next";

import { store } from "@/store";
import { useTheme } from "@/theme";
import Common from "@/theme/common.style";
import { SCREEN_WIDTH } from "@/theme/_config";
import { closeDrawer } from "@/utils/Navigation";
import Icons from "@/theme/assets/images/svgs/icons";
import { Typography, Button } from "@/components/atoms";
import { getTimeZoneLabel } from "@/utils/timezoneUtils";
import getGlobalTimeZoneModal from "./GlobalTimeZoneModal.styles";
import GenericModal from "@/components/molecules/GenericModal/GenericModal";
import { updateDeviceTimeZoneThunk, updateTimeZoneToggleThunk } from "@/store/slices/settingsSlice";

interface GlobalTimeZoneModalProps {
  isVisible: boolean;
  currentTimeZone: string;
  profileTimeZone: string;
  timeZoneDifference: string;
  onKeepCurrent: () => void;
  onUpdateNew: () => void;
  onClose?: () => void;
  modalWidth?: number | string; // Optional: allow custom width
  modalHeight?: number | string; // Optional: allow custom height
  timeZones: { id: string; displayName: string }[]; // <-- add this
}

const GlobalTimeZoneModal: React.FC<GlobalTimeZoneModalProps> = ({
  isVisible,
  currentTimeZone,
  profileTimeZone,
  timeZoneDifference,
  onKeepCurrent,
  onUpdateNew,
  onClose,
  modalWidth = SCREEN_WIDTH * 0.9, // default width
  modalHeight, // default height (auto)
  timeZones,
}) => {
  const theme = useTheme();
  const { t } = useTranslation(['common']);
  const styles = getGlobalTimeZoneModal(theme);

  useLayoutEffect(() => {
    if (isVisible) {
      store.dispatch(updateTimeZoneToggleThunk(true));
      store.dispatch(updateDeviceTimeZoneThunk());
      closeDrawer()
    }
  }, [isVisible])

  return (
    <GenericModal
      isVisible={isVisible}
      onClose={onClose}
      modalStyle={{ width: modalWidth, height: modalHeight }} // Pass to GenericModal
      customHeader={
        <View style={styles.headerContainer}>
          <Icons.Clock width={48} height={48} color={theme.colors.textPrimary} />
          <Typography.H2 style={styles.headerTitle}>
            {t('common:timezone.tzModalTitle')}
          </Typography.H2>
        </View>
      }
      customBody={
        <View>
          <Typography.B1 style={styles.bodyText}>
            {t('common:timezone.tzModalSubtitle')}
          </Typography.B1>
          <Typography.B3 style={styles.bodyExample}>
            {t('common:timezone.tzModalExample')}
          </Typography.B3>
          <View style={styles.timeZoneInfoContainer}>
            <Typography.B2 style={styles.labelMuted}>
              Current Time Zone
            </Typography.B2>
            <Typography.H3 style={styles.labelHighlight}>
              {getTimeZoneLabel(currentTimeZone, timeZones)}
            </Typography.H3>
            <Typography.B2 style={styles.labelMuted}>
              Your profile Time Zone
            </Typography.B2>
            <Typography.H3 style={styles.labelHighlight}>
              {getTimeZoneLabel(profileTimeZone, timeZones)}
            </Typography.H3>
            <Typography.B2>
              Difference:{" "}
              <Typography.B2 style={styles.labelHighlight}>
                {timeZoneDifference}
              </Typography.B2>
            </Typography.B2>
          </View>
        </View>
      }
      customFooter={
        <View>
          <Button.Outline onPress={onUpdateNew} style={styles.buttonOutline}>
            <Typography.B2 style={[styles.buttonText, Common.textBold]}>
              {t('common:timezone.updateTz')}
            </Typography.B2>
          </Button.Outline>
          <Button.Main onPress={onKeepCurrent}>
            <Typography.B2 style={[styles.buttonText, styles.buttonTextPrimary, Common.textBold]}>
              {t('common:timezone.keepTz')}
            </Typography.B2>
          </Button.Main>
        </View>
      }
    />
  );
};

export default GlobalTimeZoneModal;
