import { Fonts } from "@/constants";
import { Theme } from "@/types/theme/theme";
import { ScaledSheet, ms, vs } from "react-native-size-matters";

const getGlobalTimeZoneModal = (theme: Theme) =>
  ScaledSheet.create({
    headerContainer: {
      alignItems: "center",
      marginBottom: vs(12),
    },
    headerTitle: {
      textAlign: "center",
      marginTop: vs(16),
      fontFamily: Fonts.RALEWAY_BOLD,
    },
    bodyText: {
      textAlign: "center",
      marginBottom: vs(16),
    },
    bodyExample: {
      textAlign: "center",
      marginBottom: vs(24),
      fontStyle: "italic",
    },
    timeZoneInfoContainer: {
      alignItems: "center",
      marginBottom: vs(8),
    },
    labelMuted: {
      color: theme.colors.textPrimary,
      fontFamily: Fonts.RALEWAY_BOLD
    },
    labelHighlight: {
      color: theme.variant === 'light' ? theme.colors.primary : "#FFD600",
      marginBottom: vs(8),
    },
    buttonOutline: {
      marginBottom: vs(8),
      borderColor: theme.variant === 'dark' ? "#FFD600" : theme.colors.borderDarkGray,
      height: vs(40),
      borderRadius: ms(15),
      paddingHorizontal: ms(24)
    },
    buttonText: {
      color: theme.colors.textPrimary,
      paddingHorizontal: ms(16),
    },
    buttonTextPrimary: {
      color: theme.colors.white
    }
  });

export default getGlobalTimeZoneModal;
