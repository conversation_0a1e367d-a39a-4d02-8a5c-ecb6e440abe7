import { Button, Typography } from "@/components/atoms";
import React from "react";
import { ViewStyle, StyleProp } from "react-native";

type Props = {
  onPress: () => void;
  icon: React.ReactNode;
  label: string;
  style?: StyleProp<ViewStyle>;
  textColor?: string;
  textStyle?: StyleProp<Text>;
};

const ActionButton: React.FC<Props> = ({
  onPress,
  icon,
  label,
  style,
  textStyle,
}) => {
  return (
    <Button.Main style={style} onPress={onPress}>
      {icon}
      <Typography.H4 style={textStyle}>{label}</Typography.H4>
    </Button.Main>
  );
};

export default ActionButton;
