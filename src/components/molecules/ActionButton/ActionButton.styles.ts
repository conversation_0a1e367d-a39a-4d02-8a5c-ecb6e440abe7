import { ms, ScaledSheet, vs } from "react-native-size-matters";
import { Dimensions } from "react-native";
import { Theme } from "@/types/theme/theme";
import { SCREEN_HEIGHT } from "@/theme/_config";

const { width } = Dimensions.get("window");

const actionButtonStyle = (theme: Theme) => ScaledSheet.create({
  actionButton: {
    alignItems: "center",
    justifyContent: "center",
    borderRadius: ms(16),
    height: ms(143),
    width: width * 0.44, 
    padding: ms(10),
  },
  actionButtonText: {
    marginTop: ms(8),
    fontSize: ms(14),
    color: theme.colors.white,
    textAlign: "center", 
  },
});

export default actionButtonStyle;
