import { ms, ScaledSheet } from "react-native-size-matters";

import { Fonts } from "@/constants";
import { Theme } from "@/types/theme/theme";

const getGenericModalStyle = (theme: Theme) =>
  ScaledSheet.create({
    bannerImage: {
      width: "100%",
      height: ms(150),
      marginBottom: 0,
      padding: 0,
      borderTopLeftRadius: 0,
      borderTopRightRadius: 0,
      alignSelf: "stretch",
    },
    closeIcon: {
      position: "absolute",
      top: ms(12),
      right: ms(12),
    },
    modalContainer: {
      width: ms(280),
      height: "auto",
      borderWidth: 1.5,
      borderColor: theme.colors.mediumGray,
      backgroundColor: theme.colors.app_bg,
    },
    whatsNewmodalContainer: {
      width: ms(280),
      height: "auto",
    },
    outlineButton: {
      borderWidth: 1,
      borderColor: theme.colors.primary,
      borderRadius: "15@ms",
      paddingVertical: "12@ms",
      alignItems: "center",
    },
    outlineButtonText: {
      color: theme.colors.primary,
      fontSize: "16@ms",
      textAlign: "center",
    },
    modalFooter: {
      padding: "16@ms",
      alignItems: "center",
      marginBottom: "20@ms",
    },
    buttonGroup: {
      width: "100%",
      gap: "12@ms",
      paddingHorizontal: 20,
    },
    confirmButtonText: {
      color: theme.colors.white,
      fontFamily: Fonts.RALEWAY_BOLD,
      fontSize: "16@ms",
      textAlign: "center",
    },
    cancelButtonText: {
      color: theme.colors.primary,
      fontFamily: Fonts.RALEWAY_BOLD,
      fontSize: "16@ms",
      textAlign: "center",
    },
    confirmButton: {
      backgroundColor: theme.colors.primary, // assumes this is pink
      paddingVertical: "12@ms",
      borderRadius: "12@ms",
      alignItems: "center",
    },
    cancelButton: {
      borderWidth: 1,
      borderColor: theme.colors.primary,
      paddingVertical: "12@ms",
      borderRadius: "12@ms",
      alignItems: "center",
    },
    modalHeader: {
      padding: "16@ms",
      alignItems: "center",
      marginTop: "20@ms",
    },
    modalHeaderText: {
      fontSize: "18@ms",
      fontFamily: Fonts.RALEWAY_EXTRA_BOLD,
      color: theme.colors.textPrimary,
      textAlign: "center",
    },
    modalBody: {
      paddingHorizontal: "35@ms",
      paddingVertical: 0,
      alignItems: "center",
    },
    whatsNewModalBody: {
      paddingHorizontal: "20@ms",
      paddingVertical: "16@ms",
      alignItems: "center",
    },
    modalBodyText: {
      fontSize: "14@ms",
      textAlign: "center",
      color: theme.colors.textPrimary,
    },
    modalButton: {
      paddingHorizontal: "16@ms",
      paddingVertical: "8@ms",
      borderRadius: "8@ms",
      marginLeft: "8@ms",
      alignItems: "center",
    },
    modalButtonText: {
      color: theme.colors.textPrimary,
    },
    topIcon:{
       alignItems: "center",
       marginTop:ms(30)
    },
    closeText:{
      textAlign:'center'
    }
  });

export default getGenericModalStyle;
