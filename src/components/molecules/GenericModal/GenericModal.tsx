import React, { useEffect, useState } from "react";
import {
  View,
  TouchableOpacity,
  Image,
  AppState,
  AppStateStatus,
} from "react-native";
import Modal from "@/components/atoms/Modal/Modal";
import { Button, Typography } from "@/components/atoms";

import getGenericModalStyle from "./GenericModal.styles";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import Common from "@/theme/common.style";
import Icons from "@/theme/assets/images/svgs/icons";
import { useTheme } from "@/theme";

interface GenericModalProps {
  isVisible: boolean;
  onClose?: () => void;
  onCloseIcon?: () => void;
  onConfirm?: () => void;
  headerText?: string;
  bodyText?: string;
  confirmText?: string;
  closeText?: string;
  headerColor?: string;
  customHeader?: React.ReactNode;
  customBody?: React.ReactNode;
  customFooter?: React.ReactNode;
  bannerImageSource?: React.ReactNode;
  showWhatsNew?: boolean;
  modalStyle?: object;
  bannerImageStyle?: object;
  svgSource?: object
  customModalStyle?: object,
  customModalHeaderStyle?:object
  customButtonGroupStyle?:object
}

const GenericModal: React.FC<GenericModalProps> = ({
  isVisible: visible,
  onClose,
  onCloseIcon,
  onConfirm,
  headerText,
  bodyText,
  confirmText,
  closeText,
  customHeader,
  customBody,
  customFooter,
  bannerImageSource,
  headerColor,
  showWhatsNew,
  modalStyle,
  bannerImageStyle,
  svgSource,
  customModalStyle,
  customModalHeaderStyle,
  customButtonGroupStyle
}) => {
  const styles: any = useDynamicStyles(getGenericModalStyle);
  const { colors } = useTheme();

  const [appState, setAppState] = useState<AppStateStatus>("active");

  useEffect(() => {
    const subscription = AppState.addEventListener("change", setAppState);
    return () => subscription.remove();
  }, []);

  const shouldShow = visible && appState === "active";

  if (!shouldShow) return null;

  return (
    <Modal
      style={[
        showWhatsNew ? styles.whatsNewmodalContainer : styles.modalContainer,
        modalStyle,
      ]}
      isVisible={true}
      onClose={onClose}
    >
      {onCloseIcon && (
        <TouchableOpacity
          hitSlop={10}
          onPress={onCloseIcon}
          style={styles.closeIcon}
        >
          <Icons.Close width={18} height={18} color={colors.textPrimary} />
        </TouchableOpacity>
      )}

      {bannerImageSource && (
        <Image
          source={bannerImageSource}
          style={[styles.bannerImage, bannerImageStyle]}
          resizeMode="cover"
        />
      )}
      {svgSource && (
        <View style={styles.topIcon}>
          {svgSource}
        </View>
      )}

      <View style={[styles.modalHeader, customModalStyle]}>
        {customHeader || (
          <Typography.H3
            style={[
              styles.modalHeaderText, customModalHeaderStyle,
              headerColor && { color: headerColor },
            ]}
          >
            {headerText}
          </Typography.H3>
        )}
      </View>
      <View style={showWhatsNew ? styles.whatsNewModalBody : styles.modalBody}>
        {customBody || (
          <Typography.B1 style={styles.modalBodyText}>{bodyText}</Typography.B1>
        )}
      </View>

      <View style={styles.modalFooter}>
        {customFooter || (
          <View style={[styles.buttonGroup, customButtonGroupStyle]}>
            {onConfirm && (
              <Button.Main onPress={onConfirm}>
                <Typography.B1 style={styles.confirmButtonText}>
                  {confirmText}
                </Typography.B1>
              </Button.Main>
            )}
            {onClose && (
              <Button.Outline onPress={onClose} style={styles.outlineButton}>
                <Typography.B1  style={[Common.textBold, styles.closeText]}>
                  {closeText}
                </Typography.B1>
              </Button.Outline>
            )}
          </View>
        )}
      </View>
    </Modal>
  );
};

export default GenericModal;
