import { ms, ScaledSheet } from "react-native-size-matters";

const styles = ScaledSheet.create({
  tabView: {
    width: ms(32),
    height: ms(42),
    alignSelf:'center',
    alignItems: 'center',
  },
  tabRow:{
    gap: ms(48),
    width:'100%',
    flexDirection: 'row',
    justifyContent:'space-between',
  },
  tabContainer:{
    paddingTop: ms(18),
    position:'relative',
    paddingBottom: ms(60),
    paddingHorizontal: ms(40),
  }
})

export default styles;