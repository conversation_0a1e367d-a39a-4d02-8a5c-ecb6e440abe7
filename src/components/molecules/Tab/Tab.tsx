import { StyleProp, View, ViewStyle } from "react-native";

import styles from "./Tab.style";
import { useTheme } from "@/theme";

const Tab = (props: { children?: any, style?: StyleProp<ViewStyle> }) => {
  const { children, style } = props || {};
  return (
    <View style={[styles.tabView, style]}>
      {children}
    </View>
  )
}


Tab.Row = (props: { children?: any }) => {
  return <View style={styles.tabRow}>
    {props.children}
  </View>
};


Tab.Container = (props: { children?: any }) => {
  return <View style={styles.tabContainer}>
    <View />
    {props.children}
  </View>
};


export default Tab;