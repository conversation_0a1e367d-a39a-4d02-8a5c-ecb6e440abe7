import { ScaledSheet, ms, verticalScale, vs } from "react-native-size-matters";

import { Theme } from "@/types/theme/theme";
import { Fonts } from "@/constants";

const getSelectDateBottomSheetStyles = (theme: Theme) => ScaledSheet.create({
  container: {
    paddingVertical: ms(20),
    paddingHorizontal: ms(20),
    justifyContent: "space-between",
    flex:0.85
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: ms(20),
  },
  title: {
    fontSize: 22,
    textAlign: "center",
  },
  closeText: {
    fontSize: ms(14),
  },
  dateRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginVertical: ms(50),
  },

  label: {
    fontSize: ms(12),
    color: theme.colors.textPrimary,
    marginBottom: ms(5),
  },
  dateWrapper: {
    flexDirection: "row",
    alignItems: "center",
    borderColor: theme.colors.textPrimaryYellow,
    paddingBottom: ms(5),
  },
  dateText: {
    marginLeft: ms(8),
    fontSize: ms(14),
    color: theme.colors.textPrimaryYellow,
  },
  saveButtonContainer: {
    alignItems: "center",
    marginTop: ms(20),
  },
  saveButton: {
    backgroundColor: theme.colors.primary,
    justifyContent: "center",
    alignItems: "center",
    // marginTop: vs(100),
    // zIndex:111
  },
  errorMessage: {
      fontFamily: Fonts.RALEWAY_REGULAR,
      color: theme.colors.red,
      marginTop: verticalScale(4),
    },
});

export default getSelectDateBottomSheetStyles;
