import React, { useState, useEffect } from "react";
import {
  View,
  Platform,
  TouchableOpacity,
  KeyboardAvoidingView,
} from "react-native";
import dayjs from "dayjs";

import { useAppSelector } from "@/store";
import Common from "@/theme/common.style";
import Icons from "@/theme/assets/images/svgs/icons";
import Button from "@/components/atoms/Button/Button";
import { selectLabs } from "@/store/slices/labsSlice";
import Typography from "@/components/atoms/Typography/Typography";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import getSelectDateBottomSheetStyles from "./SelectDateBottomSheet.styles";
import CustomCalendar from "@/components/atoms/CustomCalendar/CustomCalendar";
import BottomSheetWrapper from "@/components/atoms/BottomSheetWrapper/BottomSheetWrapper";
import { useTheme } from "@/theme";

const SelectDateBottomSheet: React.FC<{
  isVisible: boolean;
  onClose: () => void;
  onSave: (dates: { fromDate: string; toDate: string }) => void; // Pass selected dates
}> = ({ isVisible, onClose, onSave }) => {
  const { dateRange } = useAppSelector(selectLabs);

  const { colors } = useTheme();
  const styles: any = useDynamicStyles(getSelectDateBottomSheetStyles);

  // Calculate default start date (90 days ago) and end date (today)
  const defaultFromDate = dayjs().subtract(89, "day").format("YYYY-MM-DD");
  const defaultToDate = dayjs().format("YYYY-MM-DD"); // Today's date

  const [fromDate, setFromDate] = useState<string>(defaultFromDate);
  const [toDate, setToDate] = useState<string>(defaultToDate);
  const [calendarFor, setCalendarFor] = useState<"from" | "until" | null>(null);
  const [isCalendarVisible, setIsCalendarVisible] = useState(false);
  const [canSelectFuture, setCanSelectFuture] = useState(false);

  // ✅ Retain selected dates and only set defaults if no previous selection
  useEffect(() => {
    if (isVisible) {
      setFromDate((prev) => prev || defaultFromDate); // Retain previous selection
      setToDate((prev) => prev || defaultToDate); // Retain previous selection
    }
  }, [isVisible]);

  const handleDateSelect = (range: { startDate: string }) => {
    if (calendarFor === "from") {
      setFromDate(range.startDate);
    } else if (calendarFor === "until") {
      setToDate(range.startDate);
    }
    setIsCalendarVisible(false); // Hide calendar after selecting the date
  };

  const handleSave = () => {
    onSave({ fromDate, toDate });
    onClose();
  };

  const handleClose = () => {
    onClose();
    setIsCalendarVisible(false);
  };

  let isDisabled = !fromDate || !toDate
  return (
    <BottomSheetWrapper
      height="75%"
      isVisible={isVisible}
      onClose={handleClose}
    >
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === "ios" ? "padding" : undefined}
      >
        {!isCalendarVisible ? (
          <View style={styles.container}>
            <View>
              <Typography.H2 style={[Common.textBold, styles.title]}>
                Select Date
              </Typography.H2>

              {/* From Date (Defaults to 90 days ago but retains selection) */}
              <View style={styles.dateRow}>
                <View>
                  <Typography.B2 style={styles.label}>From</Typography.B2>
                  <TouchableOpacity
                    hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
                    onPress={() => {
                      setCalendarFor("from");
                      setIsCalendarVisible(true);
                      setCanSelectFuture(false);
                    }}
                  >
                    <View style={styles.dateWrapper}>
                      <Icons.Calendar
                        width={15}
                        height={15}
                        fill={colors.textPrimary}
                      />
                      <Typography.B2 style={styles.dateText}>
                        {fromDate || "Add Date"}
                      </Typography.B2>
                    </View>
                    <Typography.B4 style={styles.errorMessage}>{!fromDate ? "From Date is required": ""}</Typography.B4>
                  </TouchableOpacity>
                </View>

                {/* Until Date (Defaults to today but retains selection) */}
                <View>
                  <Typography.B2 style={styles.label}>Until</Typography.B2>
                  <TouchableOpacity
                    hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
                    onPress={() => {
                      setCalendarFor("until");
                      setIsCalendarVisible(true);
                      setCanSelectFuture(true);
                    }}
                  >
                    <View style={styles.dateWrapper}>
                      <Icons.Calendar
                        width={15}
                        height={15}
                        fill={colors.textPrimary}
                      />
                      <Typography.B2 style={styles.dateText}>
                        {toDate || "Add Date"}
                      </Typography.B2>
                    </View>
                    <Typography.B4 style={styles.errorMessage}>{!toDate ? "To Date is required": ""}</Typography.B4>
                  </TouchableOpacity>
                </View>
              </View>
            </View>

            {/* Save Button */}
            <Button.Main disabled={isDisabled} style={styles.saveButton} onPress={handleSave}>
              <Typography.B1 style={[Common.textBold, { color: "white" }]}>
                Save
              </Typography.B1>
            </Button.Main>
          </View>
        ) : (
          // Show Calendar when isCalendarVisible is true
          <View style={{ alignItems: "center", marginTop: 20 }}>
            <CustomCalendar
              isVisible={isCalendarVisible}
              onClose={() => setIsCalendarVisible(false)}
              onDateSelect={({ startDate }) => handleDateSelect({ startDate })}
              disableModal
              canSelectFuture={canSelectFuture}
              initialDate={calendarFor === "from" ? fromDate : toDate} // Retain selected date
            />
          </View>
        )}
      </KeyboardAvoidingView>
    </BottomSheetWrapper>
  );
};

export default SelectDateBottomSheet;
