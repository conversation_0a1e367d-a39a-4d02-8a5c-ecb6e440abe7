import React, { useCallback, useState } from "react";
import { View, TouchableOpacity, ViewStyle, TextStyle } from "react-native";
import { Typography } from "@/components/atoms";
import Tag from "@/components/atoms/Tag/Tag";
import Common from "@/theme/common.style";

import { useAnalytics, analyticsEventType } from "@/hooks/useAnalytics";
import { getUnitAbbreviation, truncateText } from "@/utils/helpers";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import getMealItemStyles from "./MealItem.styles";
import { useTheme } from "@/theme";
import { useSelector } from "react-redux";
import {
  selectConsumptionUnit,
  selectIsSimplifiedDiet,
} from "@/store/slices/settingsSlice";

interface MealItemProps {
  name: string;
  quantity: string;
  unit: string;
  phe: string;
  icon?: React.ReactNode; // Prop to accept a custom icon component
  onPress?: () => void; // Callback when the icon is pressed
  backgroundColor?: string; // Customizable background color
  tagBackgroundColor?: string; // Customizable tag background color
  textColor?: string; // Customizable text color
  isTextBold?: boolean;
  isFreeFood?: boolean;
  children?: React.ReactNode;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

const MealItem: React.FC<MealItemProps> = ({
  name,
  quantity,
  unit,
  phe,
  icon,
  onPress,
  backgroundColor,
  tagBackgroundColor,
  textColor,
  isTextBold,
  isFreeFood = false,
  children,
  style,
  textStyle,
}) => {
  const [toggle, setToggle] = useState<boolean>(false);
  const { setAnalyticsEvent } = useAnalytics();
  const consumptionType = useSelector(selectConsumptionUnit);
  const isSimplifiedDiet = useSelector(selectIsSimplifiedDiet);

  const styles: any = useDynamicStyles(getMealItemStyles);
  const { colors } = useTheme();
  const handleToggle = useCallback(() => {
    setAnalyticsEvent(analyticsEventType.custom, {
      event: "diet_frequent_food_logged",
      item_id: "diet_frequent_food_logged",
      action: "User logged food from frequent food",
    });
    setToggle((prev) => !prev);
  }, []);

  return (
    <View>
      <TouchableOpacity
        onPress={onPress}
        style={[
          styles.mealItem,
          !backgroundColor
            ? { backgroundColor: colors.item_secondary_bg }
            : { backgroundColor },
          toggle ? styles.expand : {},
          style,
        ]} // Apply custom background
      >
        {/* Row containing Meal Name & Icon */}
        <View style={styles.mealNameContainer}>
          <Typography.B2
            style={[
              Common.textBold,
              styles.mealName,
              { color: textColor || colors.textPrimary },
              textStyle,
            ]}
          >
            {name}
          </Typography.B2>

          {/* Right-End Icon */}
          {icon && (
            <TouchableOpacity
              style={toggle ? styles.invertIcon : styles.iconContainer}
              onPress={handleToggle}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              {icon}
            </TouchableOpacity>
          )}
        </View>

        {/* Meal Details - Auto adjust tag width & keep them in the same row */}
        <View style={styles.mealDetailsContainer}>
          {/* Quantity */}
          <View style={Common.flexOne}>
            <Tag.Main
              style={[
                styles.tag,
                { backgroundColor: tagBackgroundColor || colors.dietTagGray },
              ]}
            >
              <Typography.B5
                style={[
                  isTextBold && Common.textBold,
                  styles.mealDetails,
                  { color: textColor },
                ]}
              >
                Qty: {quantity.toLowerCase()} {getUnitAbbreviation(unit)}
              </Typography.B5>
            </Tag.Main>
          </View>

          {/* Phe on Next Row */}
          <View style={Common.flexOne}>
            <Tag.Main
              style={[
                styles.tag,
                { backgroundColor: tagBackgroundColor || colors.dietTagGray },
              ]}
            >
              <Typography.B5
                style={[
                  isTextBold && Common.textBold,
                  styles.mealDetails,
                  { color: textColor },
                ]}
              >
                {`${consumptionType === "Protein" ? "PRO" : "Phe"}: ${isSimplifiedDiet && isFreeFood ? "FREE" : `${phe} ${consumptionType === "Protein" ? "g" : "mg"}`}`}
              </Typography.B5>
            </Tag.Main>
          </View>
        </View>
      </TouchableOpacity>

      {toggle ? children : null}
    </View>
  );
};

export default MealItem;
