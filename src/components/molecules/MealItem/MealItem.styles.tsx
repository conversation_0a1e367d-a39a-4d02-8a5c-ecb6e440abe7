import { Theme } from "@/types/theme/theme";
import { ms, ScaledSheet } from "react-native-size-matters";

const getMealItemStyles = (theme: Theme) =>
  ScaledSheet.create({
    mealItem: {
      padding: ms(12),
      backgroundColor: theme.colors.dietCard,
      borderRadius: ms(8),
    },
    mealNameContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      width: "100%",
    },
    mealName: {
      flex: 1,
    },
    mealDetailsContainer: {
      flexDirection: "row",
      gap: ms(10),
      marginTop: 10,
    },
    mealDetails: {
      fontSize: ms(11.3),
      textAlign: "center",
    },
    tag: {
      minWidth: ms(60),
      paddingHorizontal: ms(8),
      height: ms(24),
      justifyContent: "center",
      alignItems: "center",
      flexShrink: 1,
    },
    iconContainer: {
      justifyContent: "center",
      alignItems: "center",
      marginLeft: ms(8),
    },
    invertIcon: {
      transform: [{ scaleY: -1 }],
      marginLeft: ms(8),
      justifyContent: "center",
      alignItems: "center",
    },

    expand: {
      marginBottom: 0,
      borderRadius: 0,
      borderTopLeftRadius: ms(8),
      borderTopRightRadius: ms(8),
    },
  });

export default getMealItemStyles;
