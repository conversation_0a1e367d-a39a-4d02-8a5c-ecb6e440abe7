import { Fonts } from "@/constants";
import { StyleSheet } from "react-native";
import { ms } from "react-native-size-matters";

import { Theme } from "@/types/theme/theme";

const getInfoSectionStyles = (theme: Theme) => StyleSheet.create({
  infoSection: {
    backgroundColor: theme.colors.tile_bg,
    borderRadius: ms(10),
    paddingVertical: ms(30),
    paddingHorizontal: ms(26),
    marginBottom: ms(10),
  },
  sectionTitle: {
    fontFamily: Fonts.RALEWAY_BOLD,
    marginBottom: ms(10),
  },
  infoItem: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: ms(8),
  },
  infoText: {
    marginLeft: ms(10),
  },
});

export default getInfoSectionStyles;