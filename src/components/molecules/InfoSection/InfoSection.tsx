import React from "react";
import { View } from "react-native";
import styles from "./InfoSection.styles";
import Icons from "@/theme/assets/images/svgs/icons";
import { Typography } from "@/components/atoms";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import getInfoSectionStyles from "./InfoSection.styles";
import { useTheme } from "@/theme";

interface InfoItem {
  icon: "person" | "mail" | "phone";
  text: string;
}

interface InfoSectionProps {
  title: string;
  items: InfoItem[];
}

// Store component references instead of JSX elements
const iconComponents = {
  person: Icons.Profile,
  mail: Icons.Mail,
  phone: Icons.Phone,
};

const InfoSection: React.FC<InfoSectionProps> = ({ title, items }) => {
  const { colors } =useTheme();
  const styles: any = useDynamicStyles(getInfoSectionStyles)
  return (
    <View style={styles.infoSection}>
      <Typography.H3 style={styles.sectionTitle}>{title}</Typography.H3>
      {React.Children.toArray(items.map((item, index) => {
        const IconComponent = iconComponents[item.icon];
        if (!item.text) {
          return null;
        }
        return (
          <View style={styles.infoItem}>
            <IconComponent width={16} height={16} color={colors.yellowBlue}/>
            <Typography.B2 style={styles.infoText}>{item.text}</Typography.B2>
          </View>
        );
      }))}
    </View>
  )
};

export default InfoSection;
