import { Image, View, StyleSheet } from "react-native";
import Splash from '@/theme/assets/images/splash.png';

const ScreenGuardModal = ({ show }: { show: boolean }) => {
  if (!show) return null; // Prevent rendering when `show` is false

  return (
    <View style={styles.container}>
      <Image source={Splash} style={styles.image} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000, // Ensure it overlays everything
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
});

export default ScreenGuardModal;
