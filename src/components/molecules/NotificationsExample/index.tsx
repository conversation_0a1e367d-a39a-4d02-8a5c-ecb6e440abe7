import React, { useEffect } from 'react';
import { View, Platform } from 'react-native';
import notifee, { AndroidImportance, AuthorizationStatus, TriggerType } from '@notifee/react-native';
import { StyledButton } from '@/components/atoms';

const NotificationScheduler = () => {

  async function requestNotificationPermission() {
    const authStatus = await notifee.requestPermission();

    if (authStatus.authorizationStatus !== AuthorizationStatus.AUTHORIZED) {
      throw new Error('Permission not granted for notifications');
    }
  }
  useEffect(() => {
    async function initialize() {
      try {
        await createNotificationChannel();
        await requestNotificationPermission();
      } catch (error) {
        console.error('Initialization error:', error);
      }
    }
    initialize();
  }, []);

  async function scheduleNotification(
    title: string,
    body: string,
    scheduledTime: Date
  ) {
    // Check if the notification time is in the past
    if (scheduledTime < new Date()) {
      throw new Error('Scheduled time must be in the future.');
    }

    // Prepare the notification content
    const notificationContent = {
      title,
      body,
      android: {
        channelId: 'default',
        importance: AndroidImportance.HIGH,
        sound: 'pillbox',
      },
      ios: {
        critical: true,
        sound: 'pillbox.wav',
      },
    };

    try {
      // Schedule the notification
      await notifee.createTriggerNotification(
        notificationContent,
        {
          type: TriggerType.TIMESTAMP,
          timestamp: scheduledTime.getTime(), // Convert to milliseconds
        }
      );
    } catch (error) {
      // Handle errors that may occur during scheduling
      console.error('Error scheduling notification:', error);
      throw new Error('Failed to schedule notification.');
    }
  }


  async function createNotificationChannel() {
    await notifee.createChannel({
      id: 'default',
      name: 'Default Channel',
      sound: Platform.OS === 'ios' ? 'pillbox.wav' : 'pillbox',
      importance: AndroidImportance.HIGH,
    });
  }

  const handleScheduleNotification = async () => {
    try {
      const scheduledTime = new Date(Date.now() + 10000); // 10 seconds from now
      await scheduleNotification('Hello!', 'This is a scheduled notification.', scheduledTime);
    } catch (error) {
      console.error('Error scheduling notification:', error);
    }
  };

  return (
    <View>
      <StyledButton label="Schedule Notification" onPress={handleScheduleNotification} />
    </View>
  );
};

export default NotificationScheduler;
