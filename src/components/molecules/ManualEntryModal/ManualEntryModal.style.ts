import { ms, ScaledSheet } from "react-native-size-matters";
import { Theme } from "@/types/theme/theme";

const getSaveMealStyle = (theme: Theme) =>
  ScaledSheet.create({
    container: {
      flex: 1,
    },
    row: {
      flexDirection: "row",
      alignItems: "center",
    },
    saveButtonContainer: {
      marginVertical: ms(24),
      marginTop: ms(90),
    },
    servingInputContent: {
      width: "100%",
      alignItems: "center",
      justifyContent: "space-between",
      borderRadius: ms(8),
      borderWidth: ms(1),
      height: ms(40.5),
      flexDirection: "row",
      borderColor: theme.colors.mediumGray,
      marginTop: ms(10),
      overflow: "hidden",
    },
    servingTextInputFocused: {
      borderColor: theme.colors.yellow,
      borderWidth: ms(1),
    },
    scrollContainer: {
      justifyContent: "center",
      padding: ms(5),
      height: "140%",
    },
    errorText: {
      color: theme.colors.red,
      fontSize: ms(12),
      marginTop: ms(4),
    },
    note: {
      color: theme.colors.textPrimaryYellow,
      marginVertical: ms(8),
      fontSize: ms(13),
    },
    header: {
      alignItems: "center",
      marginBottom: ms(40),
    },
    title: {
      marginTop: ms(20),
      fontSize: ms(22),
      textAlign: "center",
    },
  });

export default getSaveMealStyle;
