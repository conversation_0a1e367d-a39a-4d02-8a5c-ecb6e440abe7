/** @format */

import { useDispatch } from "react-redux";
import Svg, { Circle, G } from "react-native-svg";
import React, { useEffect, useRef, useState } from "react";
import { View, TouchableOpacity, AppStateStatus, AppState } from "react-native";

import { store } from "@/store";
import { useTheme } from "@/theme";
import { config } from "@/theme/_config";
import Common from "@/theme/common.style";
import { Typography } from "@/components/atoms";
import Card from "@/components/atoms/Card/Card";
import Icons from "@/theme/assets/images/svgs/icons";
import WeeklyCalendarStyles from "./WeeklyCalendarCard.style";
import useAppStateListener from "@/hooks/useAppStateListener";
import { selectDietDate } from "@/store/slices/dietTrackerSlice";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import CustomCalendar from "@/components/atoms/CustomCalendar/CustomCalendar";
import {
  formatLocalISO,
  getCurrentDayIndex,
  getFormattedCurrentDate,
} from "@/utils/helpers";
import { setRecentMoodLog } from "@/store/slices/dashboardSlice";
import  {formatLocalISOTimeZone} from "@/utils/timezoneUtils";

interface WeeklyCalendarProps {
  onDayPress?: (day: string, index: number, date: string) => void;
  weeklyProgressPercentages?: { date: string; percentage: number }[]; // Array of progress objects
  hasPercentages?: boolean;
  canSelectFuture?: boolean;
  isPeriod?: boolean;
}

const WeeklyCalendar: React.FC<WeeklyCalendarProps> = ({
  onDayPress,
  weeklyProgressPercentages = [],
  isPeriod = false,
  hasPercentages = false,
}) => {
  const days = ["S", "M", "T", "W", "T", "F", "S"];
  const [selectedDay, setSelectedDay] = useState(getCurrentDayIndex());
  const [currentDate, setCurrentDate] = useState(new Date());
  const [isCalendarVisible, setIsCalendarVisible] = useState(false);
  const [weekStartDate, setWeekStartDate] = useState(new Date());
  const appState = useRef<AppStateStatus>(AppState.currentState);

  const { variant ,colors} = useTheme();
  const dispatch = useDispatch();
  const styles: any = useDynamicStyles(WeeklyCalendarStyles);
  const { selectedDietDate } = store.getState()?.dietTracker;

  useAppStateListener(undefined, () => {
    setIsCalendarVisible(false);
  });

  const handleCalendarIconPress = () => {
    setIsCalendarVisible(true);
  };

  const handleDayPress = (index: number) => {
    if (selectedDay !== index) {
      dispatch(setRecentMoodLog(null));
    }

    const selectedDate = new Date(weekStartDate);
    selectedDate.setDate(weekStartDate.getDate() + index);

    const isoDate = formatLocalISO(new Date(selectedDate.toString())); // Convert to ISO string
    const isToday = selectedDate.toDateString() === new Date().toDateString(); // Check if selected date is today

    setSelectedDay(index);
    setCurrentDate(selectedDate);

    dispatch(selectDietDate(isoDate)); // Update selectedDietDate in Redux as ISO string

    // Notify parent component
    if (onDayPress) {
      onDayPress(days[index], index, isoDate);
    }
  };

  const handleDateSelect = (range: { startDate: string; endDate: string }) => {
  const [year, month, day] = range.startDate.split('T')[0].split('-').map(Number);
  const selectedDate = new Date(year, month - 1, day); 
  const sundayDate = new Date(selectedDate);
  sundayDate.setDate(selectedDate.getDate() - selectedDate.getDay()); 
  setWeekStartDate(sundayDate);
  const selectedDayIndex = selectedDate.getDay(); 
  setSelectedDay(selectedDayIndex);
  setCurrentDate(selectedDate);

  const isoDate = formatLocalISO(selectedDate);
  dispatch(selectDietDate(isoDate));
  if (onDayPress) {
    onDayPress(days[selectedDayIndex], selectedDayIndex, isoDate);
  }
  setIsCalendarVisible(false);
};

  const getWeekDates = () => {
    const start = new Date(weekStartDate);
    return Array.from({ length: 7 }, (_, i) => {
      const date = new Date(start);
      date.setDate(start.getDate() + i);
      return date;
    });
  };

  const weekDates = getWeekDates();

  const getPercentageForDate = (date: Date): number => {
    const progress = weeklyProgressPercentages.find((p) => new Date(p.date).toDateString() === date.toDateString());
    return progress?.percentage || 0;
  };

const syncWithToday = () => {
    const today = new Date();
    const todayIndex = today.getDay(); // 0 = Sun, 1 = Mon, etc.
    const localTodayStr = formatLocalISO(today); // "2024-08-26"

    // Update state
    setCurrentDate(today);
    setSelectedDay(todayIndex);

    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - today.getDay());
    setWeekStartDate(startOfWeek);

    // Sync with Redux
    dispatch(selectDietDate(localTodayStr));
  };

  useEffect(() => {
    syncWithToday();
  }, [dispatch]);

  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active') {
        syncWithToday();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription.remove();
  }, []);

  const getProgressColor = (percentage: number, isToday: boolean): string => {
    if (isToday) return colors.primary; // Today's progress bar color
    if (percentage === 100) return colors.primary; // Full progress
    if (percentage === 0) return config.colors.gray; // No progress
    return config.colors.yellow; // Partial progress
  };

  const getDayContainerStyle = (percentage: number, isToday?: boolean): object => {
    if (isToday || percentage === 100) {
      return {
        backgroundColor: colors.primary, // Primary color for today's date or completed
      };
    }
    if (percentage > 0 && percentage < 100) {
      return {
        backgroundColor: config.colors.yellow, // Yellow for partial progress
      };
    }
    return {};
  };

  return (
    <Card style={styles.weeklyBox}>
      <TouchableOpacity onPress={handleCalendarIconPress} style={styles.row2}>
        <Icons.Calendar fill={variant === "light" ? config.colors.textPrimary : config.colors.white} />
        <View style={styles.dateWrapper}>
          <Typography.B2 style={[Common.textBold, styles.header3Text]} numberOfLines={1} ellipsizeMode="clip">
            {getFormattedCurrentDate(new Date(selectedDietDate))}
          </Typography.B2>
        </View>
      </TouchableOpacity>

      <View style={styles.daysRow}>
        {weekDates.map((date, index) => {
          const isToday = date.toDateString() === new Date().toDateString();
          const percentage = getPercentageForDate(date);

          return (
            <TouchableOpacity key={index} onPress={() => handleDayPress(index)}>
              <View style={styles.progressContainer}>
                {hasPercentages && (
                  <Svg height={44} width={44}>
                    <G rotation="-90" origin="22,22">
                      <Circle
                        cx="22"
                        cy="22"
                        r="19"
                        stroke={getProgressColor(percentage, isToday)}
                        strokeWidth={2}
                        strokeDasharray={Math.PI * 2 * 19}
                        strokeDashoffset={Math.PI * 2 * 19 - (Math.PI * 2 * 19 * percentage) / 100}
                        fill="none"
                      />
                    </G>
                  </Svg>
                )}
                <View
                  style={[
                    styles.dayContainer,
                    hasPercentages && styles.dayContainerWithProgressBar,
                    getDayContainerStyle(percentage, isToday),
                    !percentage && index === selectedDay && styles.activeDayContainer,
                  ]}
                >
                  {isToday && <View style={styles.dotAbove} />}
                  <Typography.B2
                    style={[Common.textBold, styles.dayText, (index === selectedDay || isToday|| percentage)&& styles.activeDayText]}
                  >
                    {days[index]}
                  </Typography.B2>
                </View>
              </View>
            </TouchableOpacity>
          );
        })}

        <CustomCalendar
          isVisible={isCalendarVisible}
          onClose={() => setIsCalendarVisible(false)}
          onDateSelect={handleDateSelect}
          hideClear
          canSelectFuture
          hasPercentages={hasPercentages}
          isPeriod={isPeriod}
         initialDate={formatLocalISOTimeZone(currentDate)} // Pass selected date
        />
      </View>

      <View style={{ margin: 20 }}></View>
    </Card>
  );
};

export default WeeklyCalendar;
