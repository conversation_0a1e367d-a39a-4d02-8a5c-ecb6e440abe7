
import { Theme } from "@/types/theme/theme";
import { ms, ScaledSheet } from "react-native-size-matters";

const WeeklyCalendarStyles = (theme: Theme) => ScaledSheet.create({
  weeklyBox: {
    gap: "16@ms",
    width: "100%",
    height: "110@ms",
    backgroundColor: theme.colors.tile_bg,
    borderRadius: "16@ms",
    elevation: 1
  },
  row2: {
    flexDirection: "row",
    alignItems: "center",
    width: "100%",
    justifyContent: 'flex-start'
  },
  dateWrapper: {
    flex: 1,
    minWidth: 0,
  },
  header3Text: {
    fontSize: ms(12),
    marginLeft: ms(10)
  },
  daysRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginTop: ms(10)
  },
  progressContainer: {
    alignItems: "center",
    justifyContent: "center",
    width: ms(44),
    height: ms(44)
  },
  dayContainer: {
    width: ms(28),
    height: ms(28),
    borderRadius: ms(18),
    backgroundColor: theme.colors.gray,
    justifyContent: "center",
    alignItems: "center",
    position: "absolute", // Centered inside the circle
  },
  dayContainerWithProgressBar: {
    width: ms(30),
    height: ms(30),
  },
  activeDayContainer: {
    backgroundColor: theme.colors.primary,
  },
  dayText: {
    fontSize: ms(14),
    color: theme.colors.textPrimary,
  },
  activeDayText: {
    color: theme.colors.white,
  },
  dotAbove: {
    width: ms(8),
    height: ms(8),
    position: "absolute",
    borderRadius: ms(4),
    backgroundColor: theme.colors.primary,
    top: -ms(18),
  },
});

export default WeeklyCalendarStyles;
