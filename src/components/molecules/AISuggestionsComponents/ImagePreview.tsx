/** @format */

import React, { useEffect, useState } from "react";
import { View, Pressable, Image, Dimensions } from "react-native";
import { useNavigation } from "@react-navigation/native";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing,
} from "react-native-reanimated";
import { SCREEN_WIDTH } from "@/theme/_config";
import ExpandIcon from "@/theme/assets/images/svgs/ExpandIcon.svg";
import CollapseIcon from "@/theme/assets/images/svgs/CollapseIcon.svg";
import styles from "./ImagePreview.style";
import default_img from "@/theme/assets/images/default_food.png";
import { useSelector } from "react-redux";
import { RootState } from "@/store";

const { height: SCREEN_HEIGHT } = Dimensions.get("window");

export default function ImagePreview({setIsLarge}:{setIsLarge:any}) {
  const navigation = useNavigation();
  const imageUrl = useSelector(
    (state: RootState) => state.dietTracker.captureImage
  );

  const [error, setError] = useState(false);
  const [imageReady, setImageReady] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  const isRemote = /^https?:\/\//i.test(imageUrl || "");

  // Animated shared values
  const animatedHeight = useSharedValue(SCREEN_WIDTH * 0.6);
  const animatedTranslateY = useSharedValue(0);

  const toggleExpand = () => {
    const newState = !isExpanded;
    setIsExpanded(newState);
    setIsLarge(newState)
    if (newState) {
      animatedHeight.value = withTiming(SCREEN_HEIGHT, {
        duration: 400,
        easing: Easing.out(Easing.cubic),
      });
      animatedTranslateY.value = withTiming(0, {
        duration: 400,
        easing: Easing.out(Easing.cubic),
      });
    } else {
      animatedHeight.value = withTiming(SCREEN_WIDTH * 0.6, {
        duration: 400,
        easing: Easing.out(Easing.cubic),
      });
      animatedTranslateY.value = withTiming(0, {
        duration: 400,
        easing: Easing.out(Easing.cubic),
      });
    }
  };

  const animatedStyle = useAnimatedStyle(() => ({
    height: animatedHeight.value,
    transform: [{ translateY: animatedTranslateY.value }],
  }));

  useEffect(() => {
    if (!imageUrl) return setError(true);

    if (isRemote) {
      Image.prefetch(imageUrl)
        .then(() => {
          setImageReady(true);
          setError(false);
        })
        .catch(() => {
          setError(true);
        });
    } else {
      setImageReady(true);
      setError(false);
    }
  }, [imageUrl]);

  return (
    <View style={styles.imageContainer}>
      {imageReady && !error && (
        <>
          <Animated.Image
            source={{ uri: imageUrl || "" }}
            defaultSource={default_img}
            resizeMode="cover"
            style={[{ width: SCREEN_WIDTH }, animatedStyle]}
            onError={() => setError(true)}
          />

          <Pressable onPress={toggleExpand} style={styles.iconContainer}>
            {isExpanded ? (
              <CollapseIcon style={styles.icon} />
            ) : (
              <ExpandIcon style={styles.icon} />
            )}
          </Pressable>
        </>
      )}
    </View>
  );
}
