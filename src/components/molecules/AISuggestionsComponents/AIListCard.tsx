/** @format */

import React, { useRef, useState } from "react";
import { ActivityIndicator, FlatList, Keyboard, LogBox, Pressable, Text, TouchableOpacity, View } from "react-native";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import getAIListCardStyles from "./AIListCard.style";
import Icons from "@/theme/assets/images/svgs/icons";
import { useTheme } from "@/theme";
import { Typography } from "@/components/atoms";
import { ms } from "react-native-size-matters";
import SearchListInput, { SearchListInputRef } from "@/components/atoms/SearchListInput/SearchListInput";
import { useSelector } from "react-redux";
import {
  replaceWithAlternative,
  selectDietTracker,
  selectDietTrackerLoading,
  selectFoodItemsByFrequency,
} from "@/store/slices/dietTrackerSlice";
import Common from "@/theme/common.style";
import { useAppDispatch, useAppSelector } from "@/store";
import { getFoodById } from "@/services/api/dietTrackerAPI";
import Animated, { runOnJS, useAnimatedStyle, useSharedValue, withTiming } from "react-native-reanimated";

const AIListCard = ({
  item,
  onAddNew,
  onDelete,
  index,
  isSwapped,
  onSwap,
  resetSwap,
}: {
  item: any;
  onAddNew: () => void;
  onDelete: () => void;
  index: number;
  isSwapped: boolean;
  onSwap: () => void;
  resetSwap: () => void;
}) => {
  LogBox.ignoreLogs(["VirtualizedLists should never be nested"]);
  const styles: any = useDynamicStyles(getAIListCardStyles);
  const { variant, colors } = useTheme();
  const [activeTab, setActiveTab] = useState<"alternatives" | "search" | null>(null);
  const searchResults = useSelector(selectFoodItemsByFrequency);
  const dispatch = useAppDispatch();
  const loading = useSelector(selectDietTrackerLoading);
  const [isAPILoading, setIsAPILoading] = useState<boolean>(false);
  const { analysisId, matchFoods } = useAppSelector(selectDietTracker);
  const searchInputRef = useRef<SearchListInputRef>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const translateX = useSharedValue(0);
  const opacity = useSharedValue(1);

  const renderDropdownItem = ({ item, index }: { item: any; index: number }) => (
    <TouchableOpacity
      onPress={() => {
        handleSelectItem(item);
      }}
      style={styles.dropdownItem}
    >
      <Typography.B2 style={{ ...styles.dropdownItemText, marginTop: index == 0 ? 10 : 0 }}>
        {item?.description}
      </Typography.B2>
    </TouchableOpacity>
  );
  const handleSelectItem = async (item: any) => {
    Keyboard.dismiss();
    if (activeTab === "search") {
      setIsAPILoading(true);

      const food = await getFoodById(
        item?.foodId || item?.id,
        parseFloat(item?.editQuantity) || 1,
        item?.editUnit,
        item?.editGramWeight || 0
      );
      const itemToAdd = {
        ...food,
        _localId: matchFoods[index]._localId, // keep original
        _version: (matchFoods[index]._version || 0) + 1,
        analysis_id: analysisId,
      };
      searchInputRef.current?.reset();
      dispatch(
        replaceWithAlternative({
          index,
          data: itemToAdd,
        })
      );
      setIsAPILoading(false);
      setTimeout(() => setActiveTab(null), 200);
      onSwap();
    } else {
      dispatch(
        replaceWithAlternative({
          index,
          data: {
            ...item,
            _localId: matchFoods[index]._localId, // keep original
            _version: (matchFoods[index]._version || 0) + 1,
          },
        })
      );
      setTimeout(() => setActiveTab(null), 200);
      onSwap();
    }
  };
  const toggleTab = (tabName: "alternatives" | "search") => {
    const newTab = activeTab === tabName ? null : tabName;
    setActiveTab(newTab);

    if (newTab) {
      resetSwap(); // reset swapped state in parent only when opening a tab
    }
  };
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value }],
    opacity: opacity.value,
  }));

  const handleDeletePress = () => {
    translateX.value = withTiming(-500, { duration: 300 });
    opacity.value = withTiming(0, { duration: 300 }, (finished) => {
      if (finished) {
        runOnJS(onDelete)();
      }
    });
  };

  return (
    <Animated.View style={[styles.cardWrapper, animatedStyle]}>
      <View style={[styles.topRow, isSwapped && styles.swappedTop]}>
        <Text style={styles.foodNameText}>{item?.description}</Text>
        <TouchableOpacity onPress={handleDeletePress} style={styles.trashButton}>
          {variant === "dark" ? <Icons.DeleteDark /> : <Icons.DeleteLight />}
        </TouchableOpacity>
      </View>

      {item?.alternatives && (
        <View style={[styles.bottomContainer, isSwapped && styles.swappedBottom]}>
          <Pressable
            style={[styles.bottomToggleButtons, styles.altButton, activeTab === "alternatives" && styles.activeTab]}
            onPress={() => toggleTab("alternatives")}
          >
            {activeTab === "alternatives" ? (
              <Icons.AlternativeDark />
            ) : variant === "dark" ? (
              <Icons.AlternativeDark />
            ) : (
              <Icons.AlternativeLight />
            )}
            <Typography.B2
              style={[styles.bottonToggleButtonsText, activeTab === "alternatives" && styles.activeTabText]}
            >
              Alternatives
            </Typography.B2>
          </Pressable>

          <Pressable
            style={[styles.bottomToggleButtons, styles.searchButton, activeTab === "search" && styles.activeTab]}
            onPress={() => toggleTab("search")}
          >
            <Icons.Search width={16} height={16} color={activeTab === "search" ? colors.white : colors.textPrimary} />
            <Typography.B2 style={[styles.bottonToggleButtonsText, activeTab === "search" && styles.activeTabText]}>
              Search
            </Typography.B2>
          </Pressable>
        </View>
      )}
      {activeTab && (
        <View
          style={{
            maxHeight: activeTab == "alternatives" ? ms(135) : ms(220),
            ...styles.dropdownList,
          }}
        >
          {activeTab == "search" && (
            <SearchListInput
              placeholder="Search for Foods"
              ref={searchInputRef}
              customStyle={{ marginVertical: 15 }}
              showTitle={false}
              setIsLoading={setIsLoading}
              isInnerSearch
              onQueryChange={setSearchQuery}
            />
          )}
          {isLoading || isAPILoading ? (
            <View style={styles.loaderContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
            </View>
          ) : (
            <>
              <FlatList
                data={activeTab === "search" ? searchResults : item?.alternatives}
                keyExtractor={(altItem) => altItem?.id}
                renderItem={renderDropdownItem}
                scrollEnabled={true}
                nestedScrollEnabled={true}
                showsVerticalScrollIndicator={true}
                ListEmptyComponent={
                  activeTab === "alternatives" ? (
                    <View style={{ paddingVertical: 10, alignItems: "center" }}>
                      <Typography.B2 style={{ ...styles.dropdownItemText, marginTop: 10 }}>
                        No Alternatives Found
                      </Typography.B2>
                    </View>
                  ) : activeTab === "search" && searchQuery.trim().length >= 2 && searchResults.length === 0 ? (
                    <View style={{ paddingVertical: 10, alignItems: "center" }}>
                      <Typography.B2 style={{ ...styles.dropdownItemText, marginTop: 10 }}>No food found</Typography.B2>
                    </View>
                  ) : null
                }
              />
              {activeTab === "search" && searchQuery.trim().length >= 2 ? (
                <TouchableOpacity
                  onPress={() => {
                    onAddNew();
                    searchInputRef.current?.reset();
                    setSearchQuery("");
                    setActiveTab(null);
                  }}
                >
                  <View style={styles.searchAddItemView}>
                    <Typography.B2 style={Common.textBold}>Add new</Typography.B2>
                    <Icons.CirclePlus
                      stroke={variant === "dark" ? colors.white : colors.textPrimary}
                      width={15}
                      height={15}
                    />
                  </View>
                </TouchableOpacity>
              ) : (
                <></>
              )}
            </>
          )}
        </View>
      )}
    </Animated.View>
  );
};

export default AIListCard;
