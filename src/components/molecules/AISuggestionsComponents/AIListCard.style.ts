/** @format */

import { Fonts } from "@/constants";
import { Theme } from "@/types/theme/theme";
import { ScaledSheet, ms } from "react-native-size-matters";

const getAIListCardStyles = (theme: Theme) =>
  ScaledSheet.create({
    cardWrapper: {
      marginBottom: ms(16),
    },
    topRow: {
      backgroundColor: theme.colors.aiCard,
      paddingVertical: ms(10),
      paddingHorizontal: ms(16),
      borderRadius: ms(8),
      flexDirection: "row",
      alignItems: "flex-start",
      justifyContent: "space-between",
    },
    foodNameText: {
      color: theme.colors.textPrimary,
      fontSize: ms(17),
      lineHeight: ms(19),
      fontFamily: Fonts.RALEWAY_BOLD,
      marginRight: ms(8),
      maxWidth: 250,
    },
    trashButton: {
      width: ms(24),
      height: ms(24),
      borderRadius: ms(12),
      justifyContent: "center",
      alignItems: "center",
      marginTop: ms(2),
    },
    bottomContainer: {
      flex: 1,
      flexDirection: "row",
      paddingVertical: ms(12),
      paddingHorizontal: ms(16),
      backgroundColor: theme.colors.aiCardBottom,
      borderBottomWidth: 1,
      borderLeftWidth: 1,
      borderRightWidth: 1,
      borderRightColor: theme.colors.aiCardBottomContainer,
      borderBottomColor: theme.colors.aiCardBottomContainer,
      borderLeftColor: theme.colors.aiCardBottomContainer,
      borderBottomEndRadius: ms(8),
      borderBottomStartRadius: ms(8),
      marginTop: -8,
      marginBottom: -5,
      zIndex: 10,
    },
    bottomToggleButtons: {
      flex: 1,
      flexDirection: "row",
      backgroundColor: theme.colors.app_bg,
      paddingVertical: ms(10),
      paddingHorizontal: ms(20),
      justifyContent: "center",
      alignItems: "center",
    },
    bottonToggleButtonsText: {
      color: theme.colors.textPrimary,
      fontSize: ms(15),
      lineHeight: ms(15),
      fontFamily: Fonts.RALEWAY_BOLD,
      marginLeft: ms(10),
    },
    altButton: {
      borderBottomStartRadius: ms(6),
      borderTopStartRadius: ms(6),
    },
    searchButton: {
      borderBottomEndRadius: ms(6),
      borderTopEndRadius: ms(6),
      borderLeftColor: theme.colors.aiBottomButtonsSeparator,
      borderLeftWidth: 1,
    },
    dropdownList: {
      backgroundColor: theme.colors.aiCard,
      borderLeftWidth: 1,
      borderRightWidth: 1,
      borderBottomWidth: 1,
      borderLeftColor: theme.colors.aiCardBottomContainer,
      borderRightColor: theme.colors.aiCardBottomContainer,
      borderBottomColor: theme.colors.aiCardBottomContainer,
      borderBottomEndRadius: ms(8),
      borderBottomStartRadius: ms(8),
      overflow: "hidden",
      paddingHorizontal: ms(16),
    },
    dropdownItem: {
      alignItems: "flex-start",
      paddingVertical: ms(8),
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.mediumGray,
    },
    dropdownItemText: {
      color: theme.colors.textPrimary,
      fontSize: ms(15),
      fontFamily: Fonts.RALEWAY_BOLD,
      lineHeight: ms(15),
    },
    activeTab: {
      backgroundColor: theme.colors.primary,
    },
    activeTabText: {
      color: theme.colors.white,
    },
    searchAddItemView: {
      justifyContent: "space-between",
      alignItems: "center",
      flexDirection: "row",
      paddingBottom: ms(26),
      paddingTop: ms(12),
    },

    loaderContainer: {
      justifyContent: "center",
      alignItems: "center",
      height: ms(100),
    },
    swappedTop: { borderColor: theme.colors.yellow, borderWidth: 3 },
    swappedBottom: {
      borderColor: theme.colors.yellow,
      borderBottomColor: theme.colors.yellow,
      borderRightColor: theme.colors.yellow,
      borderLeftColor: theme.colors.yellow,
      borderBottomWidth: 3,
      borderRightWidth: 3,
      borderLeftWidth: 3,
    },
  });

export default getAIListCardStyles;
