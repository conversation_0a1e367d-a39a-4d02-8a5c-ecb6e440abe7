import React from "react";
import { ViewStyle, TextStyle, Pressable } from "react-native";
import Animated from "react-native-reanimated";
import RNCheckbox from "@/components/atoms/RNCheckbox/RNCheckbox";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import getAnimatedSelectorStyle from "./AnimatedSelectors.styles";
import { useTheme } from "@/theme";
import { useAnimatedBorderScale } from "@/hooks/onboarding/useRoleSelectAnimations";

export interface IOptions {
  id: number;
  question: string;
}

interface AnimatedSelectorProps {
  selectedItems: IOptions[];
  onToggle: (item: IOptions) => void;
  containerStyle?: ViewStyle;
  labelStyle?: TextStyle;
  item: IOptions;
}

const IntentionSelector: React.FC<AnimatedSelectorProps> = ({
  selectedItems,
  onToggle,
  containerStyle,
  labelStyle,
  item,
}) => {
  const styles: any = useDynamicStyles(getAnimatedSelectorStyle);
  const { colors } = useTheme();

  const isSelected = selectedItems.some((selected) => selected.id === item.id); 

  const { animatedBorderStyle, animatedScaleStyle } = useAnimatedBorderScale({
    isActive: isSelected,
    activeColor: colors.yellow,
    inactiveColor: colors.gray,
    scaleFactor: 0.95,
  });

  const handlePress = () => {
    onToggle(item);
  };

  return (
    <Pressable onPress={handlePress}>
      <Animated.View
        style={[styles.container, animatedBorderStyle, containerStyle]}
      >
        <RNCheckbox
          value={isSelected}
          onSelect={handlePress}
          checkedfillColor={colors.yellow}
          checkboxUnCheckedFillColor={colors.gray}
        />
        <Animated.Text
          numberOfLines={2}
          style={[styles.label, animatedScaleStyle, labelStyle]}
        >
          {item?.question}
        </Animated.Text>
      </Animated.View>
    </Pressable>
  );
};

export default IntentionSelector;
