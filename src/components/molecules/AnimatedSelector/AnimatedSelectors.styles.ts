import { Fonts } from "@/constants";
import { ms, ScaledSheet } from "react-native-size-matters";

const getAnimatedSelectorStyle = (theme: { colors: { textPrimary: any } }) =>
  ScaledSheet.create({
    container: {
      flexDirection: "row",
      alignItems: "center",
      borderWidth: ms(2),
      borderRadius: ms(12),
      paddingHorizontal: ms(20),
    },
    label: {
      fontSize: ms(12),
      color: theme.colors.textPrimary,
      fontFamily: Fonts.RALEWAY_BOLD,
      lineHeight: ms(14),
    },
    roleLabels: {
      fontFamily: Fonts.RALEWAY_BOLD,
      fontSize: ms(22)
    },
    roleContainer: {
      flexDirection: "row",
      alignItems: "center",
      borderRadius: ms(16),
      paddingHorizontal: ms(20),
      marginTop:ms(55)
    },
    centerContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      flex: 1,
      marginHorizontal:ms(5),
      
    },
    leftLabel:{
      marginLeft: ms(20)
    },
    rightLabel:{
     marginLeft: ms(-5)
      
    }
  });

export default getAnimatedSelectorStyle;
