import { Theme } from "@/types/theme/theme";
import { ScaledSheet, ms } from "react-native-size-matters";

const getTaskDashboardCardStyles = (theme:Theme) => ScaledSheet.create({
  container: {
    padding: ms(16),
    borderRadius: ms(12),
    backgroundColor: theme.colors.tile_bg,
    marginBottom: ms(10),
  },
  title: {
    color: theme.colors.textPrimary,
    marginBottom: ms(12),
  },
  sectionTitle: {
    color: theme.colors.textPrimary,
    marginBottom: ms(8),
  },
  taskRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: ms(8),
    marginLeft: ms(20),
  },
  iconCircle: {
    width: ms(21),
    height: ms(21),
    borderRadius: ms(20),
    backgroundColor: theme.colors.tertiary,
    justifyContent: "center",
    alignItems: "center",
  },
  pillIcon: {
    width: ms(15),
    height: ms(10),
    resizeMode: "contain",
  },
  formulaIcon: {
    width: ms(24),
    height: ms(24),
  },
  taskInfo: {
    flex: 0.7,
    marginLeft: ms(12),
  },
  taskName: {
    color: theme.colors.textPrimary,
  },
  taskDetails: {
    flex: 1,
    marginHorizontal: ms(12),
  },
  taskDose: {
    marginBottom: ms(4),
  },
  taskTime: {
    color: theme.colors.textPrimary
  },
  taskStatus: {
    alignItems: "center",
    justifyContent: "center",
  },
  horizontalLine: {
    height: ms(1),
    backgroundColor: theme.colors.pheGray,
    marginVertical: ms(8),
  },
  lastTaskLine: {
    height: ms(4), // Thicker line for the last task
    backgroundColor: theme.colors.pheGray, // Use primary color or any desired color
    marginVertical: ms(8),
  },
  addButton: {
    borderRadius: ms(12),
    borderWidth: ms(1),
    borderColor: theme.colors.primary,
    backgroundColor: `${theme.colors.primary}22`, // Primary color with 60% opacity
    alignItems: "center",
    justifyContent: "center",
    width: ms(285),
    height: ms(28),
  },
  addButtonContent: {
    flexDirection: "row", // Arrange icon and text in a row
    alignItems: "center", // Vertically center the content
  },
  addButtonIcon: {
    marginRight: ms(8), // Add some spacing between the icon and text
  },
  addButtonText: {
    color: theme.colors.textPrimary, // Pure white text
  },
  noDataText: {
    color: theme.colors.mediumGray,
    marginBottom: ms(10),
  },
  spinner: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: ms(20), // Adjust the margin to fit your layout
  },
});

export default getTaskDashboardCardStyles;
