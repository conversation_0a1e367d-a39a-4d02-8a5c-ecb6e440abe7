import dayjs from "dayjs";
import { useTheme } from "@/theme";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import { ms } from "react-native-size-matters";
import React, { useState, useEffect } from "react";
import { useNavigation } from "@react-navigation/native";
import { View, TouchableOpacity, ActivityIndicator } from "react-native";

import {
  selectTaskLoading,
  updateTaskStatus,
} from "@/store/slices/dashboardSlice";
import throttle from "lodash/throttle";
import { config } from "@/theme/_config";
import Common from "@/theme/common.style";
import isBetween from "dayjs/plugin/isBetween";
import { store, useAppSelector } from "@/store";
import { TIME_SLOT } from "@/constants/timeSlots";
import Icons from "@/theme/assets/images/svgs/icons";
import { Card, Typography } from "@/components/atoms";
import isSameOrAfter from "dayjs/plugin/isSameOrAfter";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
import { renderMedicationDosage } from "@/utils/helpers";
import customParseFormat from "dayjs/plugin/customParseFormat";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import getTaskDashboardCardStyles from "./TaskDashboardCard.styles";
import { Task } from "@/types/components/molecules/TaskDashboardCard";
import { useAnalytics, analyticsEventType } from "@/hooks/useAnalytics";
import { selectTask } from "@/store/slices/taskManager/taskManager.slice";

dayjs.extend(utc);
dayjs.extend(customParseFormat);
dayjs.extend(isBetween);
dayjs.extend(timezone);
dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

interface TaskDashboardCardProps {
  tasks: Task[];
  onTaskToggle?: (updatedTask: Task) => void;
  selectedDate: Date;
}

const classifyTasksByPeriod = (tasks: Task[]) => {
  const sortByTime = (taskList: Task[]) =>
    taskList.sort((a, b) =>
      dayjs(a.intakeTime, "HH:mm:ss").diff(dayjs(b.intakeTime, "HH:mm:ss"))
    );

  return {
    morning: sortByTime(
      tasks.filter((task) => {
        const taskTime = dayjs(task.intakeTime, "HH:mm:ss");
        const startTime = dayjs(
          taskTime.format("YYYY-MM-DD") + " " + TIME_SLOT.morning.startTime,
          "YYYY-MM-DD HH:mm:ss"
        );
        const endTime = dayjs(
          taskTime.format("YYYY-MM-DD") + " " + TIME_SLOT.morning.endTime,
          "YYYY-MM-DD HH:mm:ss"
        );
        return (
          taskTime.isSameOrAfter(startTime) && taskTime.isSameOrBefore(endTime)
        );
      })
    ),
    afternoon: sortByTime(
      tasks.filter((task) => {
        const taskTime = dayjs(task.intakeTime, "HH:mm:ss");
        const startTime = dayjs(
          taskTime.format("YYYY-MM-DD") + " " + TIME_SLOT.afternoon.startTime,
          "YYYY-MM-DD HH:mm:ss"
        );
        const endTime = dayjs(
          taskTime.format("YYYY-MM-DD") + " " + TIME_SLOT.afternoon.endTime,
          "YYYY-MM-DD HH:mm:ss"
        );
        return (
          taskTime.isSameOrAfter(startTime) && taskTime.isSameOrBefore(endTime)
        );
      })
    ),
    evening: sortByTime(
      tasks.filter((task) => {
        const taskTime = dayjs(task.intakeTime, "HH:mm:ss");
        const startTime = dayjs(
          taskTime.format("YYYY-MM-DD") + " " + TIME_SLOT.evening.startTime,
          "YYYY-MM-DD HH:mm:ss"
        );
        const endTime = dayjs(
          taskTime.format("YYYY-MM-DD") + " " + TIME_SLOT.evening.endTime,
          "YYYY-MM-DD HH:mm:ss"
        );
        return (
          taskTime.isSameOrAfter(startTime) && taskTime.isSameOrBefore(endTime)
        );
      })
    ),
  };
};

const TaskDashboardCard: React.FC<TaskDashboardCardProps> = ({
  tasks = [],
  onTaskToggle,
  selectedDate,
}) => {
  const { colors } = useTheme();
  const navigation = useNavigation();
  const [localTasks, setLocalTasks] = useState<Task[]>(tasks);
  const { setAnalyticsEvent } = useAnalytics();
  const loading = useAppSelector(selectTaskLoading);
  const styles: any = useDynamicStyles(getTaskDashboardCardStyles);

  const { categories, formulaUnitList } = useAppSelector(selectTask);

  useEffect(() => {
    // Update localTasks only if tasks has a valid value
    if (tasks && tasks.length > 0) {
      setLocalTasks(tasks);
    }
  }, [tasks]);

  const filteredTasks = localTasks.filter((task) => {
    const fromDate = dayjs(task.taskDate).startOf("day");
    const toDate = task.toDate ? dayjs(task.toDate).endOf("day") : null;
    const selected = dayjs(selectedDate).startOf("day");
    return (
      (selected.isAfter(fromDate) && (!toDate || selected.isBefore(toDate))) || // If no toDate, assume ongoing task
      selected.isSame(fromDate, "day") ||
      (toDate && selected.isSame(toDate, "day"))
    );
  });

  const { morning, afternoon, evening } = classifyTasksByPeriod(filteredTasks);

  const handleTaskToggle = throttle(
    async (task: Task) => {
      try {
        // Dispatch the `updateTaskStatus` action
        await store.dispatch(
          updateTaskStatus({
            taskId: task.taskId ?? 0, // Send 0 if taskId is null
            patientRoutineId: task.id,
            actualDate: task.taskDate, // Ensure taskDate is in ISO format
            isCompleted: !task.isCompleted,
          })
        );

        // Call the `onTaskToggle` function if provided
        if (onTaskToggle) {
          const updatedTask = { ...task, isCompleted: !task.isCompleted };
          onTaskToggle(updatedTask);
        }

        // Update the task's local state after API call succeeds
        const updatedTasks = localTasks.map((t) =>
          t.id === task.id ? { ...t, isCompleted: !t.isCompleted } : t
        );
        setLocalTasks(updatedTasks);
        setAnalyticsEvent(analyticsEventType.custom, {
          id: task.id,
          event: "dashboard_task_complete",
          item_id: "dashboard_task_complete",
          action: "User ticks task",
        });
      } catch (error) {
        console.error("Failed to update task completion status:", error);
      }
    },
    1000, // 1 second throttle
    { leading: true, trailing: false } // Invoke immediately, and ignore trailing calls
  );

  const getHeaderTitle = (): string => {
    const today = dayjs();
    const selectedDateObj = dayjs(selectedDate); // Parse selectedDate with dayjs

    if (selectedDateObj.isSame(today, "day")) {
      return "Today's tasks";
    }

    return `${selectedDateObj.format("dddd")}'s tasks`; // Format selectedDate with day name
  };

  const renderTask = (
    task: Task,
    index: number,
    tasksInSection: Task[],
    isLastSectionTask: boolean
  ) => {
    const isLastTask = index === tasksInSection.length - 1 && isLastSectionTask;
    return (
      <View key={task.id}>
        <View style={styles.taskRow}>
          {task.isFormula ? (
            <Icons.FormulaBottle
              style={styles.formulaIcon}
              fill={colors?.textPrimary}
            />
          ) : (
            colors.textPrimary === "#FFFFFF" ?
              <Icons.MedPillIcon
                width={ms(20)}
                height={ms(20)}
                style={styles.formulaIcon}
                color={colors?.textPrimary}
              />
              :
              <Icons.MedPillDark
                width={ms(20)}
                height={ms(20)}
                style={styles.formulaIcon}
                color={colors?.textPrimary}
              />
          )}

          <View style={styles.taskInfo}>
            <Typography.B1 style={[styles.taskName, Common.textBold]}>
              {task.medicineName}
            </Typography.B1>
          </View>

          <View style={styles.taskDetails}>
            <Typography.B3 style={[styles.taskTime, Common.textBold]}>
              {renderMedicationDosage(
                task?.patientRoutineDetailList ?? [],
                categories,
                task.isFormula,
                false,
                task.formulaUnitId,
                formulaUnitList
              )}
              {"\n"}
              {dayjs(task.intakeTime, "HH:mm:ss").format("h:mm A")}
            </Typography.B3>
          </View>
          <TouchableOpacity
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            style={styles.taskStatus}
            onPress={() => handleTaskToggle(task)}
          >
            {task.isCompleted ? (
              <Icons.Check />
            ) : (
              <Icons.UnCheck color={colors.textPrimary} />
            )}
          </TouchableOpacity>
        </View>
        {/* Add a bulky line for the last task */}
        <View
          style={
            isLastTask
              ? styles.lastTaskLine // Bulky line for the last task
              : styles.horizontalLine // Regular line
          }
        />
      </View>
    );
  };
  const renderSection = (
    title: string,
    sectionTasks: Task[],
    isLastSection: boolean
  ) => {
    return (
      sectionTasks.length > 0 && (
        <View>
          <Typography.B1 style={styles.sectionTitle}>{title}</Typography.B1>
          {sectionTasks.map((task, index) =>
            renderTask(task, index, sectionTasks, isLastSection)
          )}
        </View>
      )
    );
  };

  return (
    <Card style={styles.container}>
      <Typography.H3 style={[styles.title, Common.textBold]}>
        {getHeaderTitle()}
      </Typography.H3>

      {loading ? (
        <>
          <ActivityIndicator
            size="large"
            color={config.colors.primary}
            style={styles.spinner}
          />
        </>
      ) : (
        <>
          {tasks.length === 0 ? (
            <></>
          ) : (
            <View>
              {/* Morning Section */}
              {renderSection(
                "Morning",
                morning,
                afternoon.length === 0 && evening.length === 0
              )}
              {/* Afternoon Section */}
              {renderSection("Afternoon", afternoon, evening.length === 0)}

              {/* Evening Section */}
              {renderSection("Evening", evening, true)}
            </View>
          )}
        </>
      )}

      {/* Add Button */}
      <TouchableOpacity
        style={styles.addButton}
        onPress={() => navigation.navigate('Tasks' as never)}
      >
        <View style={styles.addButtonContent}>
          <Icons.Plus
            style={styles.addButtonIcon}
            color={styles.addButtonText.color}
          />
          <Typography.B3 style={[styles.addButtonText, Common.textBold]}>
            Add Medication or Formula to Your Routine
          </Typography.B3>
        </View>
      </TouchableOpacity>
    </Card>
  );
};

export default TaskDashboardCard;
