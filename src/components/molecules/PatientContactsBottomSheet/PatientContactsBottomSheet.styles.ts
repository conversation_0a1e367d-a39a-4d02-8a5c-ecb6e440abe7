import { ms, ScaledSheet } from "react-native-size-matters";
import { config } from "@/theme/_config";

const styles = ScaledSheet.create({
  scrollContainer: {
    paddingBottom: ms(20),
    flexGrow: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: ms(20),
  },
  title: {
    textAlign: "center",
    flex: 1,
    marginVertical: ms(10),
  },
  closeButton: {
    color: config.colors.white,
    fontSize: ms(16),
  },
  label: {
    fontSize: ms(14),
    marginBottom: ms(5),
  },
  buttonContainer: {
    marginTop: ms(20),
    paddingHorizontal: ms(10),
  },
  outlineButton: {
    borderColor: config.colors.delete,
    paddingHorizontal: ms(20),
    borderRadius: 16,
    alignItems: "center",
    justifyContent: "center",
    marginVertical: ms(10),
    height: ms(47),
  },
  errorText: {
    color: config.colors.red,
    fontSize: ms(12),
    marginBottom: ms(2.5),
    marginTop: ms(2),
  },
});

export default styles;
