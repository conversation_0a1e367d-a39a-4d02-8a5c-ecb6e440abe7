import React, { useEffect, useState } from "react";
import {
  View,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  ScrollView,
} from "react-native";
import styles from "./PatientContactsBottomSheet.styles";
import BottomSheetWrapper from "@/components/atoms/BottomSheetWrapper/BottomSheetWrapper";
import CustomTextInput from "@/components/atoms/CustomTextInput/CustomTextInput";
import { Typography, Button } from "@/components/atoms";
import Common from "@/theme/common.style";
import { ms } from "react-native-size-matters";
import { validateEmail, validatePhoneNumber } from "@/utils/helpers";
import { PatientContact } from "@/types/schemas/patientContact";
import GenericModal from "../GenericModal/GenericModal";

interface PatientContactsBottomSheetProps {
  isVisible: boolean;
  onClose: () => void;
  onDelete: () => void;
  onConfirmDelete: () => void;
  onToggleDeleteModal: () => void;
  showDeleteModal: boolean;
  mode: "add" | "edit";
  onSave: (data: PatientContact) => void;
  selectedContact?: PatientContact;
}

const PatientContactsBottomSheet: React.FC<PatientContactsBottomSheetProps> = ({
  isVisible,
  onClose,
  onDelete,
  onConfirmDelete,
  onToggleDeleteModal,
  showDeleteModal,
  mode,
  onSave,
  selectedContact,
}) => {
  const [name, setName] = useState("");
  const [role, setRole] = useState("");
  const [emailAddress, setEmailAddress] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");

  const [errors, setErrors] = useState({
    name: "",
    emailAddress: "",
    phoneNumber: "",
  });

  useEffect(() => {
    if (mode === "edit" && selectedContact) {
      setName(selectedContact.name);
      setRole(selectedContact.role);
      setEmailAddress(selectedContact.emailAddress || "");
      setPhoneNumber(selectedContact.phoneNumber);
    } else if (mode === "add") {
      resetForm();
    }
  }, [isVisible, mode, selectedContact]);

  const validateFields = () => {
    let hasError = false;
    const newErrors = { name: "", emailAddress: "", phoneNumber: "" };

    // Validate name
    if (name.trim().length < 3) {
      newErrors.name = "Name should have at least 3 characters.";
      hasError = true;
    }

    // Check if both email and phone are empty
    const isEmailEmpty = emailAddress.trim() === "";
    const isPhoneEmpty = phoneNumber.trim() === "";

    if (isEmailEmpty && isPhoneEmpty) {
      newErrors.emailAddress = "Provide at least email or phone number.";
      newErrors.phoneNumber = "Provide at least email or phone number.";
      hasError = true;
    } else {
      // Validate email format if provided
      if (!isEmailEmpty) {
        const emailError = validateEmail(emailAddress);
        if (emailError) {
          newErrors.emailAddress = emailError;
          hasError = true;
        }
      }

      // Validate phone format if provided
      if (!isPhoneEmpty) {
        const phoneError = validatePhoneNumber(phoneNumber);
        if (phoneError) {
          newErrors.phoneNumber = phoneError;
          hasError = true;
        }
      }
    }

    setErrors(newErrors);
    return !hasError;
  };

  const handleSave = () => {
    if (validateFields()) {
      onSave({
        contactId: selectedContact?.contactId ?? 0,
        patientId: 0,
        name,
        role,
        emailAddress,
        phoneNumber,
        address: "",
        patient: null,
        createdBy: 0,
        dateAdded: new Date().toISOString(),
        dateModified: null,
        modifiedBy: null,
        statusId: 1,
        status: null,
      });
      onClose();
      resetForm();
    }
  };

  const resetForm = () => {
    setName("");
    setRole("");
    setEmailAddress("");
    setPhoneNumber("");
    setErrors({ name: "", emailAddress: "", phoneNumber: "" });
  };

  return (
    <BottomSheetWrapper
      isVisible={isVisible}
      onClose={() => {
        onClose();
        resetForm();
      }}
      height={ms(600)}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : undefined}
        style={{ flex: 1 }}
        keyboardVerticalOffset={Platform.OS === "ios" ? ms(120) : 0}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <ScrollView
            contentContainerStyle={styles.scrollContainer}
            keyboardShouldPersistTaps="handled"
          >
            {/* Header */}
            <View style={styles.header}>
              <Typography.H3 style={[styles.title, Common.textBold]}>
                {mode === "add" ? "Add New Contact" : "Update Contact"}
              </Typography.H3>
            </View>

            {/* Name */}
            <View>
              <Typography.B1 style={styles.label}>Name</Typography.B1>
              <CustomTextInput
                placeholder="Enter name here"
                value={name}
                onChangeText={(text) => {
                  const capitalized =
                    text.charAt(0).toUpperCase() + text.slice(1);
                  setName(capitalized);
                  setErrors((prev) => ({ ...prev, name: "" }));
                }}
              />
              {errors.name && (
                <Typography.B3 style={styles.errorText}>
                  {errors.name}
                </Typography.B3>
              )}
            </View>

            {/* Role */}
            <View>
              <Typography.B1 style={styles.label}>Role</Typography.B1>
              <CustomTextInput
                placeholder="Enter role here"
                value={role}
                onChangeText={(text) => {
                  const capitalized =
                    text.charAt(0).toUpperCase() + text.slice(1);
                  setRole(capitalized);
                }}
              />
            </View>

            {/* Phone */}
            <View>
              <Typography.B1 style={styles.label}>Phone Number</Typography.B1>
              <CustomTextInput
                placeholder="Enter phone number"
                value={phoneNumber}
                onChangeText={(text) => {
                  setPhoneNumber(text);

                  setErrors((prev) => {
                    const updatedErrors = { ...prev };

                    // Clear "at least one required" errors if either field has value
                    if (emailAddress.trim() || text.trim()) {
                      updatedErrors.emailAddress = "";
                      updatedErrors.phoneNumber = "";
                    }

                    // But still apply phone format validation if phone has value
                    if (text.trim()) {
                      updatedErrors.phoneNumber = validatePhoneNumber(text);
                    }

                    return updatedErrors;
                  });
                }}
                keyboardType="phone-pad"
                maxLength={20}
              />
              {errors.phoneNumber && (
                <Typography.B3 style={styles.errorText}>
                  {errors.phoneNumber}
                </Typography.B3>
              )}
            </View>

            {/* Email */}
            <View>
              <Typography.B1 style={styles.label}>Email Address</Typography.B1>
              <CustomTextInput
                placeholder="Enter email address"
                value={emailAddress}
                onChangeText={(text) => {
                  setEmailAddress(text);

                  // Clear errors if at least one field has value
                  setErrors((prev) => {
                    const updatedErrors = { ...prev };
                    if (text.trim() || phoneNumber.trim()) {
                      updatedErrors.emailAddress = "";
                      updatedErrors.phoneNumber = "";
                    }
                    return updatedErrors;
                  });
                }}
                keyboardType="email-address"
              />
              {errors.emailAddress && (
                <Typography.B3 style={styles.errorText}>
                  {errors.emailAddress}
                </Typography.B3>
              )}
            </View>

            {/* Buttons */}
            <View style={styles.buttonContainer}>
              {mode === "edit" && selectedContact && (
                <Button.Outline
                  onPress={() => onDelete(selectedContact.contactId)}
                  style={styles.outlineButton}
                >
                  <Typography.B1 style={[Common.textBold]}>
                    Delete
                  </Typography.B1>
                </Button.Outline>
              )}

              <Button.Main onPress={handleSave}>
                <Typography.B1 style={[Common.textBold, { color: "white" }]}>
                  {mode === "add" ? "Save Contact" : "Update Contact"}
                </Typography.B1>
              </Button.Main>
            </View>
          </ScrollView>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>

      <GenericModal
        isVisible={showDeleteModal}
        onClose={onToggleDeleteModal}
        onConfirm={onConfirmDelete}
        headerText="Confirm Delete"
        bodyText="Are you sure you want to delete this contact?"
        confirmText="Yes"
        closeText="No"
      />
    </BottomSheetWrapper>
  );
};

export default PatientContactsBottomSheet;
