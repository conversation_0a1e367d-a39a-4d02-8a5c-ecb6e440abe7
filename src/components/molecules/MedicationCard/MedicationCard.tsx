import dayjs from "dayjs";
import React, { FC } from 'react';
import { View } from 'react-native';
import { ms } from 'react-native-size-matters';

import { useTheme } from "@/theme";
import { useAppSelector } from "@/store";
import Icons from '@/theme/assets/images/svgs/icons';
import { getPeriodOfDay } from "@/utils/taskManager";
import { renderMedicationDosage } from "@/utils/helpers";
import { getMedicationCardStyles } from './MedicationCard.styles';
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { Button, TaskBadge, Typography } from '@/components/atoms';
import { selectTask } from "@/store/slices/taskManager/taskManager.slice";

export type Medication = {
  id: number;
  iconId: number;
  toDate: string | null;
  upcoming: string;
  fromDate: string;
  intakeTime: string;
  medicineName: string;
  activeIngredient: string;
  period: 'Morning' | 'Afternoon' | 'Evening';
  onPress?: () => void
  medicationDetails?: any[]
};

export interface MedicationCardProps extends Medication { }

const MedicationCard: FC<MedicationCardProps> = ({
  medicineName,
  upcoming,
  medicationDetails,
  onPress
}) => {
  const localPeriod = getPeriodOfDay(dayjs(upcoming).format('HH:mm:ss'));
  const styles: any = useDynamicStyles(getMedicationCardStyles);
  const { colors, variant } = useTheme();

  const { categories, formulaUnitList } = useAppSelector(selectTask);

  return (
    <View style={styles.cardContainer}>
      <View style={styles.leftContainer}>
        {colors.textPrimary === '#FFFFFF' ?
          <Icons.MedPillIcon height={ms(24)} width={ms(24)} />
          :
          <Icons.MedPillDark height={ms(24)} width={ms(24)} />}
      </View>

      <View style={styles.centerContainer}>
        <View>
          <Typography.H2 style={styles.title}>{medicineName}</Typography.H2>
          <Typography.B3 style={styles.doseText}>
          {renderMedicationDosage(medicationDetails ?? [], categories, false, true, undefined, formulaUnitList)}{"\n"}
          </Typography.B3>
        </View>

        <View style={styles.dateTimeContainer}>
          <View style={styles.rowContainer}>
            <Icons.Calendar fill={colors.textPrimary} />
            <Typography.B3 style={styles.leftSpacing}>
              {dayjs(upcoming).format('MM-DD-YYYY')}
            </Typography.B3>
          </View>

          <View style={styles.rowContainer}>
            {variant === 'dark' ?
              <Icons.TaskClock />
              :
              <Icons.TaskClockDark />}
            <Typography.B3 style={styles.leftSpacing}>
              {dayjs(upcoming).format('hh:mm A')}
            </Typography.B3>
          </View>
        </View>
      </View>

      <View style={styles.rightContainer}>
        <View style={styles.gradientPill}>
          <TaskBadge>
            <Typography.B4 style={{ color: colors.white }}>{localPeriod}</Typography.B4>
          </TaskBadge>
        </View>

        <View style={styles.btnContainer}>
          <Button.BlackOutline style={styles.actionBtn} onPress={onPress}>
            <Icons.RightArrow color={colors.textPrimary} />
          </Button.BlackOutline>
        </View>
      </View>
    </View>
  );
};

export default MedicationCard;
