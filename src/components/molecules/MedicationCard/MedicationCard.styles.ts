import {
  ms,
  scale,
  ScaledSheet,
  verticalScale
} from 'react-native-size-matters';
import { Theme } from '@/types/theme/theme';

export const getMedicationCardStyles = (theme: Theme) => ScaledSheet.create({
  cardContainer: {
    backgroundColor: theme.colors.taskCardGray,
    paddingTop: verticalScale(18.5),
    paddingBottom: ms(8),
    paddingLeft: scale(4),
    paddingRight: -ms(1),
    borderRadius: scale(14),
    flexDirection: 'row',
  },
  pillStyle: { height: verticalScale(24) },
  title: { lineHeight: ms(24) },
  description: {
    marginTop: ms(6),
    marginBottom: ms(12)
  },
  leftContainer: {
    width: '15%',
    alignItems:'center'
  },
  centerContainer: {
    width: '60%'
  },
  dateTimeContainer: {
    marginBottom: 20
  },
  rightContainer: {
    width: '25%',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingRight: ms(12.5),
  },
  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  gradientPill: {
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'flex-end'
  },
  leftSpacing: { marginLeft: ms(10) },
  actionBtn: {
    borderRadius: ms(60),
    height: verticalScale(28),
    aspectRatio: 1
  },
  btnContainer: {
    alignSelf: 'flex-end',
  },
  doseText: {
    color: theme.colors.textPrimary,
  }
});
