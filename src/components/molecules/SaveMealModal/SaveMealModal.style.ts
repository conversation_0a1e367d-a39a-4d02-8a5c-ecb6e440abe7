import { ScaledSheet } from "react-native-size-matters";
;
import { Theme } from "@/types/theme/theme";

const getSaveMealStyle = (theme: Theme) => ScaledSheet.create({
  container: {
    paddingVertical: "10@vs",
    borderTopLeftRadius: "16@s",
    borderTopRightRadius: "16@s",
  },
  header: {
    marginBottom: "20@vs",
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  title: {
    fontSize: "18@s",
    color: theme.colors.textPrimary,
    textAlign: 'center'
  },
  inputContainer: {
    marginBottom: "150@vs",
  },
});

export default getSaveMealStyle;
