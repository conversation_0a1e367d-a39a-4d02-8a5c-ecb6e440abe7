import React, { useState } from "react";
import { Keyboard, TouchableWithoutFeedback, View } from "react-native";
import BottomSheetWrapper from "@/components/atoms/BottomSheetWrapper/BottomSheetWrapper";
import Typography from "@/components/atoms/Typography/Typography";
import CustomTextInput from "@/components/atoms/CustomTextInput/CustomTextInput";
import Button from "@/components/atoms/Button/Button";

import Common from "@/theme/common.style";
import { ms } from "react-native-size-matters";
import Loading from "@/components/atoms/Loading/Loading";
import { selectDietTrackerLoading } from "@/store/slices/dietTrackerSlice";
import { useAppSelector } from "@/store";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import getSaveMealStyle from "./SaveMealModal.style";

interface SaveMealModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSave: (mealName: string) => void;
}

const SaveMealModal: React.FC<SaveMealModalProps> = ({
  isVisible,
  onClose,
  onSave,
}) => {
  const [mealName, setMealName] = useState("");
  const styles:any = useDynamicStyles(getSaveMealStyle)
  const loading = useAppSelector(selectDietTrackerLoading);
  const handleSave = () => {
    if (!mealName.trim()) {
      return;
    }
    onSave(mealName);
    setMealName(""); // Reset input
  };

  return (
    <BottomSheetWrapper
      isVisible={isVisible}
      onClose={() => {
        setMealName(""); // Reset input when modal is closed
        onClose();
      }}
      height={ms(500)} // Custom height in pixels
    >
      <>
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.container}>
            {/* Header */}
            <View style={styles.header}>
              <Typography.H1 style={[Common.textBold, styles.title]}>
                Save as Meal
              </Typography.H1>
            </View>

            {/* Meal Name Input */}
            <View style={styles.inputContainer}>
              <Typography.B1>Meal Name</Typography.B1>
              <CustomTextInput
                placeholder="Enter your meal name here"
                value={mealName}
                onChangeText={setMealName}
              />
            </View>

            {/* Save Button */}
            <Button.Main onPress={handleSave}>
              <Typography.B1 style={[Common.textBold,{color: 'white'}]}>Save Meal</Typography.B1>
            </Button.Main>

          </View>
        </TouchableWithoutFeedback>
        {loading && <Loading />}
      </>
    </BottomSheetWrapper>
  );
};

export default SaveMealModal;
