import { ms, ScaledSheet, vs } from "react-native-size-matters";
import { Dimensions } from "react-native";
import { Theme } from "@/types/theme/theme";
import { SCREEN_HEIGHT } from "@/theme/_config";

const { width } = Dimensions.get("window");

const addMealStyles = (theme: Theme) => ScaledSheet.create({
  header: {
    alignItems: "center",
    marginBottom: ms(40),
  },
  title: {
    marginTop: ms(20),
    fontSize: ms(22),
    textAlign: 'center'
  },
  actionButtonsContainer: {
    flexDirection: "row",
    alignItems: "center",
    width: '100%',
    justifyContent: "space-between", // Ensures even spacing between buttons
    marginVertical: ms(10),
    columnGap: 8
  },
  actionButton: {
    alignItems: "center",
    justifyContent: "center",
    borderRadius: ms(16),
    height: ms(143), // Adjusted height for responsiveness
    width: width * 0.44, // Dynamically calculates width (e.g., 42% of screen width)
    padding: ms(10), // Adds internal padding
  },
  manualEntryColor: {
    backgroundColor: theme.colors.app_bg, // Custom background for manual entry
  },
  actionButtonText: {
    marginTop: ms(8),
    fontSize: ms(14),
    color: theme.colors.white,
    textAlign: "center", // Center-aligns text
  },
  foodsLabel: {
    fontSize: ms(16),
    color: theme.colors.textPrimary,
    marginBottom: ms(8),
  },
  separator: {
    marginBottom: ms(8),
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.mediumGray,
  },
  foodList: {
    paddingBottom: ms(80),
    paddingTop: ms(8),
  },
  searchResultsView: {
    top: ms(54),
    left: ms(0),
    right: ms(0),
    backgroundColor: theme.colors.dropDownGray,
    borderRadius: ms(8),
    zIndex: 999,
    position: 'absolute',
    borderWidth: 0.76,
    borderColor: theme.colors.mediumGray
  },
  searchResultsList: {
    flex: 1,
    maxHeight: vs(220),
    paddingHorizontal: ms(20),

  },
  searchAddItemView: {
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    paddingLeft: ms(16),
    paddingRight: ms(24),
    paddingBottom: ms(26),
    paddingTop: ms(12)
  },
  searchItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: vs(6.5),
    borderBottomWidth: 0.75,
    borderBottomColor: theme.colors.mediumGray,
  },
  searchItemTextContainer: {
    flex: 1,
    marginRight: ms(10),
  },
  searchItemRow: {
    justifyContent: "center",
    maxWidth: '100%',
    flex:1,
    },
  itemName: {
    fontSize: ms(14),
    marginBottom: ms(3)
  },
  itemDetails: {
    fontSize: ms(12),
  },
  addNewItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: vs(8),
    marginBottom: ms(10),
    borderTopWidth: 0.85,
    borderTopColor: theme.colors.mediumGray,
  },
  icon: {
    marginLeft: 5,
  },
  icon_marginRight_5: {
    marginRight: 5
  },
  reverse_zIndex_1: {
    zIndex: -1
  },
  relative_position: { position: "relative" },
  foodListView: {
    height: ms(280),
    gap: 8
  },
  gap_8: {
    marginVertical: ms(4)
  },
  loadingContainer: {
    flex: 1,
    height: ms(280),
    alignItems: "center",
    justifyContent: "center",
    marginTop: SCREEN_HEIGHT * 0.125
  },
});

export default addMealStyles;
