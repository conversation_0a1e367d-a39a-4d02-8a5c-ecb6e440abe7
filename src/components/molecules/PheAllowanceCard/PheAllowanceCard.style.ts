import { Fonts } from "@/constants";
import { Theme } from "@/types/theme/theme";

import { ms, ScaledSheet } from "react-native-size-matters";

const getPheAllowanceCardStyles = (theme: Theme) =>
  ScaledSheet.create({
    pheAllowanceView: {
      width: "100%",
      backgroundColor: theme.colors.tile_bg,
      borderRadius: "16@ms",
      overflow: "hidden",
    },
    row: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "flex-start",
      marginBottom: ms(5),
    },
    row2: {
      flexDirection: "row",
      alignItems: "center",
      columnGap: ms(15),
      marginTop: -ms(30),
    },
    headerText: {
      fontFamily: Fonts.RALEWAY_BOLD,
    },
    gap_8: {
      gap: ms(8),
    },
    dailypheallowance: {
      marginLeft: ms(15),
    },
    hexagonIcon: {
      marginTop: ms(4.8),
    },
    note: {
      marginTop: ms(16),
      color: theme.colors.textPrimaryYellow,
    },
    valueText: {
      fontSize: 22,
    },
  });

export default getPheAllowanceCardStyles;
