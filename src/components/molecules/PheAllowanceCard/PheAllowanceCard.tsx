import React from "react";
import { View } from "react-native";
import { Typography } from "@/components/atoms";
import Icons from "@/theme/assets/images/svgs/icons";
import Card from "@/components/atoms/Card/Card";
import Common from "@/theme/common.style";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import getPheAllowanceCardStyles from "./PheAllowanceCard.style";
import { useSelector } from "react-redux";
import { selectConsumptionUnit } from "@/store/slices/settingsSlice";

interface PheAllowanceCardProps {
  consumedPHELevel: string;
  dailyPHEAllowance: string;
}

const PheAllowanceCard: React.FC<PheAllowanceCardProps> = ({
  consumedPHELevel,
  dailyPHEAllowance,
}) => {
  const styles: any = useDynamicStyles(getPheAllowanceCardStyles);
  const consumptionType = useSelector(selectConsumptionUnit);

  return (
    <Card style={styles.pheAllowanceView}>
      <View style={styles.row}>
        <Typography.H4 style={Common.textBold}>
          {consumptionType === "Protein" ? "Protein" : "Phe"} Allowance
        </Typography.H4>
        <Icons.FoodHexagon style={styles.hexagonIcon} />
      </View>
      <View style={styles.row2}>
        <View style={styles.gap_8}>
          <Typography.H2 style={[styles.valueText, Common.textBold]}>
            {consumedPHELevel}
          </Typography.H2>
          <Typography.B3 style={Common.textBold}>
            Consumed {consumptionType === "Protein" ? "PRO" : "Phe"} Amount
          </Typography.B3>
        </View>
        <View style={styles.gap_8}>
          <Typography.H2 style={[styles.valueText, Common.textBold]}>
            {dailyPHEAllowance}
          </Typography.H2>
          <Typography.B3 style={Common.textBold}>
            Daily {consumptionType === "Protein" ? "PRO" : "Phe"} Allowance
          </Typography.B3>
        </View>
      </View>
    </Card>
  );
};

export default PheAllowanceCard;
