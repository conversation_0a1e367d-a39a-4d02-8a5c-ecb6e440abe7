import React from "react";
import { ms } from "react-native-size-matters";
import { View, TouchableOpacity, StyleSheet } from "react-native";
import { useTranslation } from "react-i18next";

import { useTheme } from "@/theme";
import { Fonts } from "@/constants";
import { Typography } from "@/components/atoms";
import Modal from "@/components/atoms/Modal/Modal";
import Icons from "@/theme/assets/images/svgs/icons";

interface SimplifiedDietInfoModalProps {
  isVisible: boolean;
  onClose: () => void;
}

const SimplifiedDietInfoModal: React.FC<SimplifiedDietInfoModalProps> = ({
  isVisible,
  onClose,
}) => {
  const { colors, variant } = useTheme();
  const { t } = useTranslation(['diet']);

  const styles = StyleSheet.create({
    container: {
      paddingHorizontal: ms(10),
      paddingVertical: ms(8),
    },
    header: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: ms(16),
    },
    title: {
      fontFamily: Fonts.RALEWAY_BOLD,
      color: colors.textPrimary,
    },
    content: {
      marginBottom: ms(16),
    },
    text: {
      fontSize: ms(12),
      lineHeight: ms(18),
    },
    modal: {
      borderRadius: ms(24),
      overflow: 'hidden',
      alignSelf: 'center',
      marginTop: ms(58),
      padding: ms(12),
      ...variant === 'light' ? {
        borderColor: "#F5F5F5",
        borderWidth: 1,
        elevation: 1
      } : {}
    },
    closeButtonContainer: {
      width: '100%',
      alignItems: 'flex-end',
      padding: ms(4),
    }
  });

  return (
    <Modal
      isVisible={isVisible}
      enableBlur
      onClose={onClose}
      hasNoOverlay
      style={styles.modal}>
      <View style={styles.closeButtonContainer}>
        <TouchableOpacity onPress={onClose}>
          <Icons.Close
            width={ms(18)}
            height={ms(18)}
            color={colors.textPrimary}
          />
        </TouchableOpacity>
      </View>
      <View style={styles.container}>
        <View style={styles.header}>
          <Typography.H4 style={styles.title}>{t('simplifiedDiet.title')}</Typography.H4>

        </View>
        <View style={styles.content}>
          <Typography.B3 style={styles.text}>
            {t('simplifiedDiet.description')}
          </Typography.B3>
        </View>
      </View>
    </Modal>
  );
};

export default SimplifiedDietInfoModal;
