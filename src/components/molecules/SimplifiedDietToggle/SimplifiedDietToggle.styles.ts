import { Fonts } from "@/constants";
import { Theme } from "@/types/theme/theme";
import { Platform } from "react-native";
import { ms, ScaledSheet } from "react-native-size-matters";

const getSimplifiedDietToggleStyles = (theme: Theme) => ScaledSheet.create({
  container: {
    backgroundColor: theme.colors.tile_bg,
    borderRadius: ms(10),
    marginBottom: ms(10),
    paddingVertical: ms(16),
    paddingHorizontal: ms(26),
  },
  headerContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: ms(12),
  },
  titleContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  title: {
    fontSize: ms(16),
    color: theme.colors.textPrimary,
    fontFamily: Fonts.RALEWAY_REGULAR,
    marginRight: ms(8),
  },
  toggle: {
    ...Platform.select({
      ios: {
        marginRight: ms(0),
      },
      android: {
        marginRight: ms(-10),
      }
    })
  },
  disclaimer: {
    color: theme.colors.textPrimary,
    fontFamily: Fonts.RALEWAY_REGULAR,
  }
});

export default getSimplifiedDietToggleStyles;
