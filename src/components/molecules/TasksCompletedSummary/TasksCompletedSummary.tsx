import React, { useState, useEffect } from "react";
import { View } from "react-native";
import styles from "./TasksCompletedSummary.styles";
import { Typography } from "@/components/atoms";
import ProfileHeader from "../ProfileHeader/ProfileHeader";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { TIME_SLOT } from "@/constants/timeSlots";
import { getPreSignedUrl } from "@/services/api/userAPI";
import Common from "@/theme/common.style";
import { useNavigation } from "@react-navigation/native";
import { TouchableOpacity } from "react-native-gesture-handler";
import { useTheme } from "@/theme";

// Define the props interface
interface TasksCompletedSummaryProps {
  percentage: number;
}

const TasksCompletedSummary: React.FC<TasksCompletedSummaryProps> = ({
  percentage,
}) => {
  const { user: profile } = useSelector((state: RootState) => state.user);
  const [defaultImage, setDefaultImage] = useState<string | undefined>(
    undefined
  );

  const navigation = useNavigation();
  const { colors } = useTheme();

  // Function to get the greeting based on the current time
  const getGreeting = () => {
    const currentTime = new Date();
    const currentHours = currentTime.getHours();
    const currentMinutes = currentTime.getMinutes();
    const currentSeconds = currentTime.getSeconds();

    // Convert current time into a format comparable with the time slots
    const currentTimeString = `${currentHours.toString().padStart(2, "0")}:${currentMinutes
      .toString()
      .padStart(2, "0")}:${currentSeconds.toString().padStart(2, "0")}`;

    if (
      currentTimeString >= TIME_SLOT.morning.startTime &&
      currentTimeString <= TIME_SLOT.morning.endTime
    ) {
      return "Good Morning";
    } else if (
      currentTimeString >= TIME_SLOT.afternoon.startTime &&
      currentTimeString <= TIME_SLOT.afternoon.endTime
    ) {
      return "Good Afternoon";
    } else if (
      currentTimeString >= TIME_SLOT.evening.startTime &&
      currentTimeString <= TIME_SLOT.evening.endTime
    ) {
      return "Good Evening";
    }
  };

  // Fetch signed image URL
  useEffect(() => {
    const signedImage = async () => {
      if (profile?.profilePictureUrl) {
        try {
          const response = await getPreSignedUrl(
            profile?.profilePictureUrl
          );

          setDefaultImage(response); // Set the fetched URL as the default image
        } catch (e) {
          console.error("Error fetching signed URL:", e);
          setDefaultImage(undefined); // Fallback to undefined in case of an error
        }
      }
    };

    signedImage();
  }, [profile?.profilePictureUrl]);

  return (
    <View style={styles.container}>
      {/* Left Section: Profile with Progress Bar */}
      <View style={styles.profileSection}>
        <TouchableOpacity
          onPress={() => navigation.navigate("UserProfile")}
          style={styles.avatarView}
        >
          <ProfileHeader
            imageUrl={defaultImage} // Use the signed image URL here
            percentage={percentage} // Dynamic percentage
            hasPercentage
          />
        </TouchableOpacity>
        <View style={styles.greetingMessage}>
          <Typography.B1>{getGreeting()}</Typography.B1>
          <Typography.H3
            numberOfLines={1}
            ellipsizeMode="tail"
            style={[Common.textBold, styles.taskTextView]}
          >
            {profile?.name}
          </Typography.H3>
        </View>
      </View>

      {/* Right Section: Tasks Completed */}
      <View style={styles.taskSection}>
        <View style={styles.verticalLine} />

        <View>
          <View style={[styles.taskCircle, { backgroundColor: colors.primary}]}>
            <Typography.B3 style={[Common.textBold, {color: colors.white}]}>{percentage}%</Typography.B3>
          </View>
          <Typography.B3 style={[styles.taskText, Common.textBold]}>
            {`Tasks \nCompleted`}
          </Typography.B3>
        </View>
      </View>
    </View >
  );
};

export default TasksCompletedSummary;
