import { ScaledSheet, ms } from "react-native-size-matters";
import { RFValue } from "react-native-responsive-fontsize";
import { Theme } from "@/types/theme/theme";
import { Fonts } from "@/constants";

const getAISuggestionItemStyles = (theme:Theme) => ScaledSheet.create({
  container: {
    paddingVertical: ms(10),
    paddingHorizontal: ms(20),
    backgroundColor: theme.colors.pheGray,
    borderRadius: ms(10),
    marginBottom: ms(10),
  },
  mealName: {
    fontSize: ms(17),
    fontWeight: "bold",
    marginBottom: ms(20),
    marginTop: ms(10),
    color: theme.colors.textPrimary,
  },
  inputContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    borderRadius: ms(8),
    borderWidth: ms(1),
    height: ms(40.5),
    borderColor: theme.colors.mediumGray,
    marginTop: ms(20),
    paddingLeft: ms(12),
  },
  nutrientsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
    marginBottom: ms(12),
  },
  nutrientBox: {
    alignItems: "center",
    flex: 1,
    marginVertical: ms(20),
  },
  nutrientValue: {
    color: theme.colors.textPrimaryYellow,
    fontSize: ms(20),
  },
  nutrientLabel: {
    color: theme.colors.mediumGray,
    marginTop: ms(4),
  },
  outlineButton: {
    borderColor: theme.colors.delete,
    paddingHorizontal: ms(20),
    borderRadius: 16,
    alignItems: "center",
    justifyContent: "center",
    marginVertical: ms(10),
    height: ms(47),
  },
  dropdownButton: {
    height: ms(38),
  },
  pheInputContent: {
    width: "100%",
    alignItems: "center",
    justifyContent: "space-between",
    borderRadius: ms(8),
    borderWidth: ms(1),
    height: ms(40.5),
    flexDirection: "row",
    borderColor: theme.colors.mediumGray,
    marginTop: ms(20),
    overflow:'hidden'
  },
  pheTextInputFocused: {
    borderColor: theme.colors.yellow,
    borderWidth: ms(1),
  },
  pheTextInput: {
    flex: 1,
    paddingLeft: ms(12),
    fontSize: RFValue(13.5),
    borderWidth: 0,

  },
  errorText: {
    color: theme.colors.red,
    marginTop: ms(10),
  },
  checkBoxlLabelText:{
    marginBottom: ms(0),
    fontSize: ms(14),
    fontFamily: Fonts.RALEWAY_MEDIUM
  },
  checkBoxText:{
    fontSize: ms(15)
  }
});

export default getAISuggestionItemStyles;
