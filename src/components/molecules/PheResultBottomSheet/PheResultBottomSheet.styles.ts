import { ms, ScaledSheet } from "react-native-size-matters";
import { Theme } from "@/types/theme/theme";

const getPheResultStyles = (theme:Theme) => ScaledSheet.create({
  title: {
    fontSize: ms(20),
    color: theme.colors.textPrimary,
    textAlign: "center",
    marginTop: ms(5),
    marginBottom: ms(30),
  },
  scrollContainer: {
    paddingBottom: ms(20),
    flex: 1
  },
  servingInputContent: {
    width: "100%",
    alignItems: "center",
    justifyContent: "space-between",
    borderRadius: ms(8),
    borderWidth: ms(1),
    height: ms(40.5),
    flexDirection: "row",
    borderColor: theme.colors.mediumGray,
    marginTop: ms(10),
  },
  errorText: {
    color: theme.colors.red,
    fontSize: ms(12),
    marginBottom: ms(2.5),
    marginTop: ms(2),
  },
  saveButtonContainer: {
    alignItems: "center",
    justifyContent: "center",
  },
  fullWidthDropdown: {
    width: "100%",
    borderWidth: 1,
    borderColor: theme.colors.mediumGray,
    backgroundColor: theme.colors.tile_bg,
    borderRadius: 8,
    padding: 10,
    marginTop: ms(5),
  },
  dropdownMenu: {
    borderRadius: 8,
    marginTop: 5,
    borderColor: theme.colors.mediumGray,
    borderWidth: 1,
  },
  calendarContainer: {
    marginVertical: ms(5),
    width: "100%",
  },
  scrollViewContent: {
    paddingBottom: "20@vs",
  },
  input: {
    marginVertical: ms(5),
  },
  outlineButton: {
    borderColor: theme.colors.delete,
    paddingHorizontal: ms(20),
    borderRadius: 16,
    alignItems: "center",
    justifyContent: "center",
    marginVertical: ms(10),
    height: ms(47),
  },

  buttonContainer: {
    flex: 0.135,
  },
});

export default getPheResultStyles;
