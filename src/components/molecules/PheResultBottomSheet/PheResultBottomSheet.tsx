import React, { useEffect, useState } from "react";
import { View, ScrollView } from "react-native";
import CustomTextInput from "@/components/atoms/CustomTextInput/CustomTextInput";
import Button from "@/components/atoms/Button/Button";
import Common from "@/theme/common.style";
import BottomSheetWrapper from "@/components/atoms/BottomSheetWrapper/BottomSheetWrapper";
import Icons from "@/theme/assets/images/svgs/icons";
import CustomCalendar from "@/components/atoms/CustomCalendar/CustomCalendar";
import { useSelector } from "react-redux";
import { selectLabLoading } from "@/store/slices/labsSlice";
import { validateNonZeroNumericInput } from "@/utils/helpers";
import { LabPayload } from "@/types/schemas/labs";
import { formatDateMMDDYYYY } from "@/utils/helpers";
import { Dropdown, Loader, Typography } from "@/components/atoms";
import bottomSheetStyles from "@/components/atoms/BottomSheetWrapper/BottomSheetWrapper.style";
import { config, SCREEN_HEIGHT } from "@/theme/_config";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import getPheResultStyles from "./PheResultBottomSheet.styles";
import { useTheme } from "@/theme";
import dayjs from "dayjs";

const PheResultBottomSheet: React.FC<{
  isVisible: boolean;
  onClose: () => void;
  mode: "add" | "edit";
  onSave: (data: LabPayload) => void;
  onUpdate: (data: LabPayload) => void;
  onDelete: (id: number) => void;
  selectedPheResult: any;
}> = ({
  isVisible,
  onClose,
  mode,
  onSave,
  onUpdate,
  onDelete,
  selectedPheResult,
}) => {
  const [sampleDate, setSampleDate] = useState("");
  const [pheLevel, setPheLevel] = useState("");
  const [pheMetrics, setPheMetric] = useState(2);
  const [isDropdownVisible, setDropdownVisible] = useState(false);
  const [calendarFor, setCalendarFor] = useState<"sample" | "test" | null>(
    null
  );
  const [errors, setErrors] = useState({ sampleDate: "", pheLevel: "" });

  const { colors } = useTheme();
  const styles: any = useDynamicStyles(getPheResultStyles);
  const loading = useSelector(selectLabLoading);

  const pheMetricOptions = [
    { id: 1, value: 1, text: "micromoles per liter (μmol/L)" },
    { id: 2, value: 2, text: "milligrams per deciliter (mg/dl)" },
  ];

  const handleDateSelect = (range: { startDate: string }) => {
    if (calendarFor === "sample") {
      setSampleDate(range.startDate);
      setErrors((prev) => ({ ...prev, sampleDate: "" })); // Clear error
    }
    setCalendarFor(null); // Close the calendar
  };

  useEffect(() => {
    if (isVisible && mode === "edit" && selectedPheResult) {
      setSampleDate(selectedPheResult.sampleDate);
      setPheLevel(
        selectedPheResult.pheMetrics === 1
          ? selectedPheResult.pheMicromolePerLiter.toString()
          : selectedPheResult.pheMilligramPerDeciliter.toString()
      );
      setPheMetric(selectedPheResult.pheMetrics);
    }
  }, [isVisible, mode, selectedPheResult]);

  const resetValues = () => {
    setSampleDate("");
    setPheLevel("");
    setCalendarFor(null);
    setErrors({ sampleDate: "", pheLevel: "" });
  };

  useEffect(() => {
    if (isVisible && mode === "add") {
      resetValues();
    }
  }, [isVisible, mode]);

  const handleSave = () => {
    let hasError = false;
    const newErrors = { sampleDate: "", pheLevel: "" };

    if (!sampleDate) {
      newErrors.sampleDate = "Sample date is required.";
      hasError = true;
    }

    if (!pheLevel || Number(pheLevel) <= 0) {
      newErrors.pheLevel = "Phe level is required.";
      hasError = true;
    }

    setErrors(newErrors);
    if (hasError) return;

    const payload = {
      pheLevel: pheLevel === "" ? 0 : pheLevel,
      pheMetrics: pheMetrics as number,
      sampleDate: sampleDate,
      testDate: sampleDate,
    };

    if (mode === "add") {
      onSave(payload);
    } else if (mode === "edit") {
      onUpdate({ ...payload, id: selectedPheResult.id });
    }
  };

  const handleDelete = () => {
    if (selectedPheResult && selectedPheResult.id) {
      onDelete(selectedPheResult.id);
    }
  };

  const validatePheLevelInput = (text: string) => {
    const isValid = text.trim() !== "" && Number(text) > 0;

    return { value: text, error: isValid ? "" : "Phe level is required." };
  };

  return (
    <>
      <BottomSheetWrapper
        isVisible={isVisible}
        onClose={() => {
          onClose();
          resetValues();
        }}
      >
        <ScrollView
          contentContainerStyle={bottomSheetStyles.scrollContainer}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          <ScrollView
            contentContainerStyle={[
              [styles.scrollContainer, { justifyContent: "space-between" }],
            ]}
          >
            <View style={{ flex: 0.7 }}>
              <Typography.H1 style={[Common.textBold, styles.title]}>
                {mode === "add" ? "Add New Result" : "Edit Phe Result"}
              </Typography.H1>

              {/* Sample Date */}
              <View style={styles.input}>
                <Typography.B1>Sample Date</Typography.B1>
                <CustomTextInput
                  style={{ marginVertical: 0, marginTop: 8 }}
                  placeholder="Select sample date"
                  value={sampleDate ? formatDateMMDDYYYY(sampleDate) : ""}
                  editable={false}
                  icon={
                    <Icons.Calendar
                      width={15}
                      height={15}
                      fill={colors.textPrimary}
                    />
                  }
                  onIconPress={() => setCalendarFor("sample")}
                />
                {errors.sampleDate ? (
                  <Typography.B3 style={styles.errorText}>
                    {errors.sampleDate}
                  </Typography.B3>
                ) : (
                  <View style={{ marginTop: 8 }} />
                )}
                {calendarFor === "sample" && (
                  <View style={[styles.calendarContainer, { zIndex: 1 }]}>
                    <CustomCalendar
                      isVisible
                      onClose={() => setCalendarFor(null)}
                      onDateSelect={({ startDate }) =>
                        handleDateSelect({ startDate })
                      }
                      // disableModal
                      initialDate={sampleDate || new Date().toISOString()} // Navigate to the sampleDate or today's date
                    />
                  </View>
                )}
              </View>

              {/* Other Inputs */}
              {calendarFor === null && (
                <>
                  <View style={styles.input}>
                    <Typography.B1>Phe Level</Typography.B1>
                    <CustomTextInput
                      placeholder="Enter your phe level"
                      keyboardType="numeric"
                      value={pheLevel}
                      maxLength={10}
                      onChangeText={(text) => {
                        const { value, error } = validatePheLevelInput(text);
                        setPheLevel(value);
                        setErrors((prev) => ({ ...prev, pheLevel: error }));
                      }}
                      errorMessage={errors.pheLevel}
                    />
                  </View>

                  <View style={styles.input}>
                    <Typography.B1>Phe Metric</Typography.B1>

                    <Dropdown
                      data={pheMetricOptions}
                      // Ensure selectedValue matches the correct option text
                      selectedValue={
                        pheMetricOptions.find(
                          (option) => option.value === pheMetrics
                        )?.text || ""
                      }
                      setSelectedValue={(value) => {
                        const selectedOption = pheMetricOptions.find(
                          (option) => option.text === value
                        );
                        if (selectedOption) {
                          setPheMetric(selectedOption.value); // Update pheMetrics state
                        }
                        setErrors((prev) => ({ ...prev, pheMetrics: "" })); // Clear error if any
                      }}
                      isVisible={isDropdownVisible}
                      onToggle={() => setDropdownVisible(!isDropdownVisible)}
                      buttonStyle={styles.fullWidthDropdown}
                      dropdownStyle={styles.dropdownMenu}
                    />
                  </View>
                </>
              )}
            </View>

            {/* Buttons */}
            <View style={{ height: SCREEN_HEIGHT * 0.15 }} />

            <View style={styles.buttonContainer}>
              {mode === "edit" && (
                <Button.Outline
                  onPress={handleDelete}
                  style={styles.outlineButton} // Add custom styles
                >
                  <Typography.B1 style={[Common.textBold]}>
                    Delete
                  </Typography.B1>
                </Button.Outline>
              )}

              <Button.Main onPress={handleSave}>
                <Typography.B1
                  style={[Common.textBold, { color: config.colors.white }]}
                >
                  Save Result
                </Typography.B1>
              </Button.Main>
            </View>
          </ScrollView>
        </ScrollView>
        {loading && <Loader />}
      </BottomSheetWrapper>
    </>
  );
};

export default PheResultBottomSheet;
