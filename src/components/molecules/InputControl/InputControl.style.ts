import { Fonts } from "@/constants";

import { Theme } from "@/types/theme/theme";
import {
  ScaledSheet,
  verticalScale,
  ms,
  scale,
} from "react-native-size-matters";

export const getInputControlStyles = (theme: Theme) =>
  ScaledSheet.create({
    wrapper: {
      marginVertical: verticalScale(8),
    },
    container: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: ms(16),
      borderColor: theme.colors.mediumGray,
      borderWidth: 1,
      borderRadius: ms(10),
      overflow: "hidden",
    },
    labelStyle: {
      fontFamily: Fonts.RALEWAY_REGULAR,
      color: theme.colors.textPrimary,
      marginBottom: ms(10),
      marginTop: ms(10),
    },
    iconWrapperLeft: {
      marginRight: ms(16),
    },
    iconWrapperRight: {
      marginLeft: ms(16),
    },
    iconStyle: { width: scale(23), height: scale(23) },
    errorMessage: {
      fontFamily: Fonts.RALEWAY_REGULAR,
      color: theme.colors.red,
      marginTop: verticalScale(4),
    },
  });
