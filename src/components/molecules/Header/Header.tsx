import React from "react";
import { View, TouchableOpacity, Pressable } from "react-native";

import { config } from "@/theme/_config";
import getHeaderStyle from "./Header.styles";
import Icons from "@/theme/assets/images/svgs/icons";
import { Button, Typography } from "@/components/atoms";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { DrawerActions, useNavigation } from "@react-navigation/native";
import { ViewStyle } from "react-native-size-matters";

interface HeaderProps {
  title: string;
  isEditing?: boolean;
  showHamburgerMenu?: boolean;
  onEditPress?: () => void;
  onBackPress?: () => void;
  isDark?: boolean;
  isCancel?: boolean;
  onCancelPress?: () => void;
  customStyle?:ViewStyle;

}

const Header: React.FC<HeaderProps> = ({
  title,
  isEditing,
  onEditPress,
  onBackPress,
  showHamburgerMenu = false,
  isDark,
  isCancel,
  onCancelPress,
  customStyle,
}) => {
  const isCenterAligned = onBackPress || onEditPress ;

  const navigation = useNavigation();

  const handleOpenDrawer = () => {
    navigation.dispatch(DrawerActions.openDrawer());
  };

  const styles: any = useDynamicStyles(getHeaderStyle);

  const color = !isDark ? config.colors.textPrimary : config.colors.white;

  const renderCancel = () => {
  if (!isCancel) return null;
  return (
    <Pressable onPress={onCancelPress}>
      <Typography.B2 style={styles.cancelText}>Cancel</Typography.B2>
    </Pressable>
  );
};
  return (
    <View style={styles.header}>
      {onBackPress && (
        <TouchableOpacity onPress={onBackPress} style={styles.backButton}>
          <Icons.BackArrow width={24} height={24} color={color} />
        </TouchableOpacity>
      )}

      <View
        style={[
          styles.titleContainer,
          !isCenterAligned && styles.titleLeftContainer,
          isEditing && styles.isEditingTextSpace,
        ]}
      >
        <Typography.H3
          style={[
            styles.headerTitle,
            !isCenterAligned
              ? styles.headerTitleLeft
              : styles.headerTitleCenter,
              customStyle,
          ]}
        >
          {title}
        </Typography.H3>
      </View>
      {onEditPress ? (
        isEditing ? (
          <View style={styles.emptyRightContainer} />
        ) : (
          <Button.Outline style={styles.editButton} onPress={onEditPress}>
            <View style={styles.iconTextWrapper}>
              <Icons.Pencil width={16} height={16} color={color} />
              <Typography.B2 style={styles.editText}>Edit</Typography.B2>
            </View>
          </Button.Outline>
        )
      ) : showHamburgerMenu ? (
        <TouchableOpacity
          onPress={handleOpenDrawer}
          style={styles.hamburgerButton}
        >
          <Icons.Hamburger width={24} height={24} color={color} />
        </TouchableOpacity>
      ) : (
        <View style={{ flex: 0.2 }}></View>
      )}
     {renderCancel()}
    </View>
  );
};

export default Header;
