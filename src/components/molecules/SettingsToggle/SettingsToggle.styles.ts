import { Fonts } from "@/constants";
import { config } from "@/theme/_config";
import { Theme } from "@/types/theme/theme";
import { Platform } from "react-native";
import { ms, ScaledSheet } from "react-native-size-matters";

const getToggleStyles = (theme:Theme) =>
  ScaledSheet.create({
    settingsOption: {
      flexDirection: "column",
      alignItems: "center",
      justifyContent: "space-between",
      backgroundColor: theme.colors.tile_bg,
      borderRadius: ms(10),
      paddingVertical: ms(16),
      marginBottom: ms(10),
      ...Platform.select({
        ios: {
          paddingHorizontal: ms(26),
        },
        android: {
          paddingLeft: ms(26),
          paddingRight: ms(16),
        },
      }),
    },
    settingsOptionText: {
      fontSize: ms(16),
      color: theme.colors.textPrimary,
      fontFamily: Fonts.RALEWAY_REGULAR,
    },
    toggle: {
      marginLeft: -ms(23),
    },
    disclaimer: {
      color: theme.colors.textPrimary,
      fontFamily: Fonts.RALEWAY_REGULAR,
    },
    mainHeadingContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      width: "100%",
      height: ms(20),
    },
    descriptionContainer: {
      marginTop: ms(10),
      width: "100%",
    },
  });

export default getToggleStyles;
