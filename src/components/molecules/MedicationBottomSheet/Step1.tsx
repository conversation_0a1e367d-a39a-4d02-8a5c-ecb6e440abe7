import { Dropdown, Typography } from '@/components/atoms';
import { useTranslation } from 'react-i18next';
import { ScrollView, View } from 'react-native';
import { useWatch } from 'react-hook-form';

import InputField from '../Field/InputField';
import { removeTrailingZeros } from '@/utils/helpers';
import { useDynamicStyles } from '@/theme/hooks/useDynamicStyles';
import { getMedicationSheetStyles } from './MedicationBottomSheet.style';
import { useEffect, useState } from 'react';
import { ms } from 'react-native-size-matters';
import { useAppSelector } from '@/store';
import { selectTask } from '@/store/slices/taskManager/taskManager.slice';
import useTaskManagerContainer from '@/containers/taskManager/useTaskManagerContainer';


const Step1 = ({ control, setFormulaValue }) => {
  const { t } = useTranslation('taskManager');
  const styles: any = useDynamicStyles(getMedicationSheetStyles);
  const servingSize = useWatch({ control, name: 'quantity' });
  
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const selectedFormulaUnit = useWatch({control, name: 'formulaUnit'})
  const {formulaUnitList} = useAppSelector(selectTask);

  useEffect(() => {
    if(!selectedFormulaUnit) {
      setFormulaValue('formulaUnit',formulaUnitList?.[0])
    }
  }, [])

  return (
    <ScrollView
      contentContainerStyle={styles.contentContainer}
      nestedScrollEnabled
    >
      <Typography.H1 style={styles.title}>{t('formula')}</Typography.H1>

      {/* medication name */}
      <InputField
        name="formulaName"
        control={control}
        label={t('formulaName')}
        placeholder={t('enterFormulaNameHere')}
      />

      {/* Serving Size */}
      <InputField
        isDecimal
        name="quantity"
        control={control}
        label={t('servingSize')}
        keyboardType="decimal-pad"
        placeholder={t('enterGramAmount')}
        containerStyle={{ paddingRight: 0 }}
        RightChild={
            <Dropdown
              isVisible={showDropdown}
              data={formulaUnitList}
              selectedValue={selectedFormulaUnit?.text || ''}
              setSelectedValue={(value) => {
                const selectedUnit = formulaUnitList?.find(x => x.text === value);
                setFormulaValue('formulaUnit', selectedUnit);
              }}
              onToggle={() => setShowDropdown(prev => !prev)}
              buttonStyle={{
                borderTopRightRadius: ms(10)
              }}
            />
        }
      />
      <View style={styles.dosageContainer}>
        <Typography.H1 style={styles.dosageValue}>
          {servingSize
            ? removeTrailingZeros(Number(servingSize || 0)?.toFixed(2))
            : 0}{' '}
          {selectedFormulaUnit?.text}
        </Typography.H1>
        <Typography.B1 style={styles.dosageFormula}>
          {t('serving')}
        </Typography.B1>
      </View>
    </ScrollView>
  );
};

export default Step1;
