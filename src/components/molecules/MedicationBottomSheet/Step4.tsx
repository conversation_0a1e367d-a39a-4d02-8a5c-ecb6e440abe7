import React, { useMemo } from 'react';
import { ScrollView, View } from 'react-native';
import { useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { useAppSelector } from '@/store';
import { Typography } from '@/components/atoms';
import CustomDailyReminder from '../CustomReminder/CustomDailyReminder';
import CustomWeeklyReminder from '../CustomReminder/CustomWeeklyReminder';
import CustomMonthlyReminder from '../CustomReminder/CustomMonthlyReminder';
import CustomYearlyReminder from '../CustomReminder/CustomYearlyReminder';
import { selectTask } from '@/store/slices/taskManager/taskManager.slice';
import { getMedicationSheetStyles } from './MedicationBottomSheet.style';
import { useDynamicStyles } from '@/theme/hooks/useDynamicStyles';

const Step4 = ({ control }) => {
  const { t } = useTranslation('taskManager');
  const styles: any = useDynamicStyles(getMedicationSheetStyles);
  const customSchedule = useWatch({ control, name: 'customSchedule' });
  const { frequencies, weekIndexes, days, status } = useAppSelector(selectTask);

  const options = useMemo(() => {
    return {
      frequencyOptions: frequencies
        ?.filter((f) => f?.isCustom)
        ?.map((f) => ({ id: f?.id, label: f?.text, value: f?.id })),
      weekIndexOptions: weekIndexes?.map((f) => ({
        id: f?.id,
        label: f?.text,
        value: f?.id
      })),
      dayOptions: days?.map((f) => ({
        id: f?.id,
        label: f?.text,
        value: f?.id
      }))
    };
  }, []);
 
  return (
    <ScrollView
      contentContainerStyle={styles.contentContainer}
      nestedScrollEnabled
    >
      <Typography.H1 style={styles.title}>{t('Custom')}</Typography.H1>
     
      {customSchedule?.frequency?.label === 'Daily' && (
        <CustomDailyReminder
          control={control}
          inputName="customSchedule"
          options={options.frequencyOptions}
        />
      )}

      {customSchedule?.frequency?.label === 'Weekly' && (
        <CustomWeeklyReminder
          control={control}
          inputName="customSchedule"
          options={options.frequencyOptions}
        />
      )}

      {customSchedule?.frequency?.label === 'Monthly' && (
        <CustomMonthlyReminder
          control={control}
          inputName="customSchedule"
          options={options.frequencyOptions}
          weekIndexOptions={options.weekIndexOptions}
          dayOptions={options.dayOptions}
        />
      )}

      {customSchedule?.frequency?.label === 'Yearly' && (
        <CustomYearlyReminder
          control={control}
          inputName="customSchedule"
          options={options.frequencyOptions}
          weekIndexOptions={options.weekIndexOptions}
          dayOptions={options.dayOptions}
        />
      )}
    </ScrollView>
  );
};

export default Step4;
