import { useWatch } from "react-hook-form";
import { Control } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { ms } from "react-native-size-matters";
import { ScrollView, View } from "react-native";
import { SCREEN_HEIGHT } from "@gorhom/bottom-sheet";

import { useAppSelector } from "@/store";
import { TimezoneService } from "@/utils/timezoneService";
import { config } from "@/theme/_config";
import Common from "@/theme/common.style";
import InputField from "../Field/InputField";
import { Typography } from "@/components/atoms";
import { Sections } from "@/types/schemas/task";
import Icons from "@/theme/assets/images/svgs/icons";
import RNCalendar from "@/components/atoms/RNCalendar/RNCalendar";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { getMedicationSheetStyles } from "./MedicationBottomSheet.style";
import { selectTask } from "@/store/slices/taskManager/taskManager.slice";
import InputDropdown from "@/components/atoms/InputDropdown/InputDropdown";
import RNDateTimePicker from "@/components/atoms/RNDateTimePicker/RNDateTimePicker";
import { getTimeZoneOffsetDiffAdvanced } from "@/utils/timezoneUtils";

const Step3 = ({
  control,
  getFrequencyName,
  customUpdateHandler,
  isFormula,
}: {
  control: Control;
  getFrequencyName: (id: number) => string;
  customUpdateHandler?: (isFormula: boolean) => void;
  isFormula: boolean;
}) => {
  const { t } = useTranslation("taskManager");
  const styles: any = useDynamicStyles(getMedicationSheetStyles);
  // const { profileTzToggle, profileTimeZone } = useAppSelector((state) => state.settings);
  const { user } = useAppSelector((state) => state.user);
  const { frequencies, reminderTimes, fromUpdate } = useAppSelector(selectTask);
  const customSchedule = useWatch({ control, name: "customSchedule" });
  const frequencyTypeId = useWatch({ control, name: "frequencyTypeId" });
  const intakeTimeValue = useWatch({ control, name: "intakeTime" });

  const isCustomValue =
    frequencyTypeId?.id === 11 &&
    customSchedule?.frequency?.id &&
    customSchedule?.interval?.id;

  const homeTime = TimezoneService.convertToHomeTime(
    intakeTimeValue,
    TimezoneService.getDeviceTimeZone(),
    user?.timeZoneId);

  console.log({
    user: getTimeZoneOffsetDiffAdvanced(user?.timeZoneId, TimezoneService.getDeviceTimeZone(), 'Step3')
  })
  return (
    <ScrollView
      contentContainerStyle={[styles.contentContainer]}
      nestedScrollEnabled
    >
      <View>
        <Typography.H1 style={styles.title}>{t("schedule")}</Typography.H1>
        <View style={styles.zIndexMax}>
          <InputField
            name="frequencyTypeId"
            control={control}
            label={t("repeats")}
            component={InputDropdown}
            defaultValue={{
              id: frequencies?.[1]?.id, // NOTE: default value of the frequency will be Daily ==> expose this to the enums later
              label: frequencies?.[1]?.text,
              value: frequencies?.[1]?.id,
            }}
            trigger="onSelect"
            wrapperStyle={{ marginVertical: 0 }}
            menuHeight={SCREEN_HEIGHT / 3.5}
            menu={
              frequencies?.map((frequency) => ({
                id: frequency.id,
                label: frequency.text,
                value: frequency.id,
              })) || []
            }
            fromUpdate={(item) => {
              if (item?.id === 11 && customUpdateHandler) {
                if (!fromUpdate) {
                  customUpdateHandler(isFormula);
                }
                customUpdateHandler(fromUpdate === Sections.FORMULA);
              }
            }}
          />
        </View>

        {isCustomValue ? (
          <View style={styles.customRow}>
            <View style={styles.marginRight30}>
              <Typography.H5 style={styles.frequencyLabel}>
                Frequency
              </Typography.H5>
              <Typography.H5 style={styles.frequencyValue}>
                {customSchedule?.frequency?.label}
              </Typography.H5>
            </View>

            <View style={styles.flex085}>
              <Typography.H5 style={styles.frequencyLabel}>
                Every
              </Typography.H5>
              <Typography.H5 style={styles.frequencyValue}>
                {`${customSchedule?.interval?.label} ${getFrequencyName(customSchedule?.frequency?.label) || ""}`}
              </Typography.H5>
            </View>
          </View>
        ) : (
          <View style={styles.height28} />
        )}

        <View style={styles.rowAlignStart}>
          <View style={styles.marginRight30}>
            <InputField
              name="intakeTime"
              control={control}
              component={RNDateTimePicker}
              label={t("intakeTime")}
              wrapperStyle={{ width: ms(114), height: ms(36) }}
              mode="time"
              trigger="onChange"
              pickerProps={{}}
            />
          </View>

          <View style={styles.flex1zIndex9998}>
            <InputField
              name="reminder"
              inputStyle={styles.reminderInput}
              control={control}
              label={t("remindMe")}
              menuHeight={ms(130)}
              itemHeight={ms(36)}
              dropdownTextStyle={styles.dropdownTextStyle}
              component={InputDropdown}
              defaultValue={{
                id: reminderTimes?.[0]?.id, // NOTE: default value of the reminder will be At the scheduled time ==> expose this to the enums later
                label: reminderTimes?.[0]?.text,
                value: reminderTimes?.[0]?.id,
              }}
              showsVerticalScrollIndicator={true}
              trigger="onSelect"
              menu={
                reminderTimes?.map((reminderTime) => ({
                  id: reminderTime.id,
                  label: `${reminderTime.text}${reminderTime.id <= 0 ? "" : " before"}`,
                  value: reminderTime.id,
                })) || []
              }
            />
          </View>
        </View>

        {TimezoneService.getTimezoneProps()?.isKeepHomeTzVisible && intakeTimeValue && (
          <View style={styles.homeTimeContainer}>
            <View style={styles.homeTimeInnerContainer}>
              <Icons.Clock width={ms(12)} height={ms(12)} color={styles.homeTimeText.color} />
              <Typography.B3 style={styles.homeTimeText}>
                Home time: {homeTime}
              </Typography.B3>
            </View>
          </View>
        )}
      </View>

      <View style={styles.marginTop32ZIndexNeg1}>
        <Typography.B1 style={Common.textBold} numberOfLines={1}>
          {t("Duration")}
        </Typography.B1>
      </View>
      <View style={styles.rowAlignCenterZIndexNeg1}>
        <View style={styles.marginRight30}>
          <InputField
            name="fromDate"
            control={control}
            trigger="onSelect"
            component={RNCalendar}
            label={t("From")}
            wrapperStyle={{ width: ms(114) }}
            mode="date"
          />
        </View>

        <View style={styles.flex085}>
          <InputField
            name="toDate"
            control={control}
            trigger="onSelect"
            component={RNCalendar}
            label={t("Until")}
            mode="date"
          />
        </View>
      </View>
    </ScrollView>
  );
};

export default Step3;
