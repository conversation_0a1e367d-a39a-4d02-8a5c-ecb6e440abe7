import dayjs from 'dayjs';
import React from 'react';
import { Control } from 'react-hook-form';
import { ms } from 'react-native-size-matters';
import { useTranslation } from 'react-i18next';
import { ActivityIndicator, ScrollView, View } from 'react-native';

import { config } from '@/theme/_config';
import { useAppSelector } from '@/store';
import { Typography } from '@/components/atoms';
import Icons from '@/theme/assets/images/svgs/icons';
import { useDynamicStyles } from '@/theme/hooks/useDynamicStyles';
import { getMedicationSheetStyles } from './MedicationBottomSheet.style';
import { selectTask } from '@/store/slices/taskManager/taskManager.slice';

type DoseDetails = {
  quantity: string | number;
  strength: string | number;
};

interface Step5Props {
  control: Control<any>;
  isLoading: boolean;
}

const Step5 = ({ control, isLoading }: Step5Props) => {
  const { t } = useTranslation('taskManager');
  const { taskDetails, frequencies } = useAppSelector(selectTask);

  const styles: any = useDynamicStyles(getMedicationSheetStyles);

  const dosage = taskDetails?.medicationDetails?.reduce(
    (sum: number, dose: DoseDetails) =>
      sum + (Number(dose?.quantity) * Number(dose?.strength)), 0);

  const frequencyId = taskDetails?.customScheduleDetails?.frequencyTypeId ?? taskDetails?.frequencyTypeId;
  const frequencyTypeName = frequencyId !== undefined
    ? frequencies.find(frequency => frequency.id === frequencyId)?.text
    : '';

  return (
    <ScrollView contentContainerStyle={[styles.contentContainer]}>
      <View style={styles.summaryContainer}>
        <Typography.H1 style={styles.title}>{t('Summary')}</Typography.H1>

        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator
              color={config.backgrounds.primary}
              size={ms(30)}
            />
          </View>
        ) : (
          <>
            <View style={styles.medicationNameSection}>
              <View style={styles.flex1}>
                <Typography.H5 style={styles.labelText}>
                  Medication Name:
                </Typography.H5>
                <Typography.H0 style={styles.medicationNameText}>
                  {taskDetails?.medicineName}
                </Typography.H0>
              </View>

              <View>
                <Icons.MedPillIcon width={42} height={42} />
              </View>
            </View>

            <View style={styles.infoRow}>
              <View style={styles.flex1}>
                <Typography.H5 style={styles.labelText}>
                  Dosage:
                </Typography.H5>
                <Typography.H5 style={styles.valueText}>
                  {dosage}{' '}mg
                </Typography.H5>
              </View>

              <View style={styles.flex1}>
                <Typography.H5 style={styles.labelText}>
                  Frequency:
                </Typography.H5>
                <Typography.H5 style={styles.valueText}>
                  {taskDetails?.customScheduleDetails
                    ? `Custom ( ${frequencyTypeName} )`
                    : (frequencyTypeName ?? '')}
                </Typography.H5>
              </View>
            </View>

            <View style={styles.infoRow}>
              <View style={styles.flex1}>
                <Typography.H5 style={styles.labelText}>
                  Start Date:
                </Typography.H5>
                <Typography.H5 style={styles.valueText}>
                  {dayjs(taskDetails?.fromDate).format('MM-DD-YYYY')}
                </Typography.H5>
              </View>

              <View style={styles.flex1}>
                <Typography.H5 style={styles.labelText}>
                  End Date:
                </Typography.H5>
                <Typography.H5 style={styles.valueText}>
                  {taskDetails?.toDate
                    ? dayjs(taskDetails?.toDate).format('MM-DD-YYYY')
                    : 'N/A'}
                </Typography.H5>
              </View>
            </View>
          </>
        )}
      </View>
    </ScrollView>
  );
};

export default Step5;
