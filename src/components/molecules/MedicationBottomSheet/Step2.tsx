import React from 'react';
import { Typography } from '@/components/atoms';
import { ScrollView, View } from 'react-native';
import { ms } from 'react-native-size-matters';
import { useTranslation } from 'react-i18next';
import { Control, useWatch } from 'react-hook-form';

import DosesSection from './DosesSection';
import InputField from '../Field/InputField';
import { useDynamicStyles } from '@/theme/hooks/useDynamicStyles';
import { getMedicationSheetStyles } from './MedicationBottomSheet.style';
import { removeTrailingZeros } from '@/utils/helpers';

interface Dose {
  amount?: number;
  time?: string;
}

interface Step2Props {
  control: Control<any>;
  handleAddDose?: () => void;
  handleRemoveDose?: (index: number) => void;
}

const Step2: React.FC<Step2Props> = ({ control, handleAddDose, handleRemoveDose }) => {
  const styles: any = useDynamicStyles(getMedicationSheetStyles);
  const { t } = useTranslation('taskManager');

  const doses = useWatch({
    control,
    name: 'doses',
    defaultValue: []
  });


  const dosageSummary = doses.reduce(
    (sum: number, dose: { quantity: string | number; strength: string | number }) =>
      sum + (Number(dose.quantity) * Number(dose.strength)), 0);

  return (
    <ScrollView
      contentContainerStyle={styles.contentContainer}
      nestedScrollEnabled
    >
      <View>
        <Typography.H1 style={styles.title}>
          {t('dosage')}
        </Typography.H1>

        <InputField
          name="medicineName"
          control={control}
          label={t('medicationName')}
          wrapperStyle={{ marginVertical: ms(7) }}
          placeholder={t('enterMedicationNameHere')}
        />

        {React.Children.toArray((doses as Dose[])
          ?.map((_: Dose, index: number) => (
            <DosesSection
              control={control}
              index={index}
              length={doses.length}
              onAdd={() => handleAddDose?.()}
              onRemove={() => handleRemoveDose?.(index)}
              defaultValue={doses[index]}
            />
          )))}
      </View>

      <View style={styles.dosageContainer}>
        <Typography.H1 style={styles.dosageValue}>
          {dosageSummary ? removeTrailingZeros(Number(dosageSummary)?.toFixed(2)) : 0} mg
        </Typography.H1>
        <Typography.B1 style={styles.dosageFormula}>
          {t('dosage')}
        </Typography.B1>
      </View>
    </ScrollView>
  );
};

export default Step2;
