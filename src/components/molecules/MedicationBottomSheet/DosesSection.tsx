import React from 'react';
import { View } from 'react-native';
import { ms } from 'react-native-size-matters';
import { useTranslation } from 'react-i18next';

import { useAppSelector } from '@/store';
import InputField from '../Field/InputField';
import { Button, Typography } from '@/components/atoms';
import { useDynamicStyles } from '@/theme/hooks/useDynamicStyles';
import { getMedicationSheetStyles } from './MedicationBottomSheet.style';
import InputDropdown from '@/components/atoms/InputDropdown/InputDropdown';
import { selectTask } from '@/store/slices/taskManager/taskManager.slice';

interface DosesSectionProps {
  control: any;
  index: number;
  length: number;
  onAdd: () => void;
  onRemove: (index: number) => void;
  defaultValue: any;
}

const DosesSection: React.FC<DosesSectionProps> = ({
  control,
  index,
  length,
  onAdd,
  onRemove,
  defaultValue
}) => {
  const { t } = useTranslation('taskManager');
  const { categories } = useAppSelector(selectTask);
  const styles: any = useDynamicStyles(getMedicationSheetStyles);

  const selectedCategory = categories?.find(
    (category) => category.medicationTypeId === (
      typeof defaultValue?.categoryTypeId === 'object'
        ? defaultValue?.categoryTypeId?.id
        : defaultValue?.categoryTypeId
    )
  );

  const formattedSelectedCategory = selectedCategory ? {
    id: selectedCategory.medicationTypeId,
    label: selectedCategory.name,
    value: selectedCategory.medicationTypeId
  } : undefined;

  const renderUnit = () => {
    return <Typography.B2 style={styles.unit}>mg</Typography.B2>;
  };

  const showRemoveButton = length > 1;
  const showAddButton = index === length - 1;
  const showSeparator = index !== length - 1;

  return (
    <View style={styles.doseContainer}>
      <InputField
        name={`doses.${index}.categoryTypeId`}
        control={control}
        label={t('medicationFormType')}
        component={InputDropdown}
        trigger="onSelect"
        menuHeight={ms(136)}
        wrapperStyle={{ marginVertical: ms(7) }}
        menu={categories?.map((category) => ({
          id: category.medicationTypeId,
          label: category.name,
          value: category.medicationTypeId
        }))}
        defaultValue={formattedSelectedCategory}
        value={formattedSelectedCategory}
        onChange={(value) => value}
      />

      <View style={styles.qtyStrengthRow}>
        <InputField
          name={`doses.${index}.quantity`}
          control={control}
          label={t('quantity')}
          placeholder={t('enterQuantity')}
          keyboardType="decimal-pad"
          isDecimal
          wrapperStyle={{ width: '48.85%', marginVertical: ms(2) }}
          defaultValue={`${defaultValue?.quantity || ""}`}
        />

        <InputField
          name={`doses.${index}.strength`}
          control={control}
          label={t('strength')}
          RightChild={renderUnit()}
          placeholder={t('enterStrength')}
          keyboardType="decimal-pad"
          isDecimal
          wrapperStyle={{ width: '48.85%', marginVertical: ms(2) }}
          defaultValue={`${defaultValue?.strength || ""}`}
        />
      </View>

      <View style={showAddButton ? styles.qtyStrengthBtnRow : styles.buttonContainer}>
        {showAddButton && (
          <Button.Outline
            style={styles.addDosageBtn}
            onPress={onAdd}
          >
            <Typography.B2 style={styles.addDosageBtnText}>
              {t('addDose')}
            </Typography.B2>
          </Button.Outline>
        )}

        {showRemoveButton && (
          <Button.Outline
            style={[
              styles.removeDosageBtn,
              showAddButton ? {} : styles.removeButtonAlone
            ]}
            onPress={() => onRemove(index)}
          >
            <Typography.B2 style={styles.removeDosageBtnText}>
              {t('remove')}
            </Typography.B2>
          </Button.Outline>
        )}
      </View>

      {showSeparator && <View style={styles.separator} />}
    </View>
  );
};

export default DosesSection;
