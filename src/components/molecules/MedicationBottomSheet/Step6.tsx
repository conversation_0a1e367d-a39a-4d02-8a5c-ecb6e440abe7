import dayjs from 'dayjs';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ms } from 'react-native-size-matters';
import { ActivityIndicator, ScrollView, View } from 'react-native';

import { config } from '@/theme/_config';
import timeSlots from '@/constants/timeSlots';
import { Typography } from '@/components/atoms';
import Icons from '@/theme/assets/images/svgs/icons';
import { useDynamicStyles } from '@/theme/hooks/useDynamicStyles';
import { getMedicationSheetStyles } from './MedicationBottomSheet.style';
import { useAppSelector } from '@/store';
import { selectTask } from '@/store/slices/taskManager/taskManager.slice';

interface FormulaDetails {
  intakeTime: string;
  toDate: string;
  fromDate: string;
  formulaName: string;
  quantity: number;
}
interface Step6Props {
  formulaDetails: FormulaDetails | null;
  frequencyTypeName?: string;
  isLoading: boolean;
}

const Step6 = ({
  formulaDetails,
  frequencyTypeName,
  isLoading
}: Step6Props) => {

  const { t } = useTranslation('taskManager');
  const styles: any = useDynamicStyles(getMedicationSheetStyles);
  const {
    intakeTime,
    formulaName,
    fromDate,
    toDate,
    quantity,
    unitId
   } = formulaDetails || {};

   const {formulaUnitList} = useAppSelector(selectTask)

  const memoValues = useMemo(() => {
    const baseDate = dayjs(fromDate).format('YYYY-MM-DD');
    const combinedDateTime = dayjs(`${baseDate}T${intakeTime}`).format('hh:mm A');

    const isIntakeTimeNotValid =
      !combinedDateTime || combinedDateTime?.toLowerCase()?.includes('invalid');

    const localIntakeTime = isIntakeTimeNotValid ? '' : combinedDateTime;
    const localFromDate = fromDate
      ? dayjs(fromDate).format(timeSlots.DATE_FORMAT)
      : '';
    const localToDate = toDate
      ? dayjs(toDate).format(timeSlots.DATE_FORMAT)
      : 'N/A';

    return {
      localIntakeTime,
      localFromDate,
      localToDate
    };
  }, [intakeTime, fromDate, toDate]);

  return (
    <ScrollView contentContainerStyle={styles.contentContainer}>
      <View style={styles.summaryContainer}>
        <Typography.H1 style={styles.title}>{t('Summary')}</Typography.H1>

        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator
              color={config.backgrounds.primary}
              size={ms(30)}
            />
          </View>
        ) : (
          <>
            <View style={styles.formulaRow}>
              <View style={styles.formulaTextContainer}>
                <Typography.H4 style={styles.formulaLabel}>
                  {t("formulaName")}:
                </Typography.H4>
                <Typography.H3 style={styles.formulaName}>
                  {formulaName}
                </Typography.H3>
              </View>
              <View>
                <Icons.SumaryFormulaIcon width={90} height={90} />
              </View>
            </View>

            <View style={styles.infoFormulaRow}>
              <View style={styles.infoColumn}>
                <Typography.H5 style={styles.infoLabel}>
                  {t("servingSize")}:
                </Typography.H5>
                <Typography.H5>
                  {quantity} {formulaUnitList?.find(x => x.id === unitId)?.text || ''}
                </Typography.H5>
              </View>
            </View>

            <View style={styles.infoFormulaRow}>
              <View style={styles.infoColumn}>
                <Typography.H5 style={styles.infoLabel}>
                  Intake time:
                </Typography.H5>
                <Typography.H5>
                  {memoValues.localIntakeTime}
                </Typography.H5>
              </View>

              <View style={styles.infoColumn}>
                <Typography.H5 style={styles.infoLabel}>
                  Frequency:
                </Typography.H5>
                <Typography.H5>{frequencyTypeName}</Typography.H5>
              </View>
            </View>
            <View style={styles.infoFormulaRow}>
              <View style={styles.infoColumn}>
                <Typography.H5 style={styles.infoLabel}>
                  Start Date:
                </Typography.H5>
                <Typography.H5>
                  {memoValues.localFromDate}
                </Typography.H5>
              </View>

              <View style={styles.infoColumn}>
                <Typography.H5 style={styles.infoLabel}>
                  End Date:
                </Typography.H5>
                <Typography.H5>
                  {memoValues.localToDate}
                </Typography.H5>
              </View>
            </View>
          </>
        )}
      </View>
    </ScrollView>
  );
};

export default Step6;
