import dayjs from "dayjs";
import React, { FC } from "react";
import { View } from "react-native";
import { ms } from "react-native-size-matters";

import { useTheme } from "@/theme";
import { FormulaTask } from "@/types/schemas/task";
import Icons from "@/theme/assets/images/svgs/icons";
import { getPeriodOfDay } from "@/utils/taskManager";
import { getFormulaCardStyles } from "./FormulaCard.styles";
import { Button, TaskBadge, Typography } from "@/components/atoms";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { useAppSelector } from "@/store";
import { selectTask } from "@/store/slices/taskManager/taskManager.slice";

export interface FormulaCardProps extends FormulaTask {
  onPress: () => void;
  quantity?: string | number;
}

const FormulaCard: FC<FormulaCardProps> = ({
  formulaName,
  fromDate,
  quantity,
  upcoming,
  unitId,
  onPress
}) => {
  const localPeriod = getPeriodOfDay(
    dayjs(fromDate).format("HH:mm:ss")
  );
  const { colors, variant } = useTheme();
  const styles: any = useDynamicStyles(getFormulaCardStyles);

  const {formulaUnitList} = useAppSelector(selectTask)
  return (
    <View style={styles.cardContainer}>
      <View style={styles.leftContainer}>
        {variant === 'light' ?
          <Icons.FormulaDarkIcon height={ms(28)} width={24} />
          :
          <Icons.FormulaIcon height={ms(28)} width={24} />}
      </View>

      <View style={styles.centerContainer}>
        <View>
          <Typography.H2 style={styles.title}>{formulaName}</Typography.H2>
        </View>
        <View style={[styles.rowContainer, styles.topSpacing, { marginBottom: ms(4) }]}>
          <Typography.B3 style={styles.doseText}>
            {quantity} {formulaUnitList?.find(x => x.id === unitId)?.text || ''}
          </Typography.B3>
        </View>

        <View style={styles.rowContainer}>
          <Icons.Calendar fill={colors.textPrimary} />
          <Typography.B3 style={styles.leftSpacing}>
            {dayjs(upcoming).format("MM-DD-YYYY")}
          </Typography.B3>
        </View>

        <View style={styles.rowContainer}>
          {variant === 'dark' ?
            <Icons.TaskClock />
            :
            <Icons.TaskClockDark />}
          <Typography.B3 style={styles.leftSpacing}>
            {dayjs(upcoming).format("hh:mm A")}
          </Typography.B3>
        </View>
      </View>

      <View style={styles.rightContainer}>
        <View style={styles.gradientPill}>
          <TaskBadge>
            <Typography.B4 style={{ color: colors.white }}>{localPeriod}</Typography.B4>
          </TaskBadge>
        </View>
        <View style={styles.btnContainer}>
          <Button.BlackOutline style={styles.actionBtn} onPress={onPress}>
            <Icons.RightArrow color={colors.textPrimary} />
          </Button.BlackOutline>
        </View>
      </View>
    </View>
  );
};

export default FormulaCard;
