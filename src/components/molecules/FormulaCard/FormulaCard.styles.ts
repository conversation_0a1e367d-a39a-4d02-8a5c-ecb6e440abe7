import {
  ms,
  scale,
  ScaledSheet,
  verticalScale,
} from "react-native-size-matters";
import { Theme } from "@/types/theme/theme";

export const getFormulaCardStyles = (theme: Theme) => ScaledSheet.create({
  cardContainer: {
    backgroundColor: theme.colors.taskCardGray,
    paddingTop: verticalScale(18.5),
    paddingBottom: ms(8),
    paddingLeft: scale(4),
    paddingRight: -ms(1),
    borderRadius: scale(14),
    flexDirection: 'row',
    minHeight: ms(134)
  },
  pillStyle: {
    height: verticalScale(24)
  },
  title: {
    lineHeight: ms(24)
  },
  topSpacing: { marginTop: ms(10) },
  leftContainer: {
    width: '15%',
    alignItems: 'center'
  },
  centerContainer: {
    width: '60%'
  },
  rightContainer: {
    width: '25%',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingRight: ms(12.5),
  },
  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  gradientPill: {
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'flex-end'
  },
  leftSpacing: { marginLeft: ms(10) },
  actionBtn: {
    padding: ms(10),
    borderRadius: ms(60),
    height: verticalScale(28),
    aspectRatio: 1
  },
  btnContainer: {
    alignSelf: 'flex-end',
  }
});
