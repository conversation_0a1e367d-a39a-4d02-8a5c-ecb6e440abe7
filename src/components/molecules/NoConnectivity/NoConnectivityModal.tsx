import React from "react";
import { View, StyleSheet, Image, Dimensions } from "react-native";
import Modal from "react-native-modal";
import NoConnectivity from "@/theme/assets/images/noconnection.png";
import { config } from "@/theme/_config";

interface NoConnectivityModalProps {
  show: boolean;
}

const NoConnectivityModal: React.FC<NoConnectivityModalProps> = ({ show }) => {

  return (
    <Modal
      isVisible={show}
      style={styles.modal}
      useNativeDriver
      animationIn="fadeIn"
      animationOut="fadeOut"
    >
      <View style={styles.container}>
        <Image
          source={NoConnectivity}
          style={styles.image}
          resizeMode="cover"
        />
      </View>
    </Modal>
  );
};

const { width, height } = Dimensions.get("window");

const styles = StyleSheet.create({
  modal: {
    margin: 0, // No margin, ensuring full-screen modal
  },
  container: {
    flex: 1,
    backgroundColor: config.colors.white, // Modal background color
    alignItems: "center",
    justifyContent: "center",
  },
  image: {
    width: width, // Full screen width
    height: height, // Full screen height
  },
});

export default NoConnectivityModal;
