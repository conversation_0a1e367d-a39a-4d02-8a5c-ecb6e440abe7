import React from "react";
import { View, TouchableOpacity } from "react-native";

import { Typography } from "@/components/atoms";
import Tag from "@/components/atoms/Tag/Tag";
import Common from "@/theme/common.style";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import getSettingsOptionStyle from "./SettingsOption.styles";
import { getPheAllowanceUnit, getPheAllowanceValue } from "@/utils/helpers";

interface SettingsOptionProps {
  title?: string;
  tagText?: string | React.ReactElement;
  leftIcon?: React.ReactNode; // Left icon
  rightIcon?: React.ReactNode; // Right icon
  onPress?: () => void;
  disabled?: boolean;
  consumptionType?: "Phe" | "Protein";
  tagInfo?: { amount: number; unit: string };
}

const SettingsOption: React.FC<SettingsOptionProps> = ({
  title,
  tagText,
  leftIcon,
  rightIcon,
  onPress,
  disabled,
  consumptionType = "Phe",
  tagInfo,
}) => {
  const styles: any = useDynamicStyles(getSettingsOptionStyle);
  return (
    <TouchableOpacity onPress={onPress} disabled={disabled}>
      <View style={styles.settingsOption}>
        <View style={styles.row}>
          {/* Left icon */}
          {leftIcon && <View style={styles.leftIcon}>{leftIcon}</View>}

          <Typography.B1 style={styles.settingsOptionText}>
            {title}
          </Typography.B1>

          {/* Tag: support single string or tagInfo */}
          {(!!tagText || !!tagInfo) && (
            <Tag.Main style={styles.tagView}>
              <Typography.B5 style={styles.tagText}>
                {tagText
                  ? tagText
                  : `${getPheAllowanceValue(Number(tagInfo?.amount || 0), consumptionType || "Phe")} ${getPheAllowanceUnit(consumptionType || "Phe")}`}
              </Typography.B5>
            </Tag.Main>
          )}

          {/* Right icon */}
          {rightIcon && <View style={styles.rightIcon}>{rightIcon}</View>}
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default SettingsOption;
