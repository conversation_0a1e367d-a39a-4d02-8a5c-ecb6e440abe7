import { Fonts } from "@/constants";
import { config } from "@/theme/_config";
import { Theme } from "@/types/theme/theme";
import { StyleSheet } from "react-native";
import { ms } from "react-native-size-matters";

const getSettingsOptionStyle = (theme:Theme) => StyleSheet.create({
  settingsOption: {
    flexDirection: "column", // Allow content to stack vertically
    alignItems: "flex-start", // Align items to the start of the column
    backgroundColor: theme.colors.tile_bg,
    borderColor: theme.colors.tile_bg,
    borderRadius: ms(10),
    paddingVertical: ms(16),
    paddingHorizontal: ms(26),
    width: "100%",
  },
  row: {
    flexDirection: "row", // Keep title, tag, and icon on the same row
    alignItems: "center",
    justifyContent: "space-between",
    width: "100%",
  },
  settingsOptionText: {
    color: theme.colors.textPrimary,
    fontSize: ms(16),
    fontFamily: Fonts.RALEWAY_REGULAR,
  },
  tagText: {
    fontFamily: Fonts.RALEWAY_BOLD,
    color: theme.colors.white
  },
  tagView: {
    paddingHorizontal: ms(12),
  },
  noteContainer: {
    marginTop: ms(10), // Add spacing from the previous row
    width: "100%", // Ensure it spans the full width
  },
  note: {
    color: theme.colors.yellowBlue,
    fontSize: ms(12),
  },
});


export default getSettingsOptionStyle