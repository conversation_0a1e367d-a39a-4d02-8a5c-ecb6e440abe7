import React, { useEffect } from "react";
import { View, TouchableOpacity } from "react-native";
import { Card, Typography } from "@/components/atoms";
import Icons from "@/theme/assets/images/svgs/icons";

import Common from "@/theme/common.style";
import { useNavigation } from "@react-navigation/native";
import { store, useAppDispatch, useAppSelector } from "@/store";
import {
  fetchRecentPheResults,
  selectLatestPheResult,
} from "@/store/slices/labsSlice";
import dayjs from "dayjs";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import getLabsDashboardCardStyles from "./LabsDashboardCard.styles";

const LabsDashboardCard: React.FC = () => {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();

  const styles: any = useDynamicStyles(getLabsDashboardCardStyles);

  // Select the latest Phe result from Redux state
  const latestPheResult = useAppSelector(selectLatestPheResult);

  // Fetch the latest Phe results when the component mounts
  useEffect(() => {
    dispatch(fetchRecentPheResults());
  }, [dispatch]);

  useEffect(() => {
    const intervalId = setInterval(() => {
      // User ID exists, dispatch fetchDailyTasks and stop polling
      store.dispatch(fetchRecentPheResults());
      clearInterval(intervalId); // Stop polling
    }, 1000); // Poll every 1 second

    // Cleanup the interval on unmount
    return () => clearInterval(intervalId);
  }, []);

  // Navigate to Labs screen
  const handlePress = () => {
    navigation.navigate("Labs");
  };

  return (
    <Card style={styles.container}>
      <TouchableOpacity onPress={handlePress}>
        <View style={styles.row}>
          <Typography.H3 style={[styles.title, Common.textBold]}>
            Labs
          </Typography.H3>
          <Icons.Drop />
        </View>
        {latestPheResult?.pheMilligramPerDeciliter ||
        latestPheResult?.pheMicromolePerLiter ? (
          <>
            <View style={styles.pheLevelContainer}>
              <Typography.B2
                style={[
                  styles.pheLevel,
                  Common.textBold,
                  ((latestPheResult?.pheMilligramPerDeciliter ||
                    latestPheResult?.pheMicromolePerLiter) ??
                    0) > 999.99 && styles.smallFont,
                ]}
              >
                {(latestPheResult.pheMetrics === 2
                  ? latestPheResult?.pheMilligramPerDeciliter
                  : latestPheResult?.pheMicromolePerLiter) ?? "N/A"}
              </Typography.B2>
              <Typography.B2
                style={[
                  styles.unit,
                  ((latestPheResult?.pheMilligramPerDeciliter ||
                    latestPheResult?.pheMicromolePerLiter) ??
                    0) > 999.99 && styles.smallFont,
                ]}
              >
                {latestPheResult.pheMetrics === 2 ? "mg/dL" : "μmol/L"}
              </Typography.B2>
            </View>

            <Typography.B1 style={styles.subtitle}>Phe Level</Typography.B1>
            <Typography.B3 style={styles.date}>
              {latestPheResult?.testDate
                ? dayjs(latestPheResult.testDate).format("MM/DD/YYYY")
                : "No date available"}
            </Typography.B3>
          </>
        ) : (
          <Typography.B3 style={styles.subtitle}>
            Tap here to enter your first test result.
          </Typography.B3>
        )}
      </TouchableOpacity>
    </Card>
  );
};

export default LabsDashboardCard;
