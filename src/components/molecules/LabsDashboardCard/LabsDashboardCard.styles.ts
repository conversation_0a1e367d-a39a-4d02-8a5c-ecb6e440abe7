import { ScaledSheet, ms } from "react-native-size-matters";

import { Theme } from "@/types/theme/theme";

const getLabsDashboardCardStyles = (theme: Theme) =>ScaledSheet.create({
  container: {
    backgroundColor: theme.colors.primary,
    width: ms(120),
  },
  row: {
    flexDirection: "row", // Align items in a row
    alignItems: "center", // Vertically center the items
    marginBottom: ms(8), // Add some spacing below the row
  },
  title: {
    color: theme.colors.white,
    flex: 1,
  },
  pheLevelContainer: {
    flexDirection: "row",
    alignItems: "center", // Center items vertically
    justifyContent: "center", // Center items horizontally
    marginBottom: ms(8), // Add space below the container
    borderWidth: 2, // Add border
    borderColor: theme.colors.white, // Set border color to white
    borderRadius: ms(8), // Rounded corners
    paddingBottom: ms(5), // Padding for top and bottom
    alignSelf: "flex-start", // Adjust width dynamically to content size
    overflow: "hidden",
    width: "100%",
  },

  pheLevel: {
    color: theme.colors.white,
  },
  unit: {
    color: theme.colors.white,
    fontSize: ms(10),
    marginLeft: ms(4),
  },
  subtitle: {
    color: theme.colors.white,
    marginBottom: ms(4),
  },
  date: {
    color: theme.colors.white,
    fontSize: ms(12),
  },
  smallFont: {
    fontSize: ms(10), // Reduced font size for values > 999
  },
});

export default getLabsDashboardCardStyles;
