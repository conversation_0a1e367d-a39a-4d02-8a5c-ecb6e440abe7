/** @format */

import { FC, useState } from "react";
import { FlatList, Pressable, View } from "react-native";
import InputField from "../Field/InputField";
import AnimatedInputDropdown from "@/components/atoms/AnimatedInputDropdown/AnimatedInputDropdown";
import { MOCK_DATA } from "@/mock-data/mockData";
import { useTranslation } from "react-i18next";
import { useWatch } from "react-hook-form";
import { Typography } from "@/components/atoms";
import { getCustomReminderStyle } from "./CustomReminder.style";
import Icons from "@/theme/assets/images/svgs/icons";
import useTaskManagerContainer from "@/containers/taskManager/useTaskManagerContainer";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { useTheme } from "@/theme";
import ErrorMessage from "./ErrorMessage";

const MONTH_DAYS = Array.from({ length: 31 }, (_, i) => i + 1);

interface CustomMonthlyReminderProps {
  control: any;
  inputName: string;
  options: any;
  weekIndexOptions: any;
  dayOptions: any;
}

const CustomMonthlyReminder: FC<CustomMonthlyReminderProps> = ({
  control,
  inputName,
  options,
  weekIndexOptions,
  dayOptions,
}) => {
  const { t } = useTranslation(["taskManager"]);

  const { getFrequencyName } = useTaskManagerContainer();
  const customSchedule = useWatch({ control, name: inputName });

  const { colors } = useTheme();
  const styles: any = useDynamicStyles(getCustomReminderStyle);
  function MonthlyRoutineList({ onSelect, value, keyName, error }) {
    const [selectedMonthDates, setSelectedMonthDates] = useState<number[]>([...value?.[keyName]]);
    const [weekIndex, setWeekIndex] = useState(value?.["weekIndex"]);
    const [weekDate, setWeekDate] = useState<number>(value?.["weekDays"]?.[0] || null);
    const [isEachSelected, setIsEachSelected] = useState<boolean>(value?.["isEachSelected"]);

    function toggleEachSelect(val: boolean) {
      setIsEachSelected(val);

      const selectPayload = val
        ? { ...value, ["weekIndex"]: null, ["weekDays"]: [], ["isEachSelected"]: val }
        : { ...value, ["isEachSelected"]: val };

      onSelect(selectPayload);
    }

    function handleCalendarSelect(item: any) {
      let updatedItems: any[] = [];
      setSelectedMonthDates((prevState) => {
        if (prevState.includes(item)) {
          updatedItems = prevState.filter((id) => id !== item);
          return updatedItems;
        } else {
          // Add the new id (toggle on)
          updatedItems = [...prevState, item];
          return updatedItems;
        }
      });
      onSelect({ ...value, [keyName]: updatedItems });
    }

    function handleSelectWeekIndex(item) {
      setWeekIndex(item);
      onSelect({ ...value, ["weekIndex"]: item.id });
    }

    function handleSelectWeekDate(item) {
      setWeekDate(item.id);
      onSelect({ ...value, ["weekDays"]: [item.id] });
    }

    return (
      <View style={styles.wrapper}>
        <Pressable style={[styles.inputContainer]} onPress={() => toggleEachSelect(true)}>
          <Typography.B2 style={[styles.labelStyle, { color: colors.textPrimary }]} numberOfLines={1}>
            {t("Each")}
          </Typography.B2>

          {isEachSelected && (
            <Icons.WhiteTick
              width={styles.whiteTick.width}
              height={styles.whiteTick.height}
              color={colors.textPrimary}
            />
          )}
        </Pressable>

        <Pressable style={[styles.inputContainer]} onPress={() => toggleEachSelect(false)}>
          <Typography.B2 style={[styles.labelStyle, { color: colors.textPrimary }]} numberOfLines={1}>
            {t("OnThe")}
          </Typography.B2>

          {!isEachSelected && (
            <Icons.WhiteTick
              width={styles.whiteTick.width}
              height={styles.whiteTick.height}
              color={colors.textPrimary}
            />
          )}
        </Pressable>

        {isEachSelected ? (
          <View style={[styles.monthlyCalendarList]}>
            {MONTH_DAYS?.map((item, idx) => (
              <Pressable onPress={() => handleCalendarSelect(item)} key={item} style={[styles.monthlyCalendarItem]}>
                <View style={selectedMonthDates.includes(item) && styles.selectedCalendarItem} />
                <Typography.B2 style={{ textAlign: "center" }} numberOfLines={1}>
                  {item}
                </Typography.B2>
              </Pressable>
            ))}
          </View>
        ) : (
          <View style={{ flexDirection: "row" }}>
            <View style={styles.menuList}>
              <FlatList
                data={weekIndexOptions}
                renderItem={({ item }) => (
                  <Pressable
                    style={[styles.menuItem, item.id === weekIndex && styles.selectedMenuItem]}
                    onPress={() => {
                      handleSelectWeekIndex(item);
                    }}
                  >
                    <Typography.B2 style={styles.menuItemText}>{item.label}</Typography.B2>
                  </Pressable>
                )}
                keyExtractor={(item) => String(item.id)}
                ItemSeparatorComponent={() => <View style={styles.separator} />}
              />
            </View>

            <View style={styles.menuList}>
              <FlatList
                data={dayOptions}
                renderItem={({ item }) => (
                  <Pressable
                    style={[styles.menuItem, weekDate === item.id && styles.selectedMenuItem]}
                    onPress={() => {
                      handleSelectWeekDate(item);
                    }}
                  >
                    <Typography.B2 style={styles.menuItemText}>{item.label}</Typography.B2>
                  </Pressable>
                )}
                keyExtractor={(item) => String(item.id)}
                ItemSeparatorComponent={() => <View style={styles.separator} />}
              />
            </View>
          </View>
        )}
        <ErrorMessage error={error}/>
      </View>
    );
  }

  return (
    <View>
      <InputField
        name={inputName}
        keyName="frequency"
        displayValue={customSchedule?.frequency?.label}
        label={t("Frequency")}
        control={control}
        visibility={true}
        trigger="onSelect"
        component={AnimatedInputDropdown}
        menu={options}
      />

      <InputField
        name={inputName}
        keyName="interval"
        displayValue={
          customSchedule?.interval?.id
            ? customSchedule?.interval?.id + " " + getFrequencyName(customSchedule?.frequency?.label)
            : customSchedule?.interval + " " + getFrequencyName(customSchedule?.frequency?.label)
        }
        label={t("Every")}
        control={control}
        trigger="onSelect"
        component={AnimatedInputDropdown}
        menu={MOCK_DATA.MEDICATION_DAILY_ROUTINE}
      />

      <InputField
        name={inputName}
        keyName="monthDates"
        visibility={true}
        label={t("Every")}
        control={control}
        trigger="onSelect"
        component={MonthlyRoutineList}
      />
    </View>
  );
};

export default CustomMonthlyReminder;
