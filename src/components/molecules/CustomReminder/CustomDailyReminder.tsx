import { FC } from 'react';
import { View } from 'react-native';
import InputField from '../Field/InputField';
import AnimatedInputDropdown from '@/components/atoms/AnimatedInputDropdown/AnimatedInputDropdown';
import { MOCK_DATA } from '@/mock-data/mockData';
import { useTranslation } from 'react-i18next';
import { useWatch } from 'react-hook-form';
import useTaskManagerContainer from '@/containers/taskManager/useTaskManagerContainer';

interface ICustomDailyReminder {
  control: any;
  inputName: string;
  options: { id: number; label: string; value: number }[];
}

const CustomDailyReminder: FC<ICustomDailyReminder> = ({
  control,
  inputName,
  options
}) => {
  const { t } = useTranslation(['taskManager']);

  const { getFrequencyName } = useTaskManagerContainer();
  const customSchedule = useWatch({ control, name: inputName });

  return (
    <View>
      <InputField
        name={inputName}
        keyName="frequency"
        displayValue={customSchedule?.frequency?.label}
        label={t('Frequency')}
        control={control}
        trigger="onSelect"
        component={AnimatedInputDropdown}
        menu={options}
      />

      <InputField
        name={inputName}
        keyName="interval"
        displayValue={
          customSchedule?.interval?.id
            ? customSchedule?.interval?.id +
              ' ' +
              getFrequencyName(customSchedule?.frequency?.label)
            : customSchedule?.interval +
              ' ' +
              getFrequencyName(customSchedule?.frequency?.label)
        }
        label={t('Every')}
        control={control}
        trigger="onSelect"
        component={AnimatedInputDropdown}
        menu={MOCK_DATA.MEDICATION_DAILY_ROUTINE}
      />
    </View>
  );
};

export default CustomDailyReminder;
