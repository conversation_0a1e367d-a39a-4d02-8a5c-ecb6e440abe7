/** @format */

import { FC, useState } from "react";
import { FlatList, Pressable, View } from "react-native";
import InputField from "../Field/InputField";
import AnimatedInputDropdown from "@/components/atoms/AnimatedInputDropdown/AnimatedInputDropdown";
import { MOCK_DATA } from "@/mock-data/mockData";
import { useTranslation } from "react-i18next";
import {useWatch } from "react-hook-form";
import { Typography } from "@/components/atoms";
import { getCustomReminderStyle } from "./CustomReminder.style";
import Icons from "@/theme/assets/images/svgs/icons";
import useTaskManagerContainer from "@/containers/taskManager/useTaskManagerContainer";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import ErrorMessage from "./ErrorMessage";

interface ICustomWeeklyReminder {
  control: any;
  inputName: string;
  options: { id: number; label: string; value: number }[];
}

const CustomWeeklyReminder: FC<ICustomWeeklyReminder> = ({ control, inputName, options }) => {
  const { t } = useTranslation(["taskManager"]);
  const styles: any = useDynamicStyles(getCustomReminderStyle);
  const { getFrequencyName } = useTaskManagerContainer();
  const customSchedule = useWatch({ control, name: inputName });

  function WeekdaysList({ onSelect, value, keyName, error }) {
    const [selectedId, setSelectedId] = useState<number[]>([...value?.[keyName]]);
    function handleDropdownSelect(item: any) {
      let updatedItems: any[] = [];
      setSelectedId((prevState) => {
        if (prevState.includes(item.id)) {
          updatedItems = prevState.filter((id) => id !== item.id);
          return updatedItems;
        } else {
          updatedItems = [...prevState, item.id];
          return updatedItems;
        }
      });
      onSelect({ ...value, [keyName]: updatedItems, weekIndex: null });
    }
    return (
      <View style={[styles.weeklyMenuList]}>
        <FlatList
          data={MOCK_DATA.MEDICATION_WEEKLY_ROUTINE}
          renderItem={({ item }) => (
            <Pressable
              style={[styles.weeklyMenuItem]}
              onPress={() => {
                handleDropdownSelect(item);
              }}
            >
              <View style={styles.contentContainer}>
                <Typography.B2 style={styles.menuItemText}>{item.label}</Typography.B2>

                {selectedId.includes(item.id) && (
                  <Icons.WhiteTick width={styles.whiteTick.width} height={styles.whiteTick.height} />
                )}
              </View>
            </Pressable>
          )}
          keyExtractor={(item) => String(item.id)}
          ItemSeparatorComponent={() => <View style={styles.separator} />}
        />
        <ErrorMessage error={error}/>
      </View>
    );
  }
  return (  

    <View>
      <InputField
        name={inputName}
        keyName="frequency"
        displayValue={customSchedule?.frequency?.label}
        label={t("Frequency")}
        control={control}
        visibility={true}
        trigger="onSelect"
        component={AnimatedInputDropdown}
        menu={options}
      />

      <InputField
        name={inputName}
        keyName="interval"
        displayValue={
          customSchedule?.interval?.id
            ? customSchedule?.interval?.id + " " + getFrequencyName(customSchedule?.frequency?.label)
            : customSchedule?.interval + " " + getFrequencyName(customSchedule?.frequency?.label)
        }
        label={t("Every")}
        control={control}
        trigger="onSelect"
        component={AnimatedInputDropdown}
        menu={MOCK_DATA.MEDICATION_DAILY_ROUTINE}
      />

      <Typography.B5 style={styles.smallText}>{t("medicationTakenEveryWeek")}</Typography.B5>

      <InputField
        name={inputName}
        keyName="weekDays"
        visibility={true}
        label={t("Every")}
        control={control}
        trigger="onSelect"
        component={WeekdaysList}
      />
    </View>
  );
};

export default CustomWeeklyReminder;
