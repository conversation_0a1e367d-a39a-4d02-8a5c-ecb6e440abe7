import { Fonts } from '@/constants';

import { Theme } from '@/types/theme/theme';
import { Dimensions } from 'react-native';
import { ms, ScaledSheet, verticalScale } from 'react-native-size-matters';

const { width: screenWidth } = Dimensions.get('screen');

export const getCustomReminderStyle = (theme: Theme)=> ScaledSheet.create({
  wrapper: {
    marginVertical: verticalScale(8)
  },
  smallText: {
    paddingVertical: ms(10),
    paddingHorizontal: ms(16)
  },
  weeklyMenuList: {
    marginVertical: ms(10),
    flex: 1,
    paddingHorizontal: ms(16)
  },
  weeklyMenuItem: {
    flexDirection: 'row',
    borderBottomColor: theme.colors.mediumGray,
    borderBottomWidth: 1,
    paddingVertical: ms(4)
  },
  menuItem: {
    paddingVertical: ms(4),
    alignSelf: 'center',
    paddingHorizontal: screenWidth / 8
  },
  dailyMenuItem: {
    marginHorizontal: screenWidth / 6,
    paddingHorizontal: ms(10),
    flexDirection: 'row'
  },
  contentContainer: {
    flexDirection: 'row',
    flex: 1,
    height: ms(30),
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  selectedMenuItem: {
    backgroundColor: theme.colors.lightMediumGray,
    borderRadius: ms(8)
  },
  menuItemText: {
    textAlign: 'center'
  },
  separator: {
    height: ms(4) // Adjust this for more or less spacing
  },
  whiteTick: {
    width: ms(15),
    height: ms(26.25)
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: verticalScale(8),
    paddingHorizontal: ms(16),
    borderBottomColor: theme.colors.mediumGray,
    borderBottomWidth: 1
  },
  labelStyle: {
    fontFamily: Fonts.RALEWAY_REGULAR,
    color: theme.colors.white,
    marginBottom: ms(10)
  },
  valueStyle: {
    fontFamily: Fonts.RALEWAY_REGULAR,
    color: theme.colors.textPrimaryYellow,
    marginBottom: ms(10)
  },
  menuList: {
    marginVertical: ms(10),
    justifyContent: 'center',
  },
  monthlyCalendarList: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    backgroundColor: theme.colors.lightMediumGray,
    borderRadius: ms(10),
    padding: ms(10)
  },
  monthlyCalendarItem: {
    width: '14%',
    padding: ms(10),
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: ms(5),
  },
  selectedCalendarItem: {
    backgroundColor: theme.colors.primary,
    borderRadius: ms(80),
    width: ms(40),
    height: ms(40),
    position: 'absolute'
  },
  yearMenuList: {
    flexDirection: 'row',
    flex: 1,
    flexWrap: 'wrap',
    borderRadius: ms(10),
    overflow: 'hidden',
    backgroundColor: theme.colors.lightMediumGray,
  },
  yearMenuItem: {
    width: '25%',
    justifyContent: 'center',
    alignItems: 'center',
    padding: ms(10),
  },
  selectedYearItem: {
    backgroundColor: theme.colors.primary
  },
   errorMessage: {
      fontFamily: Fonts.RALEWAY_REGULAR,
      color: theme.colors.red,
      marginTop: verticalScale(8),
    },
});
