/** @format */

import React, { FC } from "react";
import { getErrorMessage } from "@/utils/taskManager";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { getCustomReminderStyle } from "./CustomReminder.style";
import { Typography } from "@/components/atoms";

interface erroMessageProps {
  error: string | {} | undefined;
}
const ErrorMessage: FC<erroMessageProps> = ({ error }) => {
  const errorMessage = getErrorMessage(error);
  const styles: any = useDynamicStyles(getCustomReminderStyle);

  return (
    <>
      {!!error && <Typography.B4 style={styles.errorMessage}>{errorMessage}</Typography.B4>}
    </>
  );
};

export default ErrorMessage;
