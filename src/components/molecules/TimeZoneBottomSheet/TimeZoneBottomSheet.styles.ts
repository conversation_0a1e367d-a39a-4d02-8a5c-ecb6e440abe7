import { Fonts } from "@/constants";
import { Theme } from "@/types/theme/theme";
import { SCREEN_WIDTH } from "@/theme/_config";
import { ms,s, ScaledSheet } from "react-native-size-matters";

const getTimeZoneBottomSheetStyles = (theme: Theme) =>
  ScaledSheet.create({
    timeZoneSheetContainer: {
      flex: 1,
      paddingVertical: ms(24),
    },
    timeZoneSheetTitle: {
      textAlign: "center",
      marginBottom: ms(24),
      fontFamily: Fonts.RALEWAY_BOLD,
    },
    timeZoneSection: {
      marginBottom: ms(16),
    },
    timeZoneDropdownWrapper: {
      marginTop: ms(8),
    },
    timeZoneUpdateButton: {
      marginTop: ms(32),
      width: SCREEN_WIDTH * 0.82,
      alignSelf: "center",
    },
    dropdownButton: {
      backgroundColor: theme.colors.item_secondary_bg,
      borderRadius: ms(10),
      paddingVertical: ms(10),
      paddingHorizontal: ms(12),
      minHeight: ms(40),
      overflow:'hidden'
    },
    dropdownButtonOpen: {
      borderBottomLeftRadius: 0,
      borderBottomRightRadius: 0,
    },
    dropdownText: {
      color: "#fff",
      fontSize: ms(14),
    },
    dropdownStyle: {
      backgroundColor: theme.colors.item_secondary_bg,
      borderBottomEndRadius: ms(10),
      borderBottomStartRadius: ms(10),
    },
    textPink: {
      color: theme.colors.textPrimaryYellow,
      fontSize: ms(11)
    },
    optionStyle: {
      borderBottomColor: theme.colors.gray,
      borderBottomWidth: 0.5,
    },
    btnText: {
      color: theme.colors.white,
    },
    buttonContentLeft:{
      width: ms(118)
    }
  });

export default getTimeZoneBottomSheetStyles;
