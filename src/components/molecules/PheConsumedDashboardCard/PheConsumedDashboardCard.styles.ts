import { ScaledSheet, ms } from "react-native-size-matters";
import { Theme } from "@/types/theme/theme";
import { RFPercentage } from "react-native-responsive-fontsize";

const getPheConsumedStyles = (theme:Theme) => ScaledSheet.create({
  container: {
    backgroundColor: theme.colors.tile_bg,
    padding: ms(16),
    borderRadius: ms(16),
    flex: 1,
    marginLeft: ms(8),
  },
  iconContainer: {
    position: "absolute",
    top: ms(16),
    right: ms(16),
  },
  title: {
    color: theme.colors.textPrimary,
    marginBottom: ms(8),
    fontSize: RFPercentage(2.25),
  },
  pheConsumedContainer: {
    flexDirection: "row", // Align items horizontally
    alignItems: "center", // Center items vertically
    justifyContent: "center", // Center items horizontally
    marginBottom: ms(4), // Space below the container
    borderWidth: 2, // Add border
    borderColor: theme.colors.white, // Set border color to white
    borderRadius: ms(10), // Rounded corners
    paddingVertical: ms(4), // Vertical padding for content spacing
    paddingHorizontal: ms(8), // Horizontal padding for content spacing
    alignSelf: "flex-start", // Adjust width based on content
  },

  details: {
    color: theme.colors.textPrimary,
    fontSize: ms(12),
    marginTop: ms(4),
  },
});

export default getPheConsumedStyles;
