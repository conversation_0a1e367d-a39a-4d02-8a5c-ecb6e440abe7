import { ScaledSheet, ms } from 'react-native-size-matters';
import { Fonts } from '@/constants';
import { Theme } from '@/types/theme/theme';

const getConsumptionToggleStyle = (theme: Theme) =>
	ScaledSheet.create({
		container: {
			backgroundColor: theme.colors.tile_bg,
			padding: ms(16),
			borderRadius: ms(12),
			marginBottom: ms(10),
		},
		label: {
			fontFamily: Fonts.RALEWAY_REGULAR,
			fontSize: ms(14),
			marginBottom: ms(12),
			color: theme.colors.textPrimary,
		},
		group: {
			flexDirection: 'row',
			justifyContent: 'space-between',
		},
		button: {
			borderRadius: ms(12),
			borderWidth: ms(1),
			borderColor: theme.colors.mediumGray,
			backgroundColor: 'transparent',
			width: ms(145),
		},
		buttonLeft: {
			marginRight: ms(8),
			width: ms(145),
		},
		text: {
			fontFamily: Fonts.RALEWAY_BOLD,
			fontSize: ms(14),
			color: theme.colors.textPrimary,
		},
		activeButton: {
			backgroundColor: theme.colors.primary,
			borderColor: theme.colors.primary,
		},
		activeText: {
			color: theme.colors.white,
		},
	});

export default getConsumptionToggleStyle;
