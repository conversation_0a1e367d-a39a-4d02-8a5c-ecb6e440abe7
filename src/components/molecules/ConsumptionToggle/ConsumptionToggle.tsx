import React from 'react';
import { View } from 'react-native';
import { Typography, Button } from '@/components/atoms';
import getConsumptionToggleStyle from './ConsumptionToggle.style';
import { useDynamicStyles } from '@/theme/hooks/useDynamicStyles';

interface ConsumptionToggleProps {
	selected: 'Phe' | 'Protein';
	onSelect: (value: 'Phe' | 'Protein') => void;
	style?: any;
}

const ConsumptionToggle: React.FC<ConsumptionToggleProps> = ({ selected, onSelect, style }) => {
	const styles: any = useDynamicStyles(getConsumptionToggleStyle);

	return (
		<View style={[styles.container, style]}>
			<Typography.B2 style={styles.label}>Track my consumption using:</Typography.B2>
			<View style={styles.group}>
				<Button.Outline onPress={() => onSelect('Phe')} style={[styles.button, styles.buttonLeft, selected === 'Phe' && styles.activeButton]}>
					<Typography.B2 style={[styles.text, selected === 'Phe' && styles.activeText]}>Phe</Typography.B2>
				</Button.Outline>

				<Button.Outline onPress={() => onSelect('Protein')} style={[styles.button, selected === 'Protein' && styles.activeButton]}>
					<Typography.B2 style={[styles.text, selected === 'Protein' && styles.activeText]}>Protein</Typography.B2>
				</Button.Outline>
			</View>
		</View>
	);
};

export default ConsumptionToggle;
