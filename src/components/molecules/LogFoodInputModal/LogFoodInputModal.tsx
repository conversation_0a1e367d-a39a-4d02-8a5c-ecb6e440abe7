/** @format */

// LogFoodInput.tsx
import React, { useEffect, useState } from "react";
import { KeyboardTypeOptions, ScrollView, View } from "react-native";
import { ms } from "react-native-size-matters";

import Button from "@/components/atoms/Button/Button";
import CustomTextInput from "@/components/atoms/CustomTextInput/CustomTextInput";
import Dropdown from "@/components/atoms/Dropdown/Dropdown";
import Loading from "@/components/atoms/Loading/Loading";
import TabSelector from "@/components/atoms/TabSelector/TabSelector";
import Typography from "@/components/atoms/Typography/Typography";
import { getFoodById } from "@/services/api/dietTrackerAPI";
import { useAppDispatch, useAppSelector } from "@/store";
import {
  cleanApiState,
  fetchPortions,
  selectDietTracker,
  selectDietTrackerLoading,
  selectFoodEntries,
  updateFoodEntry,
} from "@/store/slices/dietTrackerSlice";
import { useTheme } from "@/theme";
import { config } from "@/theme/_config";
import Common from "@/theme/common.style";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { FoodEntryItem } from "@/types/schemas/dietTracker";
import { DietTrackerService } from "@/utils/dietTrackerService";
import { removeTrailingZeros, roundNumber, updateDateWithTimeSlot, validateNonZeroNumericInput } from "@/utils/helpers";
import getLogFoodInputStyles from "./LogFoodInputModal.style";
import RNCheckbox from "@/components/atoms/RNCheckbox/RNCheckbox";
import { selectIsSimplifiedDiet } from "@/store/slices/settingsSlice";
import BottomSheetWrapper from "@/components/atoms/BottomSheetWrapper/BottomSheetWrapper";
import { useSelector } from "react-redux";

interface LogFoodInputProps {
  selectedFood?: FoodEntryItem | null;
  onLogFood: (food: any | undefined) => void;
  onDeleteFood?: () => void;
  isUpdating?: boolean;
  foodsData?: any;
  onClose: () => void;
  isVisible: boolean;
}

const LogFoodInputModal: React.FC<LogFoodInputProps> = ({
  selectedFood,
  onLogFood,
  onDeleteFood,
  isUpdating,
  foodsData,
  onClose,
  isVisible,
}) => {
  const [selectedTab, setSelectedTab] = useState("Quantity");
  const [isFocused, setIsFocused] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [isFoodInsideMeal, setIsFoodInsideMeal] = useState(false);
  const [quantityError, setQuantityError] = useState<string | null>(null);
  const [foodData, setFoodData] = useState<FoodDataState>({
    finalProtein: "",
    finalPhe: "",
    finalQuantity: "",
    pheFactor: 0,
    proteinFactor: 0,
    gramWeight: 0,
    selectedUnit: "",
    isFreeFood: !!selectedFood?.isFreeFood,
    isMeal: true,
  });

  const { colors } = useTheme();
  const styles: any = useDynamicStyles(getLogFoodInputStyles);

  const dispatch = useAppDispatch();
  const loadingPortions = useAppSelector(selectDietTrackerLoading);
  const isSimplifiedDiet = useAppSelector(selectIsSimplifiedDiet);
  const foodEntries = useSelector(selectFoodEntries) || [];

  const [portions, setPortions] = useState([] as any);

  const { selectedDietDate, selectedTimeSlot } = useAppSelector(selectDietTracker);

  useEffect(() => {
    if (selectedFood && foodsData?.items) {
      // Find if any meal contains the selected food in its items array
      const isFoodInMeal = foodsData.items.some(
        (meal: any) => meal.category === "Meal" && meal.items.some((item: any) => item.food_id === selectedFood.food_id)
      );

      setIsFoodInsideMeal(isFoodInMeal);
    } else {
      setIsFoodInsideMeal(false);
    }
  }, [selectedFood, foodsData]);

  // Initialize values when a food is selected
  useEffect(() => {
    if (selectedFood) {
      dispatch(fetchPortions());
      if (selectedFood?.food_id || selectedFood?.id) {
        getFoodById(
          (selectedFood.food_id as string) || (`${selectedFood.id}` as string),
          selectedFood?.editQuantity || 1,
          selectedFood?.editUnit,
          selectedFood.editGramWeight || 0
        )
          .then((resV2) => {
            if (resV2) {
              let phe_Factor = 0;
              let protein_Factor = 0;
              if (resV2.factors) {
                phe_Factor = resV2.factors.pheFactor || 0;
                protein_Factor = resV2.factors.proteinFactor || 0;
              }

              const isMeal = resV2?.category === "Meal" && resV2?.ingredients?.length > 1;

              let foodPayload = {
                finalPhe: resV2.phe.toFixed(2),
                finalProtein: resV2.protein.toFixed(2),
                pheFactor: phe_Factor,
                proteinFactor: protein_Factor,
                finalQuantity: resV2.quantity.toFixed(2),
                gramWeight: resV2.gram_weight,
                selectedUnit: resV2.unit,
                isFreeFood: !!selectedFood?.isFreeFood,
              };

              if (isMeal && isSimplifiedDiet) {
                const totals = resV2?.ingredients?.reduce(
                  (acc: { phe: number; protein: number }, ingredient) => {
                    // Only count nutrients if is_free is false
                    if (!ingredient?.user_flags?.is_free) {
                      const pheNutrient = ingredient?.nutrients?.find(
                        (n: { name: string; amount: number; unit: string }) => n.name === "phe"
                      );
                      const proteinNutrient = ingredient?.nutrients?.find(
                        (n: { name: string; amount: number; unit: string }) => n.name === "protein"
                      );

                      return {
                        phe: acc.phe + (pheNutrient?.amount || 0),
                        protein: acc.protein + (proteinNutrient?.amount || 0),
                      };
                    }
                    return acc;
                  },
                  { phe: 0, protein: 0 }
                );

                const quantity = resV2?.quantity || 1;
                // Update foodData with the calculated totals
                foodPayload = {
                  ...foodPayload,
                  finalPhe: (totals.phe * quantity).toFixed(2),
                  finalProtein: (totals.protein * quantity).toFixed(2),
                };
              }

              setFoodData((prev) => ({
                ...prev,
                ...foodPayload,
                isMeal: isMeal,
              }));

              const portions_ = resV2.portions?.map((portion: any) => ({
                id: portion?.name,
                text: portion?.name,
                gramWeight: Number(portion?.gram_weight),
              }));
              setPortions(portions_ || []);
            }
          })
          .catch((err) => {
            console.log({ err });
          });
      }

      return () => {
        setFoodData((prev) => ({ ...prev, selectedUnit: "" }));
        setSelectedTab("Quantity");
      };
    }
  }, [JSON.stringify(selectedFood)]);

  // Cleanup state on unmount
  useEffect(() => {
    return () => {
      dispatch(cleanApiState());
    };
  }, [dispatch]);

  const handleFocus = () => setIsFocused(true);
  const handleBlur = () => setIsFocused(false);
  const handleLogOrUpdateFood = async () => {
    if (!foodData.finalQuantity || Number(foodData.finalQuantity) <= 0) {
      setQuantityError("Quantity is required and must be greater than 0.");
      return;
    } else {
      setQuantityError(""); // Clear error if valid
    }

    if (isUpdating && selectedFood) {
      const time = updateDateWithTimeSlot(`${selectedDietDate}`, selectedTimeSlot);

      let entryData;

      if (selectedFood?.isMultiItemScan) {
        // Handle multi-item scans
        const previousFoodEntries = foodEntries?.filter((item) => item?._id === selectedFood?.entryId);
        const dataToBeUpdated = previousFoodEntries?.map((item) =>
          item?.unique_entry_id === selectedFood?.unique_entry_id
            ? {
                description: selectedFood.description || "",
                quantity: Number(foodData.finalQuantity)?.toFixed(2),
                unit: foodData.selectedUnit,
                gram_weight: foodData.gramWeight,
                phe: Number(foodData.finalPhe)?.toFixed(2),
                protein: Number(foodData.finalProtein)?.toFixed(2),
                user_flags: {
                  is_free: foodData.isFreeFood,
                },
                food_id: selectedFood.food_id,
                detection_id: selectedFood?.detection_id || "",
              }
            : {
                description: item?.description || "",
                quantity: Number(item?.quantity)?.toFixed(2),
                unit: item?.unit,
                gram_weight: item?.gram_weight,
                phe: Number(item?.phe)?.toFixed(2),
                protein: Number(item?.protein)?.toFixed(2),
                user_flags: {
                  is_free: item?.items[0]?.isFreeFood ?? false, // keep each item's own flag
                },
                food_id: item?.food_id,
                detection_id: item?.detection_id || "",
              }
        );

        entryData = {
          entryId: selectedFood?.entryId,
          time,
          description: selectedFood.description || "",
          items: dataToBeUpdated,
        };
      } else {
        // Handle single-item or normal updates
        entryData =
          selectedFood?.items?.length > 1
            ? {
                entryId: selectedFood?.entryId,
                time,
                description: selectedFood.description || "",
                items: [
                  {
                    description: selectedFood.description || "",
                    quantity: Number(foodData.finalQuantity)?.toFixed(2),
                    unit: foodData.selectedUnit,
                    gram_weight: foodData.gramWeight,
                    phe: Number(foodData.finalPhe)?.toFixed(2),
                    protein: Number(foodData.finalProtein)?.toFixed(2),
                    user_flags: {
                      is_free: foodData.isFreeFood,
                    },
                    food_id: selectedFood.food_id,
                    detection_id: selectedFood?.detection_id || "",
                  },
                ],
              }
            : {
                entryId: selectedFood?.entryId,
                time,
                description: selectedFood.description || "",
                items: [
                  {
                    description: selectedFood.description || "",
                    quantity: Number(foodData.finalQuantity)?.toFixed(2),
                    unit: foodData.selectedUnit,
                    gram_weight: foodData.gramWeight,
                    phe: Number(foodData.finalPhe)?.toFixed(2),
                    protein: Number(foodData.finalProtein)?.toFixed(2),
                    user_flags: {
                      is_free: foodData.isFreeFood,
                    },
                    food_id: selectedFood.food_id,
                    detection_id: selectedFood?.detection_id || "",
                  },
                ],
              };
      }
      try {
        await dispatch(updateFoodEntry(entryData)).unwrap();
        onLogFood({}); // Notify parent component
      } catch (err) {
        // console.error("Failed to update food entry:", err);
      }
    } else {
      // Log food (create new entry)
      onLogFood({
        quantity: Number(foodData.finalQuantity)?.toFixed(2),
        phe: Number(foodData.finalPhe)?.toFixed(2),
        unit: foodData.selectedUnit,
        protein: Number(foodData.finalProtein)?.toFixed(2),
        gram_weight: foodData.gramWeight,
        isFreeFood: foodData.isFreeFood,
      });
    }
  };

  // const handleLogOrUpdateFood = async () => {
  //   if (!foodData.finalQuantity || Number(foodData.finalQuantity) <= 0) {
  //     setQuantityError("Quantity is required and must be greater than 0.");
  //     return;
  //   } else {
  //     setQuantityError(""); // Clear error if valid
  //   }

  //   if (isUpdating && selectedFood) {
  //     const time = updateDateWithTimeSlot(`${selectedDietDate}`, selectedTimeSlot);

  //     const entryData =
  //       selectedFood?.items?.length > 1
  //         ? {
  //             entryId: selectedFood?.entryId, // Assuming `food_id` represents the entry ID
  //             time: time,
  //             description: selectedFood.description || "",
  //             items: [
  //               {
  //                 description: selectedFood.description || "",
  //                 quantity: Number(foodData.finalQuantity)?.toFixed(2),
  //                 unit: foodData.selectedUnit,
  //                 gram_weight: foodData.gramWeight,
  //                 phe: Number(foodData.finalPhe)?.toFixed(2),
  //                 protein: Number(foodData.finalProtein)?.toFixed(2),
  //                 user_flags: {
  //                   is_free: foodData.isFreeFood,
  //                 },
  //                 food_id: selectedFood.food_id,
  //                 detection_id: selectedFood?.detection_id
  //                   ? selectedFood?.detection_id
  //                   : "",
  //               },
  //             ],
  //           }
  //         : {
  //             entryId: selectedFood?.entryId, // Assuming `food_id` represents the entry ID
  //             time: time,
  //             description: selectedFood.description || "",
  //             items: [
  //               {
  //                 description: selectedFood.description || "",
  //                 quantity: Number(foodData.finalQuantity)?.toFixed(2),
  //                 unit: foodData.selectedUnit,
  //                 user_flags: {
  //                   is_free: foodData.isFreeFood,
  //                 },
  //                 gram_weight: foodData.gramWeight,
  //                 phe: Number(foodData.finalPhe)?.toFixed(2),
  //                 protein: Number(foodData.finalProtein)?.toFixed(2),
  //                 food_id: selectedFood.food_id,
  //                 detection_id: selectedFood?.detection_id
  //                   ? selectedFood?.detection_id
  //                   : "",
  //               },
  //             ],
  //           };

  //     try {
  //       await dispatch(updateFoodEntry(entryData)).unwrap();
  //       onLogFood({}); // Notify parent component
  //     } catch (err) {
  //       // console.error("Failed to update food entry:", err);
  //     }
  //   } else {
  //     // Log food (create new entry)
  //     onLogFood({
  //       quantity: Number(foodData.finalQuantity)?.toFixed(2),
  //       phe: Number(foodData.finalPhe)?.toFixed(2),
  //       unit: foodData.selectedUnit,
  //       protein: Number(foodData.finalProtein)?.toFixed(2),
  //       gram_weight: foodData.gramWeight,
  //       isFreeFood: foodData.isFreeFood,
  //     });
  //   }
  // };

  const renderInputField = () => {
    // Move the conversion function inside renderInputField
    const conversion = (field: string) => (text: string) => {
      if (text.trim() !== "" && Number(text) > 0) {
        setQuantityError(null);
      } else {
        switch (field) {
          case "servings":
            setQuantityError("Quantity is required and must be greater than 0.");
            break;
        }
      }
      // Handle empty input case
      if (text === "") {
        switch (field) {
          case "servings":
            setFoodData((prev) => ({ ...prev, finalQuantity: "" }));
            break;
          case "protein":
            setFoodData((prev) => ({ ...prev, finalProtein: "" }));
            break;
          case "phe":
            setFoodData((prev) => ({ ...prev, finalPhe: "" }));
            break;
        }
        return;
      }

      // Validate input
      if (!validateNonZeroNumericInput(text)) return;
      switch (field) {
        case "servings": {
          const quan_change = DietTrackerService.onQtyChange(
            foodData.proteinFactor,
            foodData.pheFactor,
            Number(text),
            foodData.gramWeight
          );
          setFoodData((prev) => ({
            ...prev,
            finalPhe: quan_change.phe.toFixed(2),
            finalProtein: quan_change.protein.toFixed(2),
            finalQuantity: text,
          }));
          break;
        }
        case "protein": {
          const protein_change = DietTrackerService.onProteinChange(
            foodData.proteinFactor,
            foodData.pheFactor,
            Number(text),
            foodData.gramWeight
          );
          setFoodData((prev) => ({
            ...prev,
            finalPhe: protein_change.phe.toFixed(2),
            finalProtein: text,
            finalQuantity: protein_change.quantity.toFixed(2),
          }));
          break;
        }
        case "phe": {
          const phe_change = DietTrackerService.onPheChange(
            foodData.proteinFactor,
            foodData.pheFactor,
            Number(text),
            foodData.gramWeight
          );
          setFoodData((prev) => ({
            ...prev,
            finalPhe: text,
            finalProtein: phe_change.protein.toFixed(2),
            finalQuantity: phe_change.quantity.toFixed(2),
          }));
          break;
        }
      }
    };

    const inputProps = {
      placeholderTextColor: config.colors.gray,
      keyboardType: "numeric" as KeyboardTypeOptions,
      style: styles.pheTextInput,
      onFocus: handleFocus,
      onBlur: handleBlur,
    };

    const handleUnitChange = (newUnit: string) => {
      // If quantity exists, recalculate based on new unit conversion
      setFoodData((prev) => ({ ...prev, selectedUnit: newUnit }));
      const portion = portions.find((p: { text: string }) => p.text === newUnit);
      setFoodData((prev) => ({
        ...prev,
        gramWeight: Number(portion?.gramWeight || 0),
      }));
      if (foodData.finalQuantity) {
        // quantity
        const unit_change = DietTrackerService.onQtyChange(
          foodData.proteinFactor,
          foodData.pheFactor,
          Number(foodData.finalQuantity),
          Number(portion?.gramWeight || 0)
        );
        setFoodData((prev) => ({
          ...prev,
          finalPhe: unit_change.phe.toFixed(2),
          finalProtein: unit_change.protein.toFixed(2),
          finalQuantity: unit_change.quantity.toFixed(2),
        }));
      }
    };

    const inputDetails = {
      Quantity: {
        value: foodData.finalQuantity,
        onChangeText: conversion("servings"),
        placeholder: "Enter quantity",
      },
      Protein: {
        value: foodData.finalProtein, //protein,
        onChangeText: conversion("protein"),
        placeholder: "Enter protein amount",
        unit: "g",
      },
      Phe: {
        value: foodData.finalPhe, // phe,
        onChangeText: conversion("phe"),
        placeholder: "Enter phe amount",
        unit: "mg",
      },
    }[selectedTab];

    return (
      <View>
        <View style={[styles.pheInputContent, isFocused && styles.pheTextInputFocused]}>
          <CustomTextInput {...inputProps} {...inputDetails} unit={inputDetails?.unit} />

          {selectedTab === "Quantity" && (
            <Dropdown
              isVisible={showDropdown}
              data={portions}
              selectedValue={foodData.selectedUnit}
              setSelectedValue={handleUnitChange}
              onToggle={() => setShowDropdown((prevState) => !prevState)}
              buttonStyle={{
                height: ms(38),
                maxWidth: ms(180),
              }}
              // disabled={!finalQuantity}
            />
          )}
        </View>

        {/* Error Message Below Input Field */}
        {quantityError ? <Typography.B3 style={styles.errorText}>{quantityError}</Typography.B3> : null}

        <RNCheckbox.FreeFood
          isMeal={foodData.isMeal}
          key={`freeFood-${!!foodData?.isFreeFood}`}
          value={foodData?.isFreeFood}
          onSelect={() => setFoodData((prev) => ({ ...prev, isFreeFood: !prev.isFreeFood }))}
          checkedfillColor={colors.primary}
        />
      </View>
    );
  };

  return (
    <BottomSheetWrapper
      isVisible={isVisible}
      onClose={() => {
        onClose();
        setSelectedTab("Quantity");
      }}
    >
      <View style={styles.header}>
        <Typography.H1 numberOfLines={4} style={[Common.textBold, styles.title]}>
          {selectedFood?.description ? selectedFood?.description : "Log Food"}
        </Typography.H1>
      </View>
      <ScrollView>
        <View style={[styles.container]}>
          <TabSelector tabs={["Quantity", "Protein", "Phe"]} selectedTab={selectedTab} onTabPress={setSelectedTab} />
          {renderInputField()}

          <View style={isUpdating ? styles.footer : styles.footer2}>
            <View style={styles.nutrientsContainer}>
              <View style={styles.nutrientBox}>
                <Typography.H1 style={styles.nutrientValue}>
                  {isSimplifiedDiet && foodData?.isFreeFood && !foodData?.isMeal
                    ? "FREE"
                    : `${roundNumber(Number(foodData.finalPhe || "0"))} mg`}
                </Typography.H1>
                <Typography.B2 style={styles.nutrientLabel}>Phe</Typography.B2>
              </View>
              <View style={styles.nutrientBox}>
                <Typography.H1 style={styles.nutrientValue}>
                  {isSimplifiedDiet && foodData?.isFreeFood && !foodData?.isMeal
                    ? "FREE"
                    : `${removeTrailingZeros(foodData.finalProtein || "0")} g`}
                </Typography.H1>

                <Typography.B2 style={styles.nutrientLabel}>Protein</Typography.B2>
              </View>
            </View>
            {isUpdating && !isFoodInsideMeal && (
              <Button.Outline onPress={onDeleteFood} style={styles.outlineButton}>
                <Typography.B1 style={[Common.textBold]}>Remove</Typography.B1>
              </Button.Outline>
            )}
          </View>

          {!isFoodInsideMeal && (
            <Button.Main onPress={handleLogOrUpdateFood}>
              <Typography.B1 style={[Common.textBold, { color: colors.white }]}>
                {isUpdating ? "Update" : "Log Food"}
              </Typography.B1>
            </Button.Main>
          )}

          <View style={styles.bottomSpace} />
        </View>
      </ScrollView>

      {/* {!loadingPortions ? null : <Loading />} */}
      {isVisible && !foodData.selectedUnit && !loadingPortions && <Loading />}
    </BottomSheetWrapper>
  );
};

export default LogFoodInputModal;
