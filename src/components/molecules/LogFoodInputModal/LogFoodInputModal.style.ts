import { ms, s, ScaledSheet, vs } from "react-native-size-matters";

import { Fonts } from "@/constants";
import { RFValue } from "react-native-responsive-fontsize";
import { Dimensions } from "react-native";
import { Theme } from "@/types/theme/theme";

const { height } = Dimensions.get("window");

const getLogFoodInputStyles = (theme: Theme) => ScaledSheet.create({
  container: {
    flex: 1,
  },
  footer: {
    marginTop: height < 700 ? vs(120) : vs(200), // Adjust margin based on screen height
  },
  footer2: {
     marginTop: ms(240), marginBottom: ms(30) 
    },
  nutrientsContainer: {
    flexDirection: "row",
    justifyContent: "space-evenly", // Distributes items evenly with reduced space
    alignItems: "center", // Ensures items are aligned properly
    marginBottom: ms(50), // Reduced from vs(50) to vs(20) for less spacing
    gap: vs(8), // Adds consistent spacing between items (adjust as needed)
  },

  nutrientBox: {
    alignItems: "center",
  },
  nutrientValue: {
    color: theme.colors.textPrimaryYellow,
    fontSize: ms(20),
  },
  nutrientLabel: {
    color: theme.colors.mediumGray,
    marginTop: vs(4),
  },
  pheInputContent: {
    width: "100%",
    alignItems: "center",
    justifyContent: "space-between",
    borderRadius: ms(8),
    borderWidth: ms(1),
    height: ms(40.5),
    flexDirection: "row",
    borderColor: theme.colors.mediumGray,
    marginTop: ms(20),
    overflow: "hidden",
  },
  pheTextInput: {
    flex: 1,
    paddingLeft: ms(12),
    fontSize: RFValue(13.5),
    borderWidth: 0,
  },
  pheTextInputFocused: {
    borderColor: theme.colors.yellow,
    borderWidth: ms(1),
  },
  pheUnitText: {
    color: theme.colors.yellow,
    marginHorizontal: ms(10),
    fontFamily: Fonts.RALEWAY_BOLD,
  },
  dropdownContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: ms(10),
    height: "100%",
    backgroundColor: theme.colors.gray,
    borderTopEndRadius: ms(8),
    borderBottomEndRadius: ms(8),
    width: ms(115),
  },
  dropdownOptions: {
    position: "absolute",
    right: 0,
    top: ms(92),
    backgroundColor: theme.colors.gray,
    width: ms(115),
    height: ms(200),
  },
  dropdownOption: {
    paddingVertical: ms(8),
    paddingHorizontal: ms(10),
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.darkGray,
  },
  dropdownOptionText: {
    marginRight: ms(10),
  },
  capturedPhoto: {
    width: "100%",
    height: 100,
    marginBottom: 20,
    resizeMode: "cover",
  },
  aiSuggestionsHeader: {
    fontSize: ms(16),
    fontWeight: "bold",
    color: theme.colors.white,
    marginBottom: ms(10),
  },
  aiSuggestionsContent: {
    paddingVertical: ms(10),
    paddingHorizontal: ms(20),
    backgroundColor: theme.colors.gray,
    borderRadius: ms(10),
  },
  aiMealName: {
    fontSize: ms(14),
    fontWeight: "bold",
    marginBottom: ms(10),
    color: theme.colors.white,
  },
  outlineButton: {
    borderColor: theme.colors.delete,
    paddingHorizontal: ms(20),
    borderRadius: 16,
    alignItems: "center",
    justifyContent: "center",
    marginVertical: ms(10),
    height: ms(47),
  },
  mealHeading: {
    textAlign: "center",
    marginBottom: ms(10),
  },
  bottomSpace: {
    marginBottom: ms(24),
  },
  errorText: {
    color: theme.colors.red,
    marginTop: ms(10),
  },
    header: {
      alignItems: "center",
      marginBottom: ms(40),
    },
    title: {
      marginTop: ms(20),
      fontSize: ms(22),
      textAlign: "center",
    },
});

export default getLogFoodInputStyles;
