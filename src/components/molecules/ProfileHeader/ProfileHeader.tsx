import React from "react";
import { View, Image, TouchableOpacity, ActivityIndicator } from "react-native";
import Svg, { G, Circle } from "react-native-svg";
import styles from "./ProfileHeader.styles";
import Icons from "@/theme/assets/images/svgs/icons";
import { Typography } from "@/components/atoms";
import { ms } from "react-native-size-matters";
import { useTheme } from "@/theme";

interface ProfileHeaderProps {
  name?: string;
  imageUrl?: string | null;
  isEditing?: boolean;
  onEditPress?: () => void;
  percentage?: number; // Optional progress percentage for tasks
  hasPercentage?: boolean;
  isUpdating?: boolean; // New prop to show loader when updating profile picture
}

const ProfileHeader: React.FC<ProfileHeaderProps> = ({
  name,
  imageUrl,
  isEditing,
  onEditPress,
  percentage = 0, // Default to 0 if not provided
  hasPercentage = false,
  isUpdating = false, // Default to false if not provided
}) => {
  const { variant, colors } = useTheme();

  return (
    <View style={styles.profileSection}>
      {/* Profile Image with Optional Progress Bar */}
      <View style={styles.profileContainer}>
        {percentage > 0 && (
          <Svg height={ms(75)} width={ms(75)} style={styles.svg}>
            <G rotation="-90" origin={`${ms(75) / 2},${ms(75) / 2}`}>
              <Circle
                cx={ms(75) / 2}
                cy={ms(75) / 2}
                r={ms(32)} // Correct radius
                stroke={colors.primary}
                strokeWidth={ms(4)} // Stroke thickness
                fill="none"
                strokeDasharray={2 * Math.PI * ms(32)} // Circle circumference
                strokeDashoffset={
                  percentage === 100
                    ? 0 // No offset for 100%
                    : 2 * Math.PI * ms(32) -
                      (2 * Math.PI * ms(32) * percentage) / 100
                }
              />
            </G>
          </Svg>
        )}

        {/* Profile Image & Loader Overlay */}
        <TouchableOpacity
          disabled={!isEditing || isUpdating} // Disable while updating
          onPress={onEditPress}
          style={styles.profileImageContainer}
        >
          {/* Profile Image */}
          {imageUrl ? (
            <Image
              source={{ uri: imageUrl, cache: "force-cache" }}
              style={[
                hasPercentage
                  ? styles.profileImageWithProgressBar
                  : styles.profileImage,
              ]}
            />
          ) : (
            <View
              style={[
                styles.avatarCircle,
                hasPercentage && styles.profileImageWithProgressBar,
                variant === "light" ? { backgroundColor: colors.white } : {
                   backgroundColor: colors.gray
                },
              ]}
            >
              {variant !== "light" ? (
                <Icons.AvatarLight
                  width={hasPercentage ? 28 : 48}
                  height={hasPercentage ? 28 : 48}
                />
              ) : (
                <Icons.Avatar
                  width={hasPercentage ? 28 : 48}
                  height={hasPercentage ? 28 : 48}
                />
              )}
            </View>
          )}

          {/* Loader Overlay */}
          {isUpdating && (
            <View style={styles.loaderOverlay}>
              <ActivityIndicator size="large" color={colors.primary} />
            </View>
          )}

          {/* Camera Icon for Editing */}
          {isEditing && !isUpdating && (
            <View style={styles.cameraIconContainer}>
              <Icons.Camera width={23} height={23} />
            </View>
          )}
        </TouchableOpacity>
      </View>

      {/* User Name */}
      {name && <Typography.H2 style={styles.profileName}>{name}</Typography.H2>}
    </View>
  );
};

export default ProfileHeader;
