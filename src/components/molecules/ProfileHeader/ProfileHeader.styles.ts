import { Fonts } from "@/constants";
import { config } from "@/theme/_config";
import { StyleSheet } from "react-native";
import { ms, vs } from "react-native-size-matters";

export default StyleSheet.create({
  profileSection: {
    alignItems: "center",
    justifyContent: "center",
  },
  profileImageContainer: {
    position: "relative",
    alignItems: "center",
    justifyContent: "center",
  },
  profileContainer: {
    position: "relative",
    width: ms(120),
    height: ms(120),
    justifyContent: "center",
    alignItems: "center",
  },
  svg: {
    position: "absolute",
  },
  profileImage: {
    width: ms(107), // Adjusted size to fit the progress circle
    height: ms(107),
    borderRadius: ms(60),
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: config.colors.gray,
  },
  profileImageWithProgressBar: {
    width: ms(55), // Reduced size for avatar inside progress bar
    height: ms(55),
    borderRadius: ms(40),
  },
  cameraIconContainer: {
    position: "absolute",
    bottom: ms(5), // Slightly above the bottom to avoid overlapping
    right: ms(5), // Slightly inside the circle
    width: ms(30),
    height: ms(30),
    borderRadius: ms(15),
    backgroundColor: config.colors.primary,
    justifyContent: "center",
    alignItems: "center",
  },
  profileName: {
    fontFamily: Fonts.RALEWAY_BOLD,
    marginTop: vs(18),
  },
  avatarCircle: {
    width: ms(107),
    height: ms(107),
    borderRadius: ms(60),
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: config.colors.gray,
  },
  loaderOverlay: {
    position: "absolute",
    width: ms(107),
    height: ms(107),
    borderRadius: ms(60),
    justifyContent: "center",
    alignItems: "center",
  },
});
