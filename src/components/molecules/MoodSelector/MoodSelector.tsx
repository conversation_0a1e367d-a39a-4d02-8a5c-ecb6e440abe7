import dayjs from "dayjs";
import React, { useEffect, useState } from "react";
import { View, TouchableOpacity, Image, ActivityIndicator } from "react-native";
import { Card, Typography } from "@/components/atoms";
import Common from "@/theme/common.style";
import { useDispatch, useSelector } from "react-redux";
import { useAnalytics, analyticsEventType } from "@/hooks/useAnalytics";
import {
  createMoodLog,
  selectMoodLoading,
  selectRecentMoodLog,
  setRecentMoodLog,
} from "@/store/slices/dashboardSlice";
import { store } from "@/store";
import { config } from "@/theme/_config";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import getMoodSelectorStyles from "./MoodSelector.styles";

interface MoodSelectorProps {
  selectedDate: string;
}

const MoodSelector: React.FC<MoodSelectorProps> = ({ selectedDate }) => {
  const { setAnalyticsEvent } = useAnalytics();
  const moodLoading = useSelector(selectMoodLoading);
  const recentMoodLog = useSelector(selectRecentMoodLog);
  const dispatch = useDispatch();

  const styles:any = useDynamicStyles(getMoodSelectorStyles)

  // 🔹 State to Track Selected Mood
  // const [selectedMood, setSelectedMood] = useState<number | null>(null);

  // 🔹 Update the Selected Mood When `recentMoodLog` Changes
  // useEffect(() => {
  //   if (recentMoodLog?.score) {
  //     setSelectedMood(recentMoodLog.score);
  //   } else {
  //     setSelectedMood(null); // Reset mood if no mood log is found
  //   }
  // }, [recentMoodLog]);

  const handleMoodSelect = (mood: number) => {
    // setSelectedMood(mood);
    dispatch(setRecentMoodLog({ score: mood })); // Reset recent mood log

    // ✅ Append the current time to the selected date
    const dateLogged = dayjs(selectedDate)
      .hour(dayjs().hour()) // Set current hour
      .minute(dayjs().minute()) // Set current minute
      .second(dayjs().second()) // Set current second
      .format("YYYY-MM-DDTHH:mm:ss.SSS");

    store
      .dispatch(
        createMoodLog({
          score: mood,
          dateLogged,
        })
      )
      .then(() => {
        setAnalyticsEvent(analyticsEventType.custom, {
          event: "dashboard_mood_logged",
          item_id: "dashboard_mood_logged",
          action: "User logs mood",
        });
      })
      .catch((error) => console.error("Failed to log mood:", error));
  };

  return (
    <Card style={styles.container}>
      <Typography.B2 style={[styles.title, Common.textBold]}>
        How are you feeling{" "}
        {selectedDate === new Date().toISOString().split("T")[0]
          ? "today"
          : "on this day"}
        ?
      </Typography.B2>

      {moodLoading ? (
        <ActivityIndicator size="large" color={config.colors.primary} />
      ) : (
        <View style={styles.moodRow}>
          <Image
            source={require("@/theme/assets/images/sad/sad.png")}
            style={styles.emoji}
          />
          {[1, 2, 3, 4, 5].map((mood) => (
            <TouchableOpacity
              key={mood}
              style={[
                styles.moodOption,
                recentMoodLog?.score && recentMoodLog?.score === mood ? styles.selectedMoodOption : null,
                // selectedMood === mood && styles.selectedMoodOption,
              ]}
              onPress={() => handleMoodSelect(mood)}
            >
              <Typography.B2 style={[styles.moodText, Common.textBold]}>
                {mood}
              </Typography.B2>
            </TouchableOpacity>
          ))}
          <Image
            source={require("@/theme/assets/images/smile/smile.png")}
            style={styles.emoji}
          />
        </View>
      )}
    </Card>
  );
};

export default MoodSelector;
