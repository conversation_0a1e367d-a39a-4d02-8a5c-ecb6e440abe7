import { ScaledSheet, ms } from "react-native-size-matters";
import { config } from "@/theme/_config";

const getMoodSelectorStyles = theme => ScaledSheet.create({
  container: {
    alignItems: "center",
    backgroundColor: theme.colors.tile_bg,
  },
  title: {
    fontSize: ms(14),
    color: theme.colors.textPrimary,
    marginBottom: ms(10),
  },
  moodRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    width: "100%",
  },
  emoji: {
    width: ms(26),
    height: ms(26),
    marginHorizontal: ms(25),
  },
  moodOption: {
    width: ms(36),
    height: ms(36),
    justifyContent: "center",
    alignItems: "center",
  },
  selectedMoodOption: {
    borderWidth: 1,
    borderColor: theme.colors.yellow,
    borderRadius: 10,
  },
  moodText: {
    color: theme.colors.textPrimary,
    fontSize: ms(16),
  },
});

export default getMoodSelectorStyles;
