/** @format */

import { Fonts } from "@/constants";
import { config, SCREEN_WIDTH, SCREEN_HEIGHT } from "@/theme/_config";
import { Theme } from "@/types/theme/theme";
import { ScaledSheet, ms } from "react-native-size-matters";

const getBottomStyles = (theme: Theme) =>
  ScaledSheet.create({
    authText: {
      textAlign: "center",
      marginLeft: "10@ms",
      letterSpacing: -0.01,
      color: config.colors.black,
      fontFamily: Fonts.RALEWAY_SEMI_BOLD,
    },
    orContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: ms(8),
      marginTop: ms(5),
    },
    orText: {
      marginHorizontal: "5@ms",
      color: theme.colors.textPrimary,
      fontSize: "17@ms",
      fontFamily: Fonts.RALEWAY_SEMI_BOLD,
    },
    divider: {
      flex: 1,
      height: 1,
      backgroundColor: theme.colors.divider,
    },

    baseView: {
      minWidth: "202@ms",
      flexDirection: "row",
      justifyContent: "flex-start",
      alignItems: "center",
      marginleft: "18@ms",
    },
    bottomSection: {
      marginBottom: "40@ms",
    },
    loginButtonText: {
      color: theme.colors.white,
      fontFamily: Fonts.RALEWAY_BOLD,
      fontSize: "17@ms",
      marginBottom: -2,
    },
    emailButton: { width: "100%", marginBottom: ms(8) },
    createAccountButton: {
      borderRadius: ms(16),
      height: ms(47),
    },
    createButtonText: {
      color: theme.colors.textPrimary,
      fontFamily: Fonts.RALEWAY_BOLD,
      fontSize: "17@ms",
      marginBottom: -2,
    },
    subText: {
      marginBottom: ms(10),
      marginTop: ms(10),
      alignSelf: "center",
      fontSize: "13@ms",
      fontFamily: Fonts.RALEWAY_BOLD,
      color: theme.colors.textPrimary,
    },
    signup: {
      alignSelf: "center",
      fontSize: "13@ms",
      fontFamily: Fonts.RALEWAY_BOLD,
      color: theme.colors.textPrimaryYellow,
    },
  });

export default getBottomStyles;
