import { Fonts } from "@/constants";
import { Theme } from "@/types/theme/theme";
import { ms, ScaledSheet } from "react-native-size-matters";

export default (theme: Theme) => ScaledSheet.create({
  tagBold: {
    fontFamily: Fonts.RALEWAY_BOLD,
    color: theme.colors.white
  },
  container: {
    height: ms(52),
    justifyContent: "center",
    alignItems: "center",
    overflow: "hidden",
    borderRadius: ms(14),
  },
  leftElements: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  leftElementsText:{
    marginHorizontal: ms(16),
    fontSize: ms(16),
  }

});