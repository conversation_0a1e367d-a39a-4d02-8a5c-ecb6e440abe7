export type MedicationFrequency = {
  id: number;
  value: string;
  label: number;
};

export type CustomSchedule = {
  frequency: MedicationFrequency;
  alertBefore: number;
  interval: number;
  isEachSelected: boolean;
  monthDates: number[];
  months: number[];
  weekDays: number[];
  weekIndex: number[];
};

export type MedicationFormPayload = {
  id: number | null;
  fromUpdate: boolean;
  medicineName: string;
  activeIngredient: string;
  categoryTypeId: number | undefined;
  quantity: number;
  strength: number;
  doses: any[];
  frequencyTypeId: number | undefined;
  intakeTime: string; // ISO date string
  iconId: number | undefined;
  fromDate: string; // ISO date string
  toDate: Date;
  alertBefore: number | null;
  customSchedule: CustomSchedule;
};
