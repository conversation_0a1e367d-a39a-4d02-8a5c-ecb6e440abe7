import { IS_IOS } from '@/theme/_config';
import { ms } from 'react-native-size-matters';
import Toast from 'react-native-toast-message';

const toastService = (() => {
  enum messageType {
    SUCCESS = 'success',
    INFO = 'info',
    ERROR = 'error',
    NOTIFY = 'notify'
  }

  const VISIBILITY_TIME: number = 5000; // 5 sec
  const TOP_OFF_SET: number = ms(48); // 48 pixel from top

  function success(
    title = 'Success',
    text2: string = '',
    visibilityTime: number = VISIBILITY_TIME,
    topOffset: number = TOP_OFF_SET
  ) {
    setTimeout(() => {
      Toast.show({
        type: messageType.SUCCESS,
        text1: title,
        autoHide: true,
        visibilityTime: visibilityTime,
        ...(IS_IOS && { topOffset }),
        ...(text2 && { text2 })
      });
    }, 200);
  }

  function fail(
    title: string = 'Something went wrong',
    text2: string = '',
    visibilityTime: number = VISIBILITY_TIME,
    topOffset: number = TOP_OFF_SET
  ) {
    setTimeout(() => {
      Toast.show({
        type: messageType.ERROR,
        text1: title,
        autoHide: true,
        visibilityTime: visibilityTime,
        ...(IS_IOS && { topOffset }),
        ...(text2 && { text2 })
      });
    }, 200);
  }

  function info(
    title = 'Something is missing',
    text2: string = '',
    visibilityTime: number = VISIBILITY_TIME,
    topOffset: number = TOP_OFF_SET
  ) {
    setTimeout(() => {
      Toast.show({
        type: messageType.INFO,
        text1: title,
        autoHide: true,
        visibilityTime: visibilityTime,
        ...(IS_IOS && { topOffset }),
        ...(text2 && { text2 })
      });
    }, 200);
  }

  function notify(
    title = 'Notification Title',
    text2: string = '',
    additionalData: any,
    visibilityTime: number = VISIBILITY_TIME,
    topOffset: number = TOP_OFF_SET
  ) {
    setTimeout(() => {
      Toast.show({
        type: messageType.NOTIFY,
        text1: title,
        props: additionalData,
        autoHide: true,
        visibilityTime: visibilityTime,
        ...(IS_IOS && { topOffset }),
        ...(text2 && { text2 })
      });
    }, 200);
  }

  return { success, fail, info, messageType, notify };
})();

export default toastService;
