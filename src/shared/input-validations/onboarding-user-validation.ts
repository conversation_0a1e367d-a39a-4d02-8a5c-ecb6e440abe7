import * as Yup from "yup";

const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
const phoneRegex =
  /^(?:\+?\d{1,3}[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})$/;

export const ONBOARDING_USER_VALIDATION_SCHEMA = Yup.object({
  name: Yup.string()
    .trim()
    .required("Name is required.")
    .min(2, "Name should be at least 2 characters.")
    .max(50, "Name can be up to 50 characters."),

  email: Yup.string()
    .trim()
    .required("Email is required.")
    .matches(emailRegex, "Invalid email format."),

  phoneNumber: Yup.string()
    .trim()
    .nullable()
    .notRequired()
    .test(
      "isValidPhone",
      "Invalid phone number format. Please enter a valid phone number.",
      (value) => {
        if (!value) return true; // If empty, validation passes
        return phoneRegex.test(value); // Validate if a number is entered
      }
    ),

  pheAllowance: Yup.string().when("$consumptionType", {
    is: "Phe",
    then: (schema) =>
      schema
        .required("Please specify Phe allowance.")
        .min(1, "Please specify Phe allowance.") // catches empty string
        .test(
          "not-zero",
          "Phe must be greater than 0.",
          (value) => value !== "0"
        ),
    otherwise: (schema) =>
      schema
        .required("Please specify Protein allowance.")
        .min(1, "Please specify Protein allowance.")
        .test(
        "not-zero",
        "Protein must be greater than 0.",
        (value) => value !== "0"
      ),
  }),

  agreedToTerms: Yup.boolean()
    .oneOf([true], "You must agree to the terms and conditions.")
    .required("Agreement to terms is required."),

  dataUsageConsent: Yup.boolean().notRequired(),
});


export const NEW_ONBOARDING_USER_VALIDATION_SCHEMA = Yup.object({
  name: Yup.string()
    .trim()
    .required("Name is required.")
    .min(2, "Name should be at least 2 characters.")
    .max(50, "Name can be up to 50 characters."),

  email: Yup.string()
    .trim()
    .required("Email is required.")
    .matches(emailRegex, "Invalid email format."),

  phoneNumber: Yup.string()
    .trim()
    .nullable()
    .notRequired()
    .test(
      "isValidPhone",
      "Invalid phone number format. Please enter a valid phone number.",
      (value) => {
        if (!value) return true;
        return phoneRegex.test(value); 
      }
    ),
});


export const NEW_ONBOARDING_CREATE_ACCOUNT_SCHEMA  = Yup.object({
pheAllowance: Yup.number()
  .typeError("Please enter a valid number.") 
  .when("$consumptionType", {
    is: "Phe",
    then: (schema) =>
      schema
        .required("Please specify Phe allowance.")
        .moreThan(0, "Phe must be greater than 0."),
    otherwise: (schema) =>
      schema
        .required("Please specify Protein allowance.")
        .moreThan(0, "Protein must be greater than 0."),
  }),

  agreedToTerms: Yup.boolean()
    .oneOf([true], "You must agree to the terms and conditions.")
    .required("Agreement to terms is required."),

  dataUsageConsent: Yup.boolean().notRequired(),

});
 