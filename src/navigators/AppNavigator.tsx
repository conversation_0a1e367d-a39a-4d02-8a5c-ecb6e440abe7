/** @format */

import React, { useEffect, useState } from "react";
import { createStackNavigator } from "@react-navigation/stack";
import { NavigationContainer } from "@react-navigation/native";
import { SafeAreaProvider } from "react-native-safe-area-context";
import { navigationRef } from "@/utils/Navigation";

import type { RootStackParamList } from "@/types/navigation";

import { useTheme } from "@/theme";
import { Screens } from "@/constants";
import { Stacks } from "@/constants/screens";
import DrawerNavigator from "./DrawerNavigator"; // DrawerNavigator includes MainTabsNavigator
import { selectAuth } from "@/store/slices/authSlice";
import OnboardingStack from "./stacks/OnboardingStack";
import AuthStack from "./stacks/AuthStack";
import { useAppDispatch, useAppSelector } from "@/store";
import { useRemoteConfig } from "@/context/RemoteConfigContext";
import UserPreferences from "@/screens/Onboarding/Onboarding.UserPreference";
import {
  completeOnboarding,
  selectOnboarding,
  selectReOnBoarded,
} from "@/store/slices/onboardingSlice";
import { selectUserLoading } from "@/store/slices/userSlice";
import Loading from "@/components/atoms/Loading/Loading";
import { useAnalytics } from "@/hooks/useAnalytics";

const Stack = createStackNavigator<RootStackParamList>();

const AppNavigator = () => {
  const dispatch = useAppDispatch();
  const { logScreenView } = useAnalytics();
  const { navigationTheme } = useTheme();
  const { remoteConfig } = useRemoteConfig();
  const { isLoggedIn } = useAppSelector(selectAuth);
  const { completed: hasOnboarded, isPreferenceSelected } =
    useAppSelector(selectOnboarding);
  const  reOnboarded = useAppSelector(selectReOnBoarded);
  const userOnboarded = useAppSelector((state) => state.user.user?.onBoarded);
  const user = useAppSelector((state) => state.user.user);
  const isUserLoading = useAppSelector(selectUserLoading);
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);
  // Remove timezone hook from AppNavigator since we only want modal in HomeScreen
  const routeNameRef = React.useRef<string | undefined>();
  useEffect(() => {
    // When user profile loads and userOnboarded is true, mark onboarding as completed
    if (userOnboarded && !hasOnboarded) {
      dispatch(completeOnboarding());
    }

    // Mark initial load as complete once we have the user data
    if (!isUserLoading && userOnboarded !== undefined) {
      setInitialLoadComplete(true);
    }
  }, [userOnboarded, hasOnboarded, isUserLoading, dispatch, user]);

  const options = { headerShown: false, gestureEnabled: false };

  if (isPreferenceSelected) {
    // Loading screen while waiting for the API to return user data
    if (isLoggedIn && isUserLoading && !initialLoadComplete) {
      return <Loading />;
    }
  }

  const onReady = () => {
    try {
      routeNameRef.current =
        navigationRef.current?.getCurrentRoute?.()?.name ?? "";
    } catch (ex) {
      console.log(ex);
    }
  };

  const onStateChange = async () => {
    try {
      const previousRouteName = routeNameRef.current;
      const currentRouteName = navigationRef.current?.getCurrentRoute?.()?.name;
      if (previousRouteName !== currentRouteName) {
        await logScreenView(currentRouteName);
      }
      routeNameRef.current = currentRouteName;
    } catch (ex) {
      console.log(ex);
    }
  };
  if (isLoggedIn && isUserLoading && !initialLoadComplete) {
    return <Loading />;
  }


  return (
    <SafeAreaProvider>
      <NavigationContainer
        theme={navigationTheme}
        ref={navigationRef}
        onReady={onReady}
        onStateChange={onStateChange}
      >
        <Stack.Navigator screenOptions={{ headerShown: false }}>
          {!isLoggedIn ? (
            <Stack.Screen name={Stacks.Auth} component={AuthStack} />
          ) : reOnboarded ? (
            <Stack.Screen
            name={Stacks.Auth}
              component={AuthStack}
            />
          ) : // Direct to UserPreferences if user is onboarded from API but preferences not selected
          userOnboarded && !isPreferenceSelected ? (
            <Stack.Screen
              name="UserPreferences"
              component={UserPreferences}
              options={options}
            />
          ) : !hasOnboarded ? (
            <Stack.Screen
              name={Stacks.Onboarding}
              component={OnboardingStack}
            />
          ) : (
            <Stack.Screen name={Screens.App} component={DrawerNavigator} />
          )}
        </Stack.Navigator>
      </NavigationContainer>
    </SafeAreaProvider>
  );
};

export default AppNavigator;
