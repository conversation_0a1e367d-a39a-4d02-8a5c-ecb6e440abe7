import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { getIconComponent, TabNames } from "@/constants/screens";
import LabsStacks from "./stacks/LabStacks";
import TaskManagerScreen from "@/screens/App/TaskManager/TaskManagerScreen";
import { useTheme } from "@/theme";
import HomeStack from "./stacks/HomeStack";
import DietStack from "./stacks/DietStack";
import CustomTabBar from "@/components/template/CustomTabBarContent/CustomTabBar";

const Tab = createBottomTabNavigator();

const isTabName = (name: string): name is TabNames => {
  return ["Home", "Labs", "Tasks", "Diet"].includes(name);
};

const MainTabsNavigator = () => {
  const { colors } = useTheme();

  return (
    <Tab.Navigator
      tabBar={(props) => <CustomTabBar {...props} />}
      screenOptions={({ route }) => {
        return {
          headerShown: false,
          unmountOnBlur: true,
          tabBarIcon: ({ color }) => {
            if (!isTabName(route.name)) return null;
            const TabIcon = getIconComponent(route.name);
            return <TabIcon color={color} />;
          },
        };
      }}
    >
      <Tab.Screen
        name="Home"
        component={HomeStack}
        listeners={({ navigation }) => ({
          tabPress: () => {
            navigation.navigate("Home", {
              screen: "HomeScreen",
            });
          },
        })}
      />
      <Tab.Screen name="Tasks" component={TaskManagerScreen} />
      <Tab.Screen name="Labs" component={LabsStacks} />
      <Tab.Screen
        name="Diet"
        listeners={({ navigation }) => ({
          tabPress: () => {
            navigation.navigate("Diet", {
              screen: "DietScreen",
            });
          },
        })}
        component={DietStack}
      />
    </Tab.Navigator>
  );
};

export default MainTabsNavigator;
