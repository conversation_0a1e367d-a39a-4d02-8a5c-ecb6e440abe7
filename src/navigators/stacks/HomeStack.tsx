// HomeStack.tsx
import React from "react";
import { createStackNavigator } from "@react-navigation/stack";
import HomeScreen from "@/screens/App/Home/HomeScreen";
import PatientContactsStack from "./PatientContactsStack"; // 👈 nested stack

const Stack = createStackNavigator();

const HomeStack = () => (
  <Stack.Navigator screenOptions={{ headerShown: false }}>
    <Stack.Screen name="HomeScreen" component={HomeScreen} />
    <Stack.Screen
      name="PatientContactsStack"
      component={PatientContactsStack}
    />
  </Stack.Navigator>
);

export default HomeStack;
