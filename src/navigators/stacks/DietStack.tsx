// ProfileStacks.tsx

import React from "react";
import { TransitionPresets } from "@react-navigation/stack";
import { createStackNavigator } from "@react-navigation/stack";

import {
  AISuggestions,
  DietTracker,
  LogFood,
  LogFoodMain,
} from "@/screens";

export type DietStackParamList = {
  DietScreen: undefined;
  AISuggestions: undefined;
  LogFood: undefined;
  LogFoodMain: undefined;
};

const Stack = createStackNavigator<DietStackParamList>();

const DietStack = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animationEnabled: true,
        ...TransitionPresets.SlideFromRightIOS,
      }}
    >
      <Stack.Screen
        name="DietScreen"
        component={DietTracker}
        options={{ headerShown: false }}
      />
      <Stack.Screen name="AISuggestions" component={AISuggestions} />
      <Stack.Screen name="LogFood" component={LogFood} />
      <Stack.Screen name="LogFoodMain" component={LogFoodMain} />
    </Stack.Navigator>
  );
};

export default DietStack;
