// ProfileStacks.tsx

import React from "react";
import { createStackNavigator } from "@react-navigation/stack";

import { Labs } from "@/screens";

const Stack = createStackNavigator();

const LabsStacks = () => {
  return (
    <Stack.Navigator>
      <Stack.Screen
        name="LabsScreen"
        component={Labs}
        options={{ headerShown: false }}
      />
    </Stack.Navigator>
  );
};

export default LabsStacks;
