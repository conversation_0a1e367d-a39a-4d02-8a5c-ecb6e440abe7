import { createStackNavigator } from "@react-navigation/stack";

import LogFoodGuide from "@/screens/Onboarding/Onboarding.LogFoodGuide";
import DashboardGuide from "@/screens/Onboarding/Onboarding.DashboardGuide";
import PheResultGuide from "@/screens/Onboarding/Onboarding.PheResultGuide";
import MedicationGuide from "@/screens/Onboarding/Onboarding.MedicationdGuide";

const Stack = createStackNavigator();

const TutorialsStack = () => {
  const options = { headerShown: false, gestureEnabled: false }; 
  return (
    <Stack.Navigator>
      <Stack.Screen name="DashboardGuide" component={DashboardGuide} options={options} />
      <Stack.Screen name="MedicationGuide" component={MedicationGuide} options={options} />
      <Stack.Screen name="PheResultGuide" component={PheResultGuide} options={options} />
      <Stack.Screen name="LogFoodGuide" component={LogFoodGuide} options={options} />
    </Stack.Navigator>
  );
};

export default TutorialsStack;