import React from "react";
import { createStackNavigator } from "@react-navigation/stack";

import { PheAllowance, Profile } from "@/screens";

const Stack = createStackNavigator();

const ProfileStacks = () => {
  return (
    <Stack.Navigator>
      <Stack.Screen
        name="ProfileScreen"
        component={Profile}
        options={{ headerShown: false }}
      />

      <Stack.Screen
        name="PheAllowance"
        component={PheAllowance}
        options={{ headerShown: false }}
      />
    </Stack.Navigator>
  );
};

export default ProfileStacks;
