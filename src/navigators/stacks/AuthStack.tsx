/** @format */

import React, { useEffect } from "react";
import { BackHandler } from "react-native";
import { createStackNavigator } from "@react-navigation/stack";
import { useNavigation } from "@react-navigation/native";
import LoginScreen from "@/screens/Auth/Login/LoginScreen";
import InitialScreen from "@/screens/Auth/Initial/InitialScreen";
import { AuthStackParamList } from "@/types/navigation";
import LoadingScreen from "@/screens/Onboarding/Onboarding.Loading";
import WelcomeScreen from "@/screens/Onboarding/Onboarding.WelcomeScreen";
import IntentionOptions from "@/screens/Onboarding/Onboarding.IntentionOptions";
import CreateAccount from "@/screens/Onboarding/Onboarding.CreateAccount";
import DisclaimerScreen from "@/screens/Onboarding/Onboarding.DisclaimerScreen";
import RoleOptions from "@/screens/Onboarding/Onboarding.RoleOptions";
import { selectReOnBoarded } from "@/store/slices/onboardingSlice";
import { useAppSelector } from "@/store";

const Stack = createStackNavigator<AuthStackParamList>();

const AuthStack = () => {
  useEffect(() => {
    const onBackPress = () => true; // Block back button
    BackHandler.addEventListener("hardwareBackPress", onBackPress);

    return () =>
      BackHandler.removeEventListener("hardwareBackPress", onBackPress);
  }, []);

  const options = { headerShown: false, gestureEnabled: true };
  const reOnboarded = useAppSelector(selectReOnBoarded);
  return (
    <Stack.Navigator
      initialRouteName={reOnboarded ? "DisclaimerScreen" : "InitialScreen"}
    >
     
      <Stack.Screen
        name="LoginScreen"
        component={LoginScreen}
        options={options}
      />
      <Stack.Screen
        name="LoadingScreen"
        component={LoadingScreen}
        options={options}
      />
      <Stack.Screen
        name="WelcomeScreen"
        component={WelcomeScreen}
        options={options}
      />
      <Stack.Screen
        name="DisclaimerScreen"
        component={DisclaimerScreen}
        options={options}
      />
      <Stack.Screen
        name="IntentionOptions"
        component={IntentionOptions}
        options={options}
      />
      <Stack.Screen
        name="RoleOptions"
        component={RoleOptions}
        options={options}
      />
      <Stack.Screen
        name="CreateAccount"
        component={CreateAccount}
        options={options}
      />
       <Stack.Screen
        name="InitialScreen"
        component={InitialScreen}
        options={options}
      />
    </Stack.Navigator>
  );
};

export default AuthStack;
