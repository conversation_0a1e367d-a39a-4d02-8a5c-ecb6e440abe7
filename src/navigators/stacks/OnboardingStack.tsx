import React, { useEffect } from "react";
import { BackHand<PERSON> } from "react-native";
import { createStackNavigator } from "@react-navigation/stack";

import WelcomeScreen from "@/screens/Onboarding/Onboarding.WelcomeScreen";
import UserConfiguration from "@/screens/Onboarding/Onboarding.UserConfiguration";
import UserPreferences from "@/screens/Onboarding/Onboarding.UserPreference";
import DashboardGuide from "@/screens/Onboarding/Onboarding.DashboardGuide";
import MedicationGuide from "@/screens/Onboarding/Onboarding.MedicationdGuide";
import PheResultGuide from "@/screens/Onboarding/Onboarding.PheResultGuide";
import LogFoodGuide from "@/screens/Onboarding/Onboarding.LogFoodGuide";
import LoadingScreen from "@/screens/Onboarding/Onboarding.Loading";
import DisclaimerScreen from "@/screens/Onboarding/Onboarding.DisclaimerScreen";
import IntentionOptions from "@/screens/Onboarding/Onboarding.IntentionOptions";
import RoleOptions from "@/screens/Onboarding/Onboarding.RoleOptions";
import CreateAccount from "@/screens/Onboarding/Onboarding.CreateAccount";
import UserProfile from "@/screens/Onboarding/Onboarding.UserProfile";
import { useAppSelector } from "@/store";

const Stack = createStackNavigator();

const OnboardingStack = () => {
  useEffect(() => {
    const onBackPress = () => true; // Block back button
    BackHandler.addEventListener("hardwareBackPress", onBackPress);

    return () =>
      BackHandler.removeEventListener("hardwareBackPress", onBackPress);
  }, []);
  
  const options = { headerShown: false, gestureEnabled: false };
  return (
    <Stack.Navigator initialRouteName="LoadingScreen">
      <Stack.Screen
        name="LoadingScreen"
        component={LoadingScreen}
        options={options}
      />
      <Stack.Screen
        name="UserConfiguration"
        component={UserConfiguration}
        options={options}
      />
      <Stack.Screen
        name="UserPreferences"
        component={UserPreferences}
        options={options}
      />
      <Stack.Screen
        name="DashboardGuide"
        component={DashboardGuide}
        options={options}
      />
      <Stack.Screen
        name="MedicationGuide"
        component={MedicationGuide}
        options={options}
      />
      <Stack.Screen
        name="PheResultGuide"
        component={PheResultGuide}
        options={options}
      />
      <Stack.Screen
        name="LogFoodGuide"
        component={LogFoodGuide}
        options={options}
      />
      <Stack.Screen
        name="UserProfile"
        component={UserProfile}
        options={options}
      />
    </Stack.Navigator>
  );
};

export default OnboardingStack;
