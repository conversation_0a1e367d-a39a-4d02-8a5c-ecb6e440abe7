// PatientContactsStack.tsx
import React from "react";
import { createStackNavigator } from "@react-navigation/stack";
import { PatientContacts } from "@/screens";

const Stack = createStackNavigator();

const PatientContactsStack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="PatientContactsScreen" component={PatientContacts} />
    </Stack.Navigator>
  );
};

export default PatientContactsStack;
