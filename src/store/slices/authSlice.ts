// /store/slices/authSlice.ts

import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "..";
import { User } from "@/types/schemas/user";
import { setIsUserAlreadyLoggedIn } from "./notificationSlice";

interface AuthState {
  isLoggedIn: boolean;
  user: User | null;
  idToken: string | null;
}

const initialState: AuthState = {
  isLoggedIn: false,
  user: null,
  idToken: null,
};

export const logout = createAsyncThunk(
  'auth/logout',
  async (_, { dispatch }) => {
    dispatch(authSlice.actions.logoutUser());
    dispatch(setIsUserAlreadyLoggedIn(false));
  }
);

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    directAuthenticate(state) {
      state.isLoggedIn = true;
    },
    login(state, action) {
      // Handle login logic
      state.isLoggedIn = true;
      state.user = action.payload;
    },

    resetPassword(state, action: PayloadAction<{ email: string }>) {
      // Handle password reset logic
    },
    logoutUser(state) {
      state.isLoggedIn = false;
      state.user = null;
      state.idToken = null;
    },
    setIdToken(state, action: PayloadAction<string>) {
      state.idToken = action.payload;
    },
    resetAuthState: () => {
      return initialState;
    }
  },
});

export const {
  login,
  resetPassword,
  resetAuthState,
  directAuthenticate,
  setIdToken,
} = authSlice.actions;
export const selectAuth = (state: RootState) => state.auth;

export default authSlice.reducer;
