import DeviceInfo from "react-native-device-info";
import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";

import { checkAndStoreCurrentVersion } from "@/utils/versionStorage";
import { fetchUserById, onlyUpdateTimeZone } from "@/store/slices/userSlice";
import { getFeatureUpdate, getTimeZones, TimeZone, updateUserTimeZone, updateTimeZoneToggle, updateDeviceTimeZone } from "@/services/api/userAPI";

// Define state
interface SettingsState {
  showWhatsNewModal: boolean;
  consumptionUnit: "Phe" | "Protein";
  isSimplifiedDiet: boolean;
  featureUpdate?: {
    version: string;
    description: string;
    pictureUrl: string;
    title: string;
  } | null;
  currentAppVersion: string; // Current installed app version (e.g., 1.2.3)
  lastAcknowledgedVersion: string; // Last version user pressed "Got it"
  // Add for time zone modal
  showTimeZoneModal: boolean;
  profileTimeZone?: string | null;
  timeZones: TimeZone[];
  loading: boolean; // <-- add this for time zone fetch
  error: string | null; // <-- add this for time zone fetch
  lastLoginMethod: string | null;
  profileTzToggle: boolean; // <-- add this for time zone fetch
}

const deviceTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

const initialState: SettingsState = {
  showWhatsNewModal: false,
  consumptionUnit: "Phe",
  isSimplifiedDiet: false,
  featureUpdate: null,
  currentAppVersion: "0",
  lastAcknowledgedVersion: "0",
  // Add for time zone modal
  showTimeZoneModal: false,
  profileTimeZone: null,
  timeZones: [], // <-- add this
  loading: false, // <-- add this for time zone fetch
  error: null, // <-- add this for time zone fetch
  lastLoginMethod: null,
  profileTzToggle: false // Toggle responsible for change the user tz preference on Profile 
};

export const fetchFeatureUpdate = createAsyncThunk(
  "settings/fetchFeatureUpdate",
  async (_, { dispatch, getState, rejectWithValue }) => {
    try {
      dispatch(setShowWhatsNewModal(false));
      const isNew = checkAndStoreCurrentVersion();
      if (isNew) {
        const appVersionCode = DeviceInfo.getVersion();
        const response = await getFeatureUpdate(appVersionCode);
        if (response && response.version) {
          dispatch(setFeatureUpdate(response));
          dispatch(setShowWhatsNewModal(true));
        } else {
          dispatch(setFeatureUpdate(null));
          dispatch(setShowWhatsNewModal(false));
        }
        return response;
      } else dispatch(setFeatureUpdate(null));
    } catch (error) {
      dispatch(setFeatureUpdate(null));
      dispatch(setShowWhatsNewModal(false));
      return rejectWithValue(error);
    }
  }
);

export const fetchTimeZonesThunk = createAsyncThunk<TimeZone[], { offset?: number } | void>(
  "settings/fetchTimeZones",
  async (params, { rejectWithValue }) => {
    try {
      const offset = params?.offset;
      const timeZones = await getTimeZones(offset);
      return timeZones;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

// Thunk for updating user timezone
export const updateUserTimeZoneThunk = createAsyncThunk<
  string, // returns the new timezoneId string
  { timezoneId: string; restrictFetchingUser?: boolean },
  { rejectValue: string }
>("settings/updateUserTimeZone", async ({ timezoneId, restrictFetchingUser }, { rejectWithValue, dispatch }) => {
  try {
    await updateUserTimeZone({ timezoneId });
    // Fetch user data after successful timezone update
    if (!restrictFetchingUser) {
      await dispatch(fetchUserById());
    } else {
      dispatch(onlyUpdateTimeZone(timezoneId));
      dispatch(setProfileTimeZone(Intl.DateTimeFormat().resolvedOptions().timeZone));
    }

    return timezoneId;
  } catch (error: any) {
    return rejectWithValue(error?.message || "Failed to update user timezone");
  }
});

// Thunk for initializing timezone based on device vs profile
export const initializeTimezoneThunk = createAsyncThunk<void, void, { rejectValue: string }>(
  "settings/initializeTimezone",
  async (_, { rejectWithValue, dispatch, getState }) => {
    try {
      const deviceTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const state = getState() as any;
      const userTimeZone = state.user?.user?.timeZoneId;

      // Check if user timezone is empty or UTC
      const isEmptyOrUTC = !userTimeZone || userTimeZone === "UTC" || userTimeZone.trim() === "";

      if (isEmptyOrUTC) {
        // Update user timezone to device timezone
        await dispatch(updateUserTimeZoneThunk({ timezoneId: deviceTimeZone, restrictFetchingUser: true })).unwrap();
        dispatch(setProfileTimeZone(deviceTimeZone));
      }
    } catch (error: any) {
      return rejectWithValue(error?.message || "Failed to initialize timezone");
    }
  }
);

// Thunk for updating user timezone toggle preference
export const updateTimeZoneToggleThunk = createAsyncThunk<
  boolean,
  boolean,
  { rejectValue: string }
>("settings/updateTimeZoneToggle", async (timeZoneToggle, { rejectWithValue }) => {
  try {
    await updateTimeZoneToggle({ timeZoneToggle });
    return timeZoneToggle;
  } catch (error: any) {
    return rejectWithValue(error?.message || "Failed to update timezone toggle preference");
  }
});

// Thunk for updating device timezone
export const updateDeviceTimeZoneThunk = createAsyncThunk<
  string,
  void,
  { rejectValue: string }
>("settings/updateDeviceTimeZone", async (_, { rejectWithValue }) => {
  try {
    const deviceTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    await updateDeviceTimeZone({ deviceTimeZone });
    return deviceTimeZone;
  } catch (error: any) {
    return rejectWithValue(error?.message || "Failed to update device timezone");
  }
});

const settingsSlice = createSlice({
  name: "settings",
  initialState,
  reducers: {
    setShowWhatsNewModal(state, action: PayloadAction<boolean>) {
      state.showWhatsNewModal = action.payload;
    },
    setConsumptionUnit(state, action: PayloadAction<"Phe" | "Protein">) {
      state.consumptionUnit = action.payload;
    },
    setIsSimplifiedDiet(state, action: PayloadAction<boolean>) {
      state.isSimplifiedDiet = action.payload;
    },
    resetSettingsState: () => initialState,
    setCurrentAppVersion(state, action: PayloadAction<string>) {
      state.currentAppVersion = action.payload;
    },
    setLastAcknowledgedVersion(state, action: PayloadAction<string>) {
      state.lastAcknowledgedVersion = action.payload;
    },
    setLastLoginMethod(state, action: PayloadAction<string | null>) {
      state.lastLoginMethod = action.payload;
    },

    setFeatureUpdate(
      state,
      action: PayloadAction<{
        version: string;
        description: string;
        pictureUrl: string;
        title: string;
      } | null>
    ) {
      state.featureUpdate = action.payload;
    },
    setShowTimeZoneModal(state, action: PayloadAction<boolean>) {
      state.showTimeZoneModal = action.payload;
    },
    setProfileTimeZone(state, action: PayloadAction<string>) {
      state.profileTimeZone = action.payload;
    },
    hideTimeZoneModal(state) {
      state.showTimeZoneModal = false;
    },
    setProfileTzToggle(state, action: PayloadAction<boolean>) {
      state.profileTzToggle = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder.addCase(fetchFeatureUpdate.fulfilled, (state, action) => {
      if (action.payload) {
        state.featureUpdate = action.payload;
      }
    });
    builder
      .addCase(fetchTimeZonesThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTimeZonesThunk.fulfilled, (state, action: PayloadAction<TimeZone[]>) => {
        state.loading = false;
        state.timeZones = action.payload;
      })
      .addCase(fetchTimeZonesThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message ?? "Failed to fetch time zones";
      })
      // Add timezone update thunk handlers
      .addCase(updateUserTimeZoneThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateUserTimeZoneThunk.fulfilled, (state, action: PayloadAction<string>) => {
        state.loading = false;
        state.showTimeZoneModal = false;
      })
      .addCase(updateUserTimeZoneThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || action.error.message || "Failed to update user timezone";
      })
      // Add timezone initialization thunk handlers
      .addCase(initializeTimezoneThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(initializeTimezoneThunk.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(initializeTimezoneThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || action.error.message || "Failed to initialize timezone";
      })
      // Add timezone toggle thunk handlers
      .addCase(updateTimeZoneToggleThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateTimeZoneToggleThunk.fulfilled, (state, action: PayloadAction<boolean>) => {
        state.loading = false;
        state.profileTzToggle = action.payload;
      })
      .addCase(updateTimeZoneToggleThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || action.error.message || "Failed to update timezone toggle preference";
      })
      // Add device timezone update thunk handlers
      .addCase(updateDeviceTimeZoneThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateDeviceTimeZoneThunk.fulfilled, (state, action: PayloadAction<string>) => {
        state.loading = false;
      })
      .addCase(updateDeviceTimeZoneThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || action.error.message || "Failed to update device timezone";
      });
  },
});

// Export actions
export const {
  setShowWhatsNewModal,
  resetSettingsState,
  setConsumptionUnit,
  setIsSimplifiedDiet,
  setCurrentAppVersion,
  setLastAcknowledgedVersion,
  setFeatureUpdate,
  setShowTimeZoneModal,
  setProfileTimeZone,
  hideTimeZoneModal,
  setLastLoginMethod,
  setProfileTzToggle
} = settingsSlice.actions;

// Selectors
export const selectShowTimeZoneModal = (state: { settings: SettingsState }) => state.settings.showTimeZoneModal;
export const selectProfileTimeZone = (state: { settings: SettingsState }) => state.settings.profileTimeZone;
export const selectShowWhatsNewModal = (state: { settings: SettingsState }) => state.settings.showWhatsNewModal;
export const selectFeatureUpdate = (state: { settings: SettingsState }) => state.settings.featureUpdate;
export const selectConsumptionUnit = (state: { settings: SettingsState }) => state.settings.consumptionUnit;
export const selectIsSimplifiedDiet = (state: { settings: SettingsState }) => state.settings.isSimplifiedDiet;
export const selectLastLoginMethod = (state: { settings: SettingsState }) => state.settings.lastLoginMethod;

export default settingsSlice.reducer;
