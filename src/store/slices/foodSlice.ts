import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "../index";
import { getFoodDetails } from "@/services/api/foodAPI";

interface FoodDetailsState {
  data: any | null;
  loading: boolean;
  error: string | null;
}

const initialState: FoodDetailsState = {
  data: null,
  loading: false,
  error: null,
};

export const fetchFoodDetails = createAsyncThunk(
  "food/fetchFoodDetails",
  async (_, { rejectWithValue }) => {
    try {
      const data = await getFoodDetails();
      return data;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : String(error)
      );
    }
  }
);

const foodSlice = createSlice({
  name: "food",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchFoodDetails.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        fetchFoodDetails.fulfilled,
        (state, action: PayloadAction<any>) => {
          state.loading = false;
          state.data = action.payload;
        }
      )
      .addCase(fetchFoodDetails.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message ?? "Failed to fetch food details";
      });
  },
});

export const selectFoodDetails = (state: RootState) => state.food.data;
export const selectFoodLoading = (state: RootState) => state.food.loading;
export const selectFoodError = (state: RootState) => state.food.error;

export default foodSlice.reducer;
