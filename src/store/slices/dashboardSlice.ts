import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { RootState } from "../index";
import {
  createMoodLog as createMoodLogAP<PERSON>,
  getRecentMoodLog as getRecentMoodLogAPI,
  getDailyTasks as getDailyTasksAPI,
  updateTaskCompleteStatus as updateTaskCompleteStatusAPI,
  getWeeklyProgress as getWeeklyProgressAPI,
  getMonthlyProgress as getMonthlyProgressAPI,
} from "@/services/api/dashboardAPI";
import dayjs from "dayjs";
import { Task } from "@/types/components/molecules/TaskDashboardCard";

interface MoodLog {
  id?: number;
  score: number;
  dateLogged: string;
}

interface Progress {
  date: string;
  percentage: number;
}

interface TaskIdEntry {
  patientRoutineId: number;
  taskId: number;
  actualDate: string;
}

interface DashboardState {
  moodLogs: MoodLog[];
  recentMoodLog: MoodLog | null;
  dailyTasks: Task[];
  weeklyProgressPercentages: Progress[]; // Weekly progress
  monthlyProgressPercentages: Progress[]; // Monthly progress
  todayProgress: Progress | null; // Today's progress
  loading: boolean;
  taskLoading: boolean;
  moodLoading: boolean;
  error: string | null;
  taskIdCollection: TaskIdEntry[]; // Array of objects { patientRoutineId, taskId }
}

const initialState: DashboardState = {
  moodLogs: [],
  recentMoodLog: null,
  dailyTasks: [],
  weeklyProgressPercentages: [],
  monthlyProgressPercentages: [], // Initialize as empty array
  todayProgress: null, // Initialize today's progress as null
  loading: false,
  taskLoading: false,
  moodLoading: false,
  error: null,
  taskIdCollection: [], // Initialize as an empty array
};

// Async thunk for creating a mood log
export const createMoodLog = createAsyncThunk<
  MoodLog,
  MoodLog,
  { rejectValue: string }
>("mood/createMoodLog", async (payload, { rejectWithValue }) => {
  try {
    const response = await createMoodLogAPI(payload);
    if (!response || typeof response.score === "undefined") {
      throw new Error("Invalid response format");
    }
    return response;
  } catch (error: any) {
    return rejectWithValue(
      error.response?.data?.message || "Failed to create mood log."
    );
  }
});

// Async thunk for getting the most recent mood log
export const fetchRecentMoodLog = createAsyncThunk<
  MoodLog,
  string, // Passing dateLogged as a parameter
  { rejectValue: string }
>("mood/fetchRecentMoodLog", async (dateLogged, { rejectWithValue }) => {
  try {
    const response = await getRecentMoodLogAPI(dateLogged);
    if (!response || typeof response.score === "undefined") {
      throw new Error("Invalid response format");
    }
    return response;
  } catch (error: any) {
    return rejectWithValue(
      error.response?.data?.message || "Failed to fetch recent mood log."
    );
  }
});

// Async thunk for fetching daily tasks
export const fetchDailyTasks = createAsyncThunk<
  Task[],
  string,
  { rejectValue: string }
>("dashboard/fetchDailyTasks", async (curentDate, { rejectWithValue }) => {
  try {
    const response = await getDailyTasksAPI(curentDate);
    return response;
  } catch (error: any) {
    return rejectWithValue(
      error.response?.data?.message || "Failed to fetch daily tasks."
    );
  }
});

export const updateTaskStatus = createAsyncThunk<
  {
    taskId: number;
    patientRoutineId: number;
    actualDate: string;
    dailyTasks: any[];
  }, // Return type
  {
    taskId: number | null;
    patientRoutineId: number;
    actualDate: string;
    isCompleted: boolean;
  },
  { rejectValue: string; state: RootState }
>(
  "dashboard/updateTaskStatus",
  async (payload, { getState, rejectWithValue }) => {
    const { taskId, patientRoutineId, actualDate, isCompleted } = payload;

    try {
      const state = getState();
      const taskIdCollection = state.dashboard.taskIdCollection;

      let effectiveTaskId = taskId || 0; // Default to 0 if taskId is null
      const existingEntry = taskIdCollection.find(
        (entry) =>
          entry.patientRoutineId === patientRoutineId &&
          entry.actualDate === actualDate
      );

      if (existingEntry) {
        effectiveTaskId = existingEntry.taskId; // Use saved taskId if found
      }

      // Call API to update task status
      const newTaskId: number = await updateTaskCompleteStatusAPI({
        taskId: effectiveTaskId,
        patientRoutineId,
        actualDate,
        isCompleted,
      });

      if (!newTaskId) {
        throw new Error("API did not return a valid taskId.");
      }

      // Fetch the latest daily tasks after updating status
      const selectedDate = dayjs(actualDate).format("YYYY-MM-DDT00:00:00.000");
      const updatedDailyTasks = await getDailyTasksAPI(selectedDate);

      // Return both the updated task and the latest daily tasks
      return {
        taskId: newTaskId,
        patientRoutineId,
        actualDate,
        dailyTasks: updatedDailyTasks,
      };
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message ||
          "Failed to update task completion status."
      );
    }
  }
);

// Async thunk for fetching weekly progress percentages
export const fetchWeeklyProgress = createAsyncThunk<
  Progress[], // Response type
  { fromDate: string; toDate: string }, // Payload type
  { rejectValue: string } // Error type
>("dashboard/fetchWeeklyProgress", async (dates, { rejectWithValue }) => {
  try {
    const response = await getWeeklyProgressAPI(dates.fromDate, dates.toDate);
    if (!response || !Array.isArray(response)) {
      throw new Error("Invalid response format");
    }
    return response; // Array of Progress { date, percentage }
  } catch (error: any) {
    return rejectWithValue(
      error.response?.data?.message || "Failed to fetch weekly progress."
    );
  }
});

// Async thunk for fetching monthly progress percentages
export const fetchMonthlyProgress = createAsyncThunk<
  Progress[], // Response type
  { month: string; year: string }, // Payload type
  { rejectValue: string } // Error type
>(
  "dashboard/fetchMonthlyProgress",
  async ({ month, year }, { rejectWithValue }) => {
    try {
      const response = await getMonthlyProgressAPI(month, year);
      if (!response || !Array.isArray(response)) {
        throw new Error("Invalid response format");
      }
      return response; // Array of Progress { date, percentage }
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to fetch monthly progress."
      );
    }
  }
);

const dashboardSlice = createSlice({
  name: "dashboard",
  initialState,
  reducers: {
    resetDashboardState: () => initialState,
    // New action to clear taskIdCollection
    clearTaskIdCollection: (state) => {
      state.taskIdCollection = [];
    },
    setRecentMoodLog: (state, action) => {
      state.recentMoodLog = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Mood Log Actions
      .addCase(createMoodLog.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createMoodLog.fulfilled, (state, action) => {
        state.loading = false;
        state.moodLogs.push(action.payload);
        state.recentMoodLog = action.payload;
      })
      .addCase(createMoodLog.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to create mood log.";
      })
      .addCase(fetchRecentMoodLog.pending, (state) => {
        state.recentMoodLog = null;
        state.moodLoading = true;
        state.error = null;
      })
      .addCase(fetchRecentMoodLog.fulfilled, (state, action) => {
        state.recentMoodLog = null;
        state.moodLoading = false;
        state.recentMoodLog = action.payload;
      })
      .addCase(fetchRecentMoodLog.rejected, (state, action) => {
        state.moodLoading = false;
        state.error = action.payload || "Failed to fetch recent mood log.";
        state.recentMoodLog = null;
      })
      // Daily Tasks Actions
      .addCase(fetchDailyTasks.pending, (state) => {
        state.dailyTasks = [];
        state.taskLoading = true;
        state.error = null;
      })
      .addCase(fetchDailyTasks.fulfilled, (state, action) => {
        state.taskLoading = false;
        state.dailyTasks = action.payload;
      })
      .addCase(fetchDailyTasks.rejected, (state, action) => {
        state.taskLoading = false;
        state.error = action.payload || "Failed to fetch daily tasks.";
      })
      // Task Status Update Actions
      .addCase(updateTaskStatus.pending, (state) => {
        state.error = null;
      })
      .addCase(updateTaskStatus.fulfilled, (state, action) => {
        const { patientRoutineId, taskId, actualDate } = action.payload;

        // Check if an entry already exists with the same patientRoutineId & actualDate
        const existingEntryIndex = state.taskIdCollection.findIndex(
          (entry) =>
            entry.patientRoutineId === patientRoutineId &&
            entry.actualDate === actualDate
        );

        if (existingEntryIndex >= 0) {
          // Update the existing taskId
          state.taskIdCollection[existingEntryIndex].taskId = taskId;
        } else {
          // Add a new entry to the collection
          state.taskIdCollection.push({ patientRoutineId, taskId, actualDate });
        }
        state.dailyTasks = action.payload.dailyTasks;

        state.loading = false; // Reset loading state
      })
      // Weekly Progress Actions
      .addCase(fetchWeeklyProgress.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchWeeklyProgress.fulfilled, (state, action) => {
        state.loading = false;
        state.weeklyProgressPercentages = action.payload;

        // Update today's progress only if the current date is included in the payload
        const today = dayjs().format("YYYY-MM-DD");
        const progressForToday = action.payload.find(
          (progress) => dayjs(progress.date).format("YYYY-MM-DD") === today
        );

        // Only update todayProgress if the current date is in the payload
        if (progressForToday) {
          state.todayProgress = progressForToday;
        }
      })
      .addCase(fetchWeeklyProgress.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch weekly progress.";
        state.todayProgress = null; // Reset today's progress on failure
      })
      // Monthly Progress
      .addCase(fetchMonthlyProgress.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchMonthlyProgress.fulfilled, (state, action) => {
        state.loading = false;
        state.monthlyProgressPercentages = action.payload;
      })
      .addCase(fetchMonthlyProgress.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch monthly progress.";
      });
  },
});

export const { resetDashboardState, setRecentMoodLog } = dashboardSlice.actions;

// Selectors
export const selectMoodLogs = (state: RootState) => state.dashboard.moodLogs;
export const selectRecentMoodLog = (state: RootState) =>
  state.dashboard.recentMoodLog;
export const selectDailyTasks = (state: RootState) =>
  state.dashboard.dailyTasks;
export const selectDashboardLoading = (state: RootState) =>
  state.dashboard.loading;
export const selectTaskLoading = (state: RootState) =>
  state.dashboard.taskLoading;
export const selectMoodLoading = (state: RootState) =>
  state.dashboard.moodLoading;
export const selectDashboardError = (state: RootState) => state.dashboard.error;
export const selectWeeklyProgress = (state: RootState) =>
  state.dashboard.weeklyProgressPercentages;
export const selectMonthlyProgress = (state: RootState) =>
  state.dashboard.monthlyProgressPercentages;
// Selector to access today's progress
export const selectTodayProgress = (state: RootState) =>
  state.dashboard.todayProgress;
export const { clearTaskIdCollection } = dashboardSlice.actions;

export default dashboardSlice.reducer;
