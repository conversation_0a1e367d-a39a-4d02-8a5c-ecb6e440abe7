import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import {
  createContact,
  getContacts,
  deleteContact,
  updateContact,
} from "@/services/api/patientContactsAPI";
import { RootState } from "../index";
import { PatientContact } from "@/types/schemas/patientContact";

interface ContactState {
  contacts: PatientContact[];
  loading: boolean;
  success: boolean;
  error: string | null;
}

const initialState: ContactState = {
  contacts: [],
  loading: false,
  success: false,
  error: null,
};

// Async Thunk for Creating Contact
export const submitContact = createAsyncThunk<
  PatientContact,
  PatientContact,
  { rejectValue: string }
>("contact/submitContact", async (payload, { rejectWithValue }) => {
  try {
    const response = await createContact(payload);
    return response;
  } catch (error: any) {
    return rejectWithValue(
      error.response?.data?.message || "Failed to submit contact."
    );
  }
});

// Async Thunk for Fetching Contacts
export const fetchContacts = createAsyncThunk<
  PatientContact[],
  void,
  { rejectValue: string }
>("contact/fetchContacts", async (_, { rejectWithValue }) => {
  try {
    const response = await getContacts();
    return response;
  } catch (error: any) {
    return rejectWithValue(
      error.response?.data?.message || "Failed to fetch contacts."
    );
  }
});

// Async Thunk for Deleting Contact
export const removeContact = createAsyncThunk<
  number,
  number,
  { rejectValue: string }
>("contact/removeContact", async (id, { rejectWithValue }) => {
  try {
    await deleteContact(id);
    return id;
  } catch (error: any) {
    return rejectWithValue(
      error.response?.data?.message || "Failed to delete contact."
    );
  }
});

// Async Thunk for Updating Contact
export const editContact = createAsyncThunk<
  PatientContact,
  PatientContact,
  { rejectValue: string }
>("contact/editContact", async (payload, { rejectWithValue }) => {
  try {
    const response = await updateContact(payload);
    return response;
  } catch (error: any) {
    return rejectWithValue(
      error.response?.data?.message || "Failed to update contact."
    );
  }
});

const contactSlice = createSlice({
  name: "contact",
  initialState,
  reducers: {
    resetContactState: () => initialState,
  },
  extraReducers: (builder) => {
    builder
      .addCase(submitContact.pending, (state) => {
        state.loading = true;
        state.success = false;
        state.error = null;
      })
      .addCase(submitContact.fulfilled, (state) => {
        state.loading = false;
        state.success = true;
      })
      .addCase(submitContact.rejected, (state, action) => {
        state.loading = false;
        state.error =
          typeof action.payload === "string"
            ? action.payload
            : "Failed to submit contact.";
      })
      .addCase(fetchContacts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchContacts.fulfilled, (state, action) => {
        state.loading = false;
        state.contacts = action.payload;
      })
      .addCase(fetchContacts.rejected, (state, action) => {
        state.loading = false;
        state.error =
          typeof action.payload === "string"
            ? action.payload
            : "Failed to fetch contacts.";
      })
      .addCase(removeContact.fulfilled, (state, action) => {
        state.loading = false;

        state.contacts = state.contacts.filter(
          (contact) => contact.contactId !== action.payload
        );
      })
      .addCase(removeContact.pending, (state, action) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(removeContact.rejected, (state, action) => {
        state.loading = false;

        state.error =
          typeof action.payload === "string"
            ? action.payload
            : "Failed to delete contact.";
      })
      .addCase(editContact.fulfilled, (state, action) => {
        const index = state.contacts.findIndex(
          (contact) => contact.contactId === action.payload.contactId
        );
        if (index !== -1) {
          state.contacts[index] = action.payload;
        }
      })
      .addCase(editContact.rejected, (state, action) => {
        state.error =
          typeof action.payload === "string"
            ? action.payload
            : "Failed to update contact.";
      });
  },
});

export const { resetContactState } = contactSlice.actions;

// Selectors
export const selectContacts = (state: RootState) =>
  state.patientContact.contacts;
export const selectContactLoading = (state: RootState) =>
  state.patientContact.loading;
export const selectContactSuccess = (state: RootState) =>
  state.patientContact.success;
export const selectContactError = (state: RootState) =>
  state.patientContact.error;

export default contactSlice.reducer;
