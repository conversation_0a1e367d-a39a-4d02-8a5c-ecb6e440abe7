import axiosInstance from "@/services/axiosInstance";
import { store } from "@/store";
import {
  CreateMedicationDetailsPayload,
  FormulaTask,
  FrequencyResponse,
  MedicationCategory,
  MedicationTask,
  CreateFormulaDetailsPayload,
  UpdateMedicationDetailsPayload,
  UpdateFormulaDetailsPayload
} from "@/types/schemas/task";
import { TimezoneService } from "@/utils/timezoneService";
import { createAsyncThunk } from "@reduxjs/toolkit";


export const fetchMedicationCategories = createAsyncThunk<
  MedicationCategory[],
  void,
  { rejectValue: string }
>('task/fetchMedicationCategories', async (_, thunkAPI) => {
  try {
    const response = await axiosInstance.get('/TaskManager/GetCategories');
    return response.data;
  } catch (error: any) {
    return thunkAPI.rejectWithValue(
      error.response?.data?.message || 'Failed to fetch categories'
    );
  }
});


export const fetchFrequencyData = createAsyncThunk<
  FrequencyResponse,
  void,
  { rejectValue: string }
>('task/fetchFrequencyData', async (_, thunkAPI) => {
  try {
    const response = await axiosInstance.get('/TaskManager/GetFrequency');
    return response.data;
  } catch (error: any) {
    return thunkAPI.rejectWithValue(
      error.response?.data?.message || 'Failed to fetch frequency data'
    );
  }
});


export const fetchMedicationTasks = createAsyncThunk<MedicationTask[], void>(
  'task/fetchMedicationTasks',
  async (_, { rejectWithValue }) => {
    try {
      const convertToZoneId = TimezoneService.getTimezoneProps()?.convertToZoneId;
      const response = await axiosInstance.get<MedicationTask[]>(
        '/TaskManager/GetMedicationTasksList', { params: { convertToZoneId } },
      );
      return response.data;
    } catch (error: any) {
      if (error.response.status == 404) {
        return []
      }

      return rejectWithValue(error.response?.data?.message || 'Failed to fetch medication tasks');
    }
  }
);

export const fetchFormulaTasks = createAsyncThunk<FormulaTask[], void, { rejectValue: string }>(
  'task/fetchFormulaTasks',
  async (_, { rejectWithValue }) => {
    try {
      const convertToZoneId = TimezoneService.getTimezoneProps()?.convertToZoneId;
      const response = await axiosInstance.get<FormulaTask[]>('/TaskManager/GetFormulaTasksList', { params: { convertToZoneId } });

      return response.data;
    } catch (error: any) {
      if (error?.response?.status == 404) {
        return []
      }
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch formula tasks');
    }
  }
);

export const fetchFormulaUnitList = createAsyncThunk<any[], void, { rejectValue: string }>(
  'task/fetchFormulaUnitList',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.get<any[]>('/TaskManager/GetFormulaUnitList');

      return response.data;
    } catch (error: any) {
      if (error?.response?.status == 404) {
        return []
      }
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch formula units');
    }
  }
);

export const deleteTask = createAsyncThunk<
  number,
  number,
  { rejectValue: string }
>('task/deleteTask', async (taskId, thunkAPI) => {
  try {
    await axiosInstance.delete(`/TaskManager/${taskId}`);
    return taskId; // Return the deleted task ID
  } catch (error: any) {
    return thunkAPI.rejectWithValue(
      error.response?.data?.message || 'Failed to delete task'
    );
  }
});

export const fetchTaskById = createAsyncThunk<
  any, // Replace `any` with the appropriate type if available
  number,
  { rejectValue: string }
>('task/fetchTaskById', async (taskId, thunkAPI) => {
  try {
    const convertToTimeZone = TimezoneService.getTimezoneProps()?.convertToZoneId;
    const response = await axiosInstance.get(`/TaskManager/${taskId}`, { params: { convertToTimeZone } });
    return response.data;
  } catch (error: any) {
    return thunkAPI.rejectWithValue(
      error.response?.data?.message || 'Failed to fetch task details'
    );
  }
});

export const fetchFormulaById = createAsyncThunk<
  any, // Replace `any` with the appropriate type if available
  number,
  { rejectValue: string }
>('task/fetchFormulaById', async (formulaId, thunkAPI) => {
  try {
    const convertToZoneId = TimezoneService.getTimezoneProps()?.convertToZoneId;
    const response = await axiosInstance.get(`/TaskManager/GetFormulaById/${formulaId}`,
      {
        params: { convertToZoneId }
      });
    return response.data;
  } catch (error: any) {
    return thunkAPI.rejectWithValue(
      error.response?.data?.message || 'Failed to fetch formula details'
    );
  }
});

export const createMedicationDetails = createAsyncThunk<
  number, // Response type
  CreateMedicationDetailsPayload, // Payload type
  { rejectValue: string } // Rejection type
>(
  "task/createMedicationDetails",
  async (payload, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.post<number>(
        "/TaskManager/CreateMedicationDetails",
        payload
      );
      if (response.data) {
        await store.dispatch(fetchTaskById(response.data))
      }
      return response.data; // Returns the ID of the created record
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to create medication details"
      );
    }
  }
);

export const createFormulaDetails = createAsyncThunk<
  number, // Response type
  CreateFormulaDetailsPayload, // Payload type
  { rejectValue: string } // Rejection type
>(
  "task/createFormulaDetails",
  async (payload, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.post<number>(
        "/TaskManager/CreateFormulaDetails",
        payload
      );
      return response.data; // Returns the ID of the created record
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to create formula details"
      );
    }
  }
);

export const updateMedicationDetails = createAsyncThunk<
  number,
  UpdateMedicationDetailsPayload
>(
  'task/updateMedicationDetails',
  async (payload, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.put('/TaskManager/UpdateMedicationDetails', { model: payload });
      return response.data; // Expected response: ID of the updated record
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update medication details');
    }
  }
);

export const updateFormulaDetails = createAsyncThunk<number, UpdateFormulaDetailsPayload>(
  "task/updateFormulaDetails",
  async (payload, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.put<number>(
        "TaskManager/UpdateFormulaDetails",
        { model: payload }
      );
      return response.data; // Assuming API returns an ID (e.g., 9)
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.message || "Failed to update formula details";
      return rejectWithValue(errorMessage);
    }
  }
);