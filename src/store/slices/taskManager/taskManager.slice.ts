import { createSlice, PayloadAction } from "@reduxjs/toolkit";

import {
  FormulaTask,
  FrequencyResponse,
  MedicationCategory,
  MedicationTask,
  TaskState,
  TaskStatus,
} from "@/types/schemas/task";
import {
  deleteTask,
  fetchTaskById,
  fetchFormulaById,
  fetchFormulaTasks,
  fetchFrequencyData,
  fetchMedicationCategories,
  fetchMedicationTasks,
  createMedicationDetails,
  createFormulaDetails,
  updateMedicationDetails,
  updateFormulaDetails,
  fetchFormulaUnitList,
} from "./taskManager.middleware";
import { RootState } from "../../index";

const initialState: TaskState = {
  error: "",
  status: {
    medCategories: TaskStatus.IDLE,
    medicationTasks: TaskStatus.IDLE,
    formulaTasks: TaskStatus.IDLE,
    frequencies: TaskStatus.IDLE,
    deleteTask: TaskStatus.IDLE,
    fetchTaskById: TaskStatus.IDLE,
    fetchFormulaById: TaskStatus.IDLE,
    createMedicationStatus: TaskStatus.IDLE,
    updateMedicationDetails: TaskStatus.IDLE,
    createFormulaTaskStatus: TaskStatus.IDLE,
    updateFormulaStatus: TaskStatus.IDLE,
    formulaUnitListStatus: TaskStatus.IDLE,
  },
  createdMedicationId: null,
  updatedRecordId: null,
  taskDetails: null,
  formulaDetails: null,
  days: [],
  weekIndexes: [],
  frequencies: [],
  reminderTimes: [],
  categories: [],
  medicationTasks: [] as MedicationTask[],
  formulaTasks: [] as FormulaTask[],
  fromUpdate: null,
  formulaUnitList: []
};

const handlePending = (state: any, statusKey: keyof typeof state.status) => {
  state.status[statusKey] = TaskStatus.LOADING;
  state.error = "";
};

const handleRejected = (
  state: any,
  statusKey: keyof typeof state.status,
  error: any,
  defaultMessage: string
) => {
  state.status[statusKey] = TaskStatus.ERROR;
  state.error = error || defaultMessage;
};

const taskSlice = createSlice({
  name: "task",
  initialState,
  reducers: {
    clearTaskDetails: (state) => {
      state.status.fetchTaskById = TaskStatus.IDLE;
      state.taskDetails = null;
    },
    clearFromulaDetails: (state) => {
      state.status.fetchFormulaById = TaskStatus.IDLE;
      state.formulaDetails = null;
    },
    setIsFromUpdate: (state, action: PayloadAction<number | null>) => {
      state.fromUpdate = action.payload;
    },
    resetTaskState: () => {
      return initialState;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchMedicationCategories.pending, (state) =>
        handlePending(state, "medCategories")
      )
      .addCase(
        fetchMedicationCategories.fulfilled,
        (state, action: PayloadAction<MedicationCategory[]>) => {
          state.categories = action.payload;
          state.status.medCategories = TaskStatus.SUCCESS;
        }
      )
      .addCase(fetchMedicationCategories.rejected, (state, action) =>
        handleRejected(
          state,
          "medCategories",
          action.payload,
          "Failed to fetch categories"
        )
      )
      .addCase(fetchFrequencyData.pending, (state) =>
        handlePending(state, "frequencies")
      )
      .addCase(
        fetchFrequencyData.fulfilled,
        (state, action: PayloadAction<FrequencyResponse>) => {
          state.frequencies = action.payload.frequencies;
          state.reminderTimes = action.payload.reminderTimes;
          state.weekIndexes = action.payload.weekIndexes;
          state.days = action.payload.days;
          state.status.frequencies = TaskStatus.SUCCESS;
        }
      )
      .addCase(fetchFrequencyData.rejected, (state, action) =>
        handleRejected(
          state,
          "frequencies",
          action.payload,
          "Failed to fetch frequency data"
        )
      )
      .addCase(fetchMedicationTasks.pending, (state) =>
        handlePending(state, "medicationTasks")
      )
      .addCase(fetchMedicationTasks.fulfilled, (state, action) => {
        state.medicationTasks = action.payload;
        state.status.medicationTasks = TaskStatus.SUCCESS;
      })
      .addCase(fetchMedicationTasks.rejected, (state, action) =>
        handleRejected(
          state,
          "medicationTasks",
          action.payload,
          "Failed to fetch medication tasks"
        )
      )
      .addCase(fetchFormulaTasks.pending, (state) =>
        handlePending(state, "formulaTasks")
      )
      .addCase(fetchFormulaTasks.fulfilled, (state, action) => {
        state.status.formulaTasks = TaskStatus.SUCCESS;
        state.formulaTasks = action.payload;
      })
      .addCase(fetchFormulaTasks.rejected, (state, action) =>
        handleRejected(
          state,
          "formulaTasks",
          action.payload,
          "Failed to fetch formula tasks"
        )
      )
      .addCase(fetchFormulaUnitList.pending, (state) =>
        handlePending(state, "formulaUnitList")
      )
      .addCase(fetchFormulaUnitList.fulfilled, (state, action) => {
        state.status.formulaUnitListStatus = TaskStatus.SUCCESS;
        state.formulaUnitList = action.payload as any;
      })
      .addCase(fetchFormulaUnitList.rejected, (state, action) =>
        handleRejected(
          state,
          "formulaUnitList",
          action.payload,
          "Failed to fetch formula unit list"
        )
      )
      // Delete Task
      .addCase(deleteTask.pending, (state) =>
        handlePending(state, "deleteTask")
      )
      .addCase(deleteTask.fulfilled, (state) => {
        state.status.deleteTask = TaskStatus.SUCCESS;
      })
      .addCase(deleteTask.rejected, (state, action) =>
        handleRejected(
          state,
          "deleteTask",
          action.payload,
          "Failed to delete task"
        )
      )
      // Fetch Task By ID
      .addCase(fetchTaskById.pending, (state) =>
        handlePending(state, "fetchTaskById")
      )
      .addCase(fetchTaskById.fulfilled, (state, action: PayloadAction<any>) => {
        state.taskDetails = action.payload;
        state.status.fetchTaskById = TaskStatus.SUCCESS;
      })
      .addCase(fetchTaskById.rejected, (state, action) =>
        handleRejected(
          state,
          "fetchTaskById",
          action.payload,
          "Failed to fetch task details"
        )
      )
      // Fetch Formula By ID
      .addCase(fetchFormulaById.pending, (state) =>
        handlePending(state, "fetchFormulaById")
      )
      .addCase(
        fetchFormulaById.fulfilled,
        (state, action: PayloadAction<any>) => {
          state.formulaDetails = action.payload;
          state.status.fetchFormulaById = TaskStatus.SUCCESS;
        }
      )
      .addCase(fetchFormulaById.rejected, (state, action) =>
        handleRejected(
          state,
          "fetchFormulaById",
          action.payload,
          "Failed to fetch formula details"
        )
      )
      .addCase(createMedicationDetails.pending, (state) =>
        handlePending(state, "createMedicationStatus")
      )
      .addCase(createMedicationDetails.fulfilled, (state, action) => {
        state.status.createMedicationStatus = TaskStatus.SUCCESS;
        state.createdMedicationId = action.payload; // Optional: Log the new record ID
      })
      .addCase(createMedicationDetails.rejected, (state, action) =>
        handleRejected(
          state,
          "createMedicationStatus",
          action.payload,
          "Failed to create medication details"
        )
      )
      .addCase(createFormulaDetails.pending, (state) =>
        handlePending(state, "createFormulaTaskStatus")
      )
      .addCase(createFormulaDetails.fulfilled, (state, action) => {
        state.status.createFormulaTaskStatus = TaskStatus.SUCCESS;
      })
      .addCase(createFormulaDetails.rejected, (state, action) =>
        handleRejected(
          state,
          "createFormulaTaskStatus",
          action.payload,
          "Failed to create formula details"
        )
      )
      .addCase(updateMedicationDetails.pending, (state) =>
        handlePending(state, "updateMedicationDetails")
      )
      .addCase(updateMedicationDetails.fulfilled, (state, action) => {
        state.status.updateMedicationDetails = TaskStatus.SUCCESS;
        state.updatedRecordId = action.payload; // Store the updated record ID
      })
      .addCase(updateMedicationDetails.rejected, (state, action) =>
        handleRejected(
          state,
          "updateMedicationDetails",
          action.payload,
          "Failed to update medication details"
        )
      )
      .addCase(updateFormulaDetails.pending, (state) => {
        state.status.updateFormulaStatus = TaskStatus.LOADING;
        state.error = "";
      })
      .addCase(updateFormulaDetails.fulfilled, (state, action) => {
        state.status.updateFormulaStatus = TaskStatus.SUCCESS;
      })
      .addCase(updateFormulaDetails.rejected, (state, action) => {
        state.status.updateFormulaStatus = TaskStatus.ERROR;
        state.error = action.payload as string;
      });
  },
});

export const { clearTaskDetails, clearFromulaDetails, setIsFromUpdate, resetTaskState } =
  taskSlice.actions;
export const selectTask = (state: RootState) => state.task;

export default taskSlice.reducer;
