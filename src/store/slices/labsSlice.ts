import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import {
  createLabEntry as createLabEntryAPI,
  updateLabEntry as updateLabEntryAPI,
  getRecentPheResults as getRecentPheResultsAPI,
  deleteLabEntry as deleteLabEntryAPI,
  getLabPheTrends as getLabPheTrendsAPI,
} from "@/services/api/labsAPI";
import { LabPayload, PheResult } from "@/types/schemas/labs";
import { RootState } from "..";
import dayjs from "dayjs";

interface LabEntry {
  id: number;
  pheLevel: number;
  pheMetrics: number;
  dateOfTest: string;
  dateOfResult: string;
}

interface LabState {
  labEntries: LabEntry[];
  recentPheResults: PheResult[];
  loading: boolean;
  success: boolean;
  error: string | null;
  dateRange: {
    fromDate: string;
    toDate: string;
  };
}

const initialState: LabState = {
  labEntries: [],
  recentPheResults: [],
  loading: false,
  success: false,
  error: null,
  dateRange: {
    fromDate: dayjs().subtract(89, "day").format("YYYY-MM-DD"),
    toDate: dayjs().format("YYYY-MM-DD"),
  },
};

// Async Thunk for Getting Recent Phe Results
export const fetchRecentPheResults = createAsyncThunk<
  PheResult[],
  void,
  { rejectValue: string }
>("lab/fetchRecentPheResults", async (_, { rejectWithValue }) => {
  try {
    const response = await getRecentPheResultsAPI();
    return response;
  } catch (error: any) {
    return rejectWithValue(
      error.response?.data?.message || "Failed to fetch recent Phe results."
    );
  }
});

// Async Thunk for Creating Lab Entry
export const submitLabEntry = createAsyncThunk<
  LabEntry,
  LabPayload,
  { rejectValue: string }
>("lab/submitLabEntry", async (payload, { rejectWithValue }) => {
  try {
    const response = await createLabEntryAPI(payload);
    return response;
  } catch (error: any) {
    return rejectWithValue(error.message || "Failed to submit lab entry.");
  }
});

// Async Thunk for Updating Lab Entry
export const updateLabResult = createAsyncThunk<
  LabEntry,
  LabPayload,
  { rejectValue: string }
>("lab/updateLabResult", async (payload, { rejectWithValue }) => {
  try {
    const response = await updateLabEntryAPI(payload);
    return response;
  } catch (error: any) {
    return rejectWithValue(
      error.response?.data?.message || "Failed to update lab entry"
    );
  }
});

// Async Thunk for Deleting Lab Entry
export const deleteLabResult = createAsyncThunk<
  number, // The ID of the deleted lab entry
  number, // The payload is the ID
  { rejectValue: string }
>("lab/deleteLabResult", async (id, { rejectWithValue }) => {
  try {
    await deleteLabEntryAPI(id); // Call the API
    return id; // Return the deleted ID to update the state
  } catch (error: any) {
    return rejectWithValue(
      error.response?.data?.message || "Failed to delete lab entry"
    );
  }
});

export const fetchLabPheTrends = createAsyncThunk<
  any, // Define the return type based on API response structure
  { fromDate: string; toDate: string }, // Payload structure
  { rejectValue: string }
>("lab/fetchLabPheTrends", async (payload, { rejectWithValue }) => {
  try {
    let [fromDate, toDate] = [payload?.fromDate, payload?.toDate];

    if (!fromDate) {
      fromDate = dayjs().startOf("month").format("YYYY-MM-DD");
    }
    if (!toDate) {
      toDate = dayjs().endOf("month").format("YYYY-MM-DD");
    }

    const response = await getLabPheTrendsAPI(fromDate, toDate);
    return response; // API response
  } catch (error: any) {
    return rejectWithValue(
      error.response?.data?.message || "Failed to fetch lab Phe trends."
    );
  }
});

const labSlice = createSlice({
  name: "lab",
  initialState,
  reducers: {
    selectDateRange: (state, action) => {
      state.dateRange = {
        toDate:
          action.payload?.toDate ||
          dayjs().startOf("month").format("YYYY-MM-DD"),
        fromDate:
          action.payload?.fromDate ||
          dayjs().endOf("month").format("YYYY-MM-DD"),
      };
    },
    resetLabState: () => initialState,
    resetDateRange: (state) => {
      state.dateRange = {
         fromDate: dayjs().subtract(89, "day").format("YYYY-MM-DD"),
         toDate: dayjs().format("YYYY-MM-DD"),
      };
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Recent Phe Results
      .addCase(fetchRecentPheResults.pending, (state) => {
        state.error = null;
        // state.recentPheResults= [];
      })
      .addCase(fetchRecentPheResults.fulfilled, (state, action) => {
        state.loading = false;
        state.recentPheResults = action.payload;
      })
      .addCase(fetchRecentPheResults.rejected, (state, action) => {
        state.loading = false;
        state.recentPheResults = [];
        state.error = action.payload || "Failed to fetch recent Phe results.";
      })
      // Submit Lab Entry
      .addCase(submitLabEntry.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(submitLabEntry.fulfilled, (state, action) => {
        state.loading = false;
        state.success = true;
        state.labEntries.push(action.payload);
      })
      .addCase(submitLabEntry.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to submit lab entry.";
      })
      // Update Lab Entry
      .addCase(updateLabResult.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(updateLabResult.fulfilled, (state, action) => {
        state.loading = false;
        state.success = true;
        const updatedEntry = action.payload;
        state.labEntries = state.labEntries.map((entry) =>
          entry.id === updatedEntry.id ? updatedEntry : entry
        );
      })
      .addCase(updateLabResult.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to update lab entry";
      }) // Delete Lab Entry
      .addCase(deleteLabResult.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(deleteLabResult.fulfilled, (state, action) => {
        state.loading = false;
        state.success = true;
        // Remove the deleted entry from the list
        state.labEntries = state.labEntries.filter(
          (entry) => entry.id !== action.payload
        );
      })
      .addCase(deleteLabResult.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to delete lab entry";
      })
      .addCase(fetchLabPheTrends.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(fetchLabPheTrends.fulfilled, (state, action) => {
        state.loading = false;
        state.success = true;
        // Assuming the response needs to be stored as a list or graph data
        state.labEntries = action.payload;
      })
      .addCase(fetchLabPheTrends.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch lab Phe trends.";
      });
  },
});

export const { resetLabState, selectDateRange , resetDateRange} = labSlice.actions;

// Selectors
export const selectLabs = (state: RootState) => state.labs;
export const selectLabLoading = (state: RootState) => state.labs.loading;
export const selectLabSuccess = (state: RootState) => state.labs.success;
export const selectLabError = (state: RootState) => state.labs.error;
export const selectLabEntries = (state: RootState) => state.labs.labEntries;
export const selectRecentPheResults = (state: RootState) =>
  state.labs.recentPheResults;

// Selector to get the latest Phe result
export const selectLatestPheResult = (state: RootState) => {
  const recentResults = state.labs.recentPheResults || [];

  if (recentResults.length === 0) return null;

  const latestResult = recentResults.reduce((latest, current) => {
    const latestDate = new Date(latest.testDate);
    const currentDate = new Date(current.testDate);
    return currentDate > latestDate ? current : latest;
  });

  return latestResult;
};

export default labSlice.reducer;
