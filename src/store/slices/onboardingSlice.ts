import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { RootState } from "../index";
import { IRoleOptions } from "@/components/molecules/AnimatedSelector/RoleSelector";
import {
  deleteAuth0Id,
  getIntentionQuestionsById,
  postIntentionSurveyQuestion,
} from "@/services/api/onBoardingAPI";
import { PostSurveyPayload } from "@/types/schemas/onbaording";

export enum AUTH_METHOD {
  LOGIN = "LOGIN",
  SIGN_UP = "SIGN_UP",
}


interface OnboardingState {
  completed: boolean;
  isPreferenceSelected: boolean;
  isTutorial: boolean;
  role: IRoleOptions;
  loading: boolean;
  error: string | null;
  surveyQuestions: Array<any>;
  surveyResponses: Array<any>;
  accountData: any;
  selectedAuthMethod?: AUTH_METHOD | null
  reOnboarded: boolean
}

const initialState: OnboardingState = {
  completed: false,
  isPreferenceSelected: false,
  isTutorial: false,
  role: {
    id: 0,
  },
  loading: false,
  error: null,
  surveyQuestions: [],
  surveyResponses: [],
  accountData: {},
  selectedAuthMethod: null,
  reOnboarded: false
};

export const fetchIntentionQuestionsById = createAsyncThunk<any, number>(
  "surveyIntentions/fetchById",
  async (id) => {
    try {
      const response = await getIntentionQuestionsById(id);
      return response;
    } catch (error) {
      throw error;
    }
  }
);

export const postSurveyResponses = createAsyncThunk<void, PostSurveyPayload>(
  "surveyIntentions/post",
  async (surveryresponses) => {
    try {
      const response = await postIntentionSurveyQuestion(surveryresponses);
      return response;
    } catch (error) {
      throw error;
    }
  }
);

export const deleteAuth0User = createAsyncThunk<string, string>(
  "Auth0Authentication/delete",
  async (id) => {
    try {
      await deleteAuth0Id(id);
      return id;
    } catch (error) {
      throw error;
    }
  }
);

const onboardingSlice = createSlice({
  name: "onboarding",
  initialState,
  reducers: {
    completeOnboarding: (state) => {
      state.completed = true;
    },
    resetOnboarding: (state) => {
      state.completed = false;
      state.isPreferenceSelected = false;
    },
    resetOnboardingState: (state) => {
      state.completed = false;
    },
    setPrefrenceSelected: (state, action) => {
      state.isPreferenceSelected = action.payload;
    },
    setTutorial: (state, action) => {
      state.isTutorial = action.payload;
    },
    setRole: (state, action) => {
      state.role = action.payload;
    },
    setSurveyResponses: (state, action) => {
      state.surveyResponses = action.payload;
    },
    setAccountData: (state, action) => {
      state.accountData = action.payload
    },
    setSelectedAuthMethod: (state, action) => {
      state.selectedAuthMethod = action.payload;
    },
    setReOnboarded:(state, action) => {
      state.reOnboarded = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchIntentionQuestionsById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchIntentionQuestionsById.fulfilled, (state, action) => {
        state.loading = false;
        state.surveyQuestions = action.payload.value;
      })
      .addCase(fetchIntentionQuestionsById.rejected, (state, action) => {
        state.loading = false;
        state.error =
          action.error.message ?? "Failed to fetch survey questions by ID";
      })
      .addCase(postSurveyResponses.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(postSurveyResponses.fulfilled, (state, action) => {
        state.loading = false;
      })
      .addCase(postSurveyResponses.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message ?? "Failed to post survey questions";
      });
  }
});

export const {
  completeOnboarding,
  resetOnboarding,
  setPrefrenceSelected,
  setTutorial,
  setRole,
  setSurveyResponses,
  setAccountData,
  resetOnboardingState,
  setSelectedAuthMethod,
  setReOnboarded
} = onboardingSlice.actions;

export const selectOnboardingCompleted = (state: RootState) =>
  state.onboarding.completed;
export const selectIsPreferenceSelected = (state: RootState) =>
  state.onboarding.isPreferenceSelected;
export const selectOnboarding = (state: RootState) => state.onboarding;
export const selecRole = (state: RootState) => state.onboarding.role;
export const selectIntentionQuestionsById = (state: RootState) =>
  state.onboarding.surveyQuestions;
export const selectAccountInfo = (state: RootState) => state.onboarding.accountData;
export const selectAuthMethod = (state: RootState) => state.onboarding.selectedAuthMethod;
export const selectReOnBoarded = (state: RootState) =>
  state.onboarding.reOnboarded;
export default onboardingSlice.reducer;
