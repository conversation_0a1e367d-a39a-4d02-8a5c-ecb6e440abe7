import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import {
  postPheAllowance,
  getPheAllowance,
  updatePheAllowance,
  getPheAllowanceById,
  deletePheAllowanceById,
  getDailyConsumedPheAllowance,
} from "@/services/api/pheAllowanceAPI"; // Adjust the path based on your project
import { PheAllowance } from "@/types/schemas/pheAllowance"; // Adjust the path based on your project
import { RootState } from "../index"; // Adjust the import based on your project structure

// Define the state interface
interface PheAllowanceState {
  data: PheAllowance[];
  single: PheAllowance | null;
  dailyConsumedPheAllowance: number | null;
  dailyPheAllowance: number | null;
  loading: boolean;
  error: string | null;
}

// Initial state
const initialState: PheAllowanceState = {
  data: [],
  single: null,
  dailyConsumedPheAllowance: 0,
  dailyPheAllowance: 0,
  loading: false,
  error: null,
};

// Async thunks for PheAllowance API operations
export const fetchPheAllowances = createAsyncThunk<PheAllowance[]>(
  "pheAllowance/fetchAll",
  async () => {
    try {
      const response = await getPheAllowance();
      return response;
    } catch (error) {
      throw error;
    }
  }
);

export const fetchPheAllowanceById = createAsyncThunk<PheAllowance, string>(
  "pheAllowance/fetchById",
  async (id) => {
    try {
      const response = await getPheAllowanceById(id);
      return response;
    } catch (error) {
      throw error;
    }
  }
);

export const createPheAllowance = createAsyncThunk<PheAllowance, PheAllowance>(
  "pheAllowance/create",
  async (pheAllowance) => {
    try {
      const response = await postPheAllowance(pheAllowance);
      return response;
    } catch (error) {
      throw error;
    }
  }
);

export const updatePheAllowanceThunk = createAsyncThunk<
  PheAllowance,
  PheAllowance
>("pheAllowance/update", async (pheAllowance) => {
  try {
    const response = await updatePheAllowance(pheAllowance);
    return response;
  } catch (error) {
    throw error;
  }
});

export const deletePheAllowance = createAsyncThunk<number, number>(
  "pheAllowance/delete",
  async (id) => {
    try {
      await deletePheAllowanceById(id);
      return id;
    } catch (error) {
      throw error;
    }
  }
);

export const fetchDailyConsumedPheAllowance = createAsyncThunk<
  { dailyConsumedPheAllowance: number; dailyPheAllowance: number }, // Return type
  string, // Input type (date as a string)
  { rejectValue: string } // Rejection type
>("pheAllowance/fetchDailyConsumed", async (date, { rejectWithValue }) => {
  try {
    const response = await getDailyConsumedPheAllowance(date); // API call
    return {
      dailyConsumedPheAllowance: response?.dailyConsumedPheAllowance,
      dailyPheAllowance: response?.dailyPheAllowance,
    }; // Return both values as an object
  } catch (error: any) {
    return rejectWithValue(
      error.response?.data?.message ||
      "Failed to fetch daily consumed PHE allowance."
    );
  }
});

// Slice creation
const pheAllowanceSlice = createSlice({
  name: "pheAllowance",
  initialState,
  reducers: {
    resetPheState: (state) => {
      state.data = [];
      state.single = null;
      state.loading = false;
      state.error = null;
    },
    setDailyPhe: (state, action) => {
      state.dailyPheAllowance = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchPheAllowances.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPheAllowances.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload;
      })
      .addCase(fetchPheAllowances.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message ?? "Failed to fetch PheAllowances";
      })
      .addCase(fetchPheAllowanceById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPheAllowanceById.fulfilled, (state, action) => {
        state.loading = false;
        state.single = action.payload;
      })
      .addCase(fetchPheAllowanceById.rejected, (state, action) => {
        state.loading = false;
        state.error =
          action.error.message ?? "Failed to fetch PheAllowance by ID";
      })
      .addCase(createPheAllowance.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createPheAllowance.fulfilled, (state, action) => {
        state.loading = false;
        // state.data = state.data
        //   ? [...state.data, action.payload]
        //   : [action.payload];
      })
      .addCase(createPheAllowance.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message ?? "Failed to create PheAllowance";
      })
      .addCase(updatePheAllowanceThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updatePheAllowanceThunk.fulfilled, (state, action) => {
        state.loading = false;
        if (state.data) {
          state.data = state.data.map((item) =>
            item.id === action.payload.id ? action.payload : item
          );
        }
      })
      .addCase(updatePheAllowanceThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message ?? "Failed to update PheAllowance";
      })
      .addCase(deletePheAllowance.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deletePheAllowance.fulfilled, (state, action) => {
        state.loading = false;
        if (state.data) {
          state.data = state.data.filter((item) => item.id !== action.payload);
        }
      })
      .addCase(deletePheAllowance.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message ?? "Failed to delete PheAllowance";
      })
      .addCase(fetchDailyConsumedPheAllowance.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchDailyConsumedPheAllowance.fulfilled, (state, action) => {
        state.loading = false;
        state.dailyConsumedPheAllowance =
          action.payload.dailyConsumedPheAllowance;
        state.dailyPheAllowance = action.payload.dailyPheAllowance;
      })

      .addCase(fetchDailyConsumedPheAllowance.rejected, (state, action) => {
        state.loading = false;
        state.error =
          action.error.message ?? "Failed to fetch daily consumed PheAllowance";
      });
  },
});

// Export actions from reducers
export const { resetPheState, setDailyPhe } = pheAllowanceSlice.actions;

// Selectors
export const selectDailyConsumedPheAllowance = (state: RootState) =>
  state.pheAllowance.dailyConsumedPheAllowance;
export const selectDailyPheAllowance = (state: RootState) =>
  state.pheAllowance.dailyPheAllowance;
export const selectPheAllowances = (state: RootState) =>
  state.pheAllowance.data;
export const selectPheAllowanceById = (state: RootState) =>
  state.pheAllowance.single;
export const selectPheAllowanceLoading = (state: RootState) =>
  state.pheAllowance.loading;
export const selectPheAllowanceError = (state: RootState) =>
  state.pheAllowance.error;

export const pheAllowanceSelector = (state: RootState) => state.pheAllowance;

// Export the reducer
export default pheAllowanceSlice.reducer;
