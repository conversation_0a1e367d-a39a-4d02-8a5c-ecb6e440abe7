import { combineReducers, configureStore } from "@reduxjs/toolkit";
import { useDispatch, useSelector } from "react-redux";
import { persistStore, persistReducer } from "redux-persist";

import userReducer, { resetProfile } from "./slices/userSlice";
import pheAllowanceReducer, { resetPheState } from "./slices/pheAllowanceSlice";
import dietTrackerReducer, { resetDietState } from "./slices/dietTrackerSlice";
import labsReducer, { resetLabState } from "./slices/labsSlice";
import authReducer, { resetAuthState } from "./slices/authSlice";
import dashboardReducer, { resetDashboardState } from "./slices/dashboardSlice";
import MMKVStorage from "@/utils/MMKVStorage";
import onboardingReducer, { resetOnboarding } from "./slices/onboardingSlice";
import taskReducer, {
  resetTaskState,
} from "./slices/taskManager/taskManager.slice";
import notificationReducer, {
  resetNotificationState,
} from "./slices/notificationSlice";
import patientContactReducer, {
  resetContactState,
} from "./slices/patientContactSlice";
import settingsReducer, { resetSettingsState } from "./slices/settingsSlice";
import foodReducer from "./slices/foodSlice";

const persistConfig = {
  key: "root",
  storage: MMKVStorage, // Using MMKV for persistence
  whitelist: [
    "auth",
    "onboarding",
    "user",
    "dietTracker",
    "pheAllowance",
    "dashboardReducer",
    "notification",
    "patientContact",
    "settings",
    "food",
  ],
};

const rootReducer = combineReducers({
  auth: authReducer,
  onboarding: onboardingReducer,
  user: userReducer,
  pheAllowance: pheAllowanceReducer,
  dietTracker: dietTrackerReducer,
  labs: labsReducer,
  task: taskReducer,
  dashboard: dashboardReducer,
  notification: notificationReducer,
  patientContact: patientContactReducer,
  settings: settingsReducer,
  food: foodReducer,
});

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({ serializableCheck: false }),
});

export const resetStore = () => {
  store.dispatch(resetProfile());
  store.dispatch(resetPheState());
  store.dispatch(resetOnboarding());
  store.dispatch(resetDietState());
  store.dispatch(resetLabState());
  store.dispatch(resetTaskState());
  store.dispatch(resetDashboardState());
  store.dispatch(resetNotificationState());
  store.dispatch(resetContactState());
  store.dispatch(resetSettingsState());
  store.dispatch(resetAuthState());

  MMKVStorage.clear();
};

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export const useAppDispatch = useDispatch.withTypes<AppDispatch>();
export const useAppSelector = useSelector.withTypes<RootState>();

export const persistor = persistStore(store);
