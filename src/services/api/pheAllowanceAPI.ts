import { store } from "@/store";
import axiosInstance from "../axiosInstance";
import { PheAllowance } from "@/types/schemas/pheAllowance";

export const postPheAllowance = async (pheAllowance: PheAllowance) => {
  try {
    const response = await axiosInstance.post("/PheAllowance", {
      ...pheAllowance, // Spread the existing pheAllowance fields
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const getPheAllowance = async () => {
  try {
    const response = await axiosInstance.get(`/PheAllowance`, {
    });

    return response.data;
  } catch (error) {
    console.error("Error fetching Phe Allowance:", error);
    throw error;
  }
};

export const updatePheAllowance = async (pheAllowance: PheAllowance) => {
  try {
    const response = await axiosInstance.put("/PheAllowance", pheAllowance);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const getPheAllowanceById = async (id: string) => {
  try {
    const response = await axiosInstance.get(`/PheAllowance/${id}`);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const deletePheAllowanceById = async (id: number) => {
  try {
    const response = await axiosInstance.delete(`/PheAllowance/${id}`);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const getDailyConsumedPheAllowance = async (
  date: string
): Promise<number> => {
  try {
    const response = await axiosInstance.get(
      `/PheAllowance/DailyConsumedPheAllowance?date=${date}`
    );

    return response.data;
  } catch (error) {
    throw error;
  }
};
