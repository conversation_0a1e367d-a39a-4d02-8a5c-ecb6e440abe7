import { SearchBy } from '@/constants/searchFood';
import axiosInstance from '@/services/axiosInstance';
import { ScanItem } from '@/store/slices/dietTrackerSlice';
import { store } from '@/store';
import type { RootState } from '@/store';
import { selectIsSimplifiedDiet } from '@/store/slices/settingsSlice';
import { CreateFoodEntryPayload, CreateFoodPayload, Food, FoodEntryItem, Portions } from '@/types/schemas/dietTracker';
import { DietTrackerService } from '@/utils/dietTrackerService';

// API call for searching food
export const searchFood = async (searchQuery: string, searchBy: SearchBy = SearchBy.user, consumptionType: string): Promise<Food[]> => {
	try {
		const response = await axiosInstance.post('/Food/search', {
			text: searchQuery,
			searchType: searchBy, // Use the dynamic searchBy value from the enum
		});
		const state = store.getState() as RootState;
		const isSimplifiedDiet = selectIsSimplifiedDiet(state);
		// Transform the filtered data into SearchFoodItem objects
		return DietTrackerService.CalculatePheProteinFromSearchFoodResponse(response.data, consumptionType, isSimplifiedDiet);
	} catch (error) {
		console.error('Failed to fetch food: ', error);
		throw new Error('Failed to fetch food');
	}
};

export const getFrequentEntries = async (consumptionType: string): Promise<FoodSearchResponse[]> => {
	try {
		const response = await axiosInstance.get('/Food/GetFrequentEntries');
		// Filter the response data to include only entries with frequency greater than 1 (Azure Ticket: https://dev.azure.com/ThinkDigitally/Cycle%20Vita%20App/_workitems/edit/58920)
		const frequentFood = response.data.filter((food: any) => food.frequency > 1);
		const state = store.getState() as RootState;
		const isSimplifiedDiet = selectIsSimplifiedDiet(state);
		return DietTrackerService.ConvertToSearchFreqFoodResponse(frequentFood, consumptionType, isSimplifiedDiet);
	} catch (error) {
		console.error('Failed to fetch food: ', error);
		throw new Error('Failed to fetch food');
	}
};

export const getPortions = async (): Promise<Portions[]> => {
	try {
		const response = await axiosInstance.get('/Food/portions');
		return response.data;
	} catch (error) {
		console.error('Failed to fetch portions: ', error);
		throw new Error('Failed to fetch portions');
	}
};

export const createFood = async (payload: CreateFoodPayload): Promise<any> => {
	try {
		const response = await axiosInstance.post('/Food', payload);

		return response.data;
	} catch (error) {
		console.error('Failed to create food: ', error);
		throw new Error('Failed to create food');
	}
};

export const createFoodEntry = async (entry: CreateFoodEntryPayload): Promise<any> => {
	try {
		const response = await axiosInstance.post('/Food/CreateEntry', entry);

		return response.data;
	} catch (error) {
		console.error('Failed to create entry: ', error);
		throw new Error('Failed to create entry');
	}
};

export const listFoodEntries = async (date: string): Promise<any[]> => {
	try {
		const response = await axiosInstance.post('/Food/ListEntries', { date });
		return DietTrackerService.ConvertToListEntriesResponse(response.data);
	} catch (error) {
		console.error('Failed to fetch food entries:', error);
		throw new Error('Failed to fetch food entries');
	}
};

export const deleteFoodEntry = async (entryId: string): Promise<void> => {
	try {
		const response = await axiosInstance.delete('/Food/DeleteEntry', {
			data: { entryId }, // Pass `entryId` in the body
		});
	} catch (error) {
		console.error('Failed to delete entry:', error);
		throw new Error('Failed to delete food entry');
	}
};

export const updateFoodEntry = async (entry: {
	entryId: string;
	time: string;
	description: string;
	items: {
		description: string;
		quantity: number;
		unit: string;
		gram_weight: number;
		phe: number;
		protein: number;
		food_id: string;
	}[];
}): Promise<any> => {
	try {
		const response = await axiosInstance.patch('/Food/UpdateEntry', entry);
		return response.data;
	} catch (error) {
		console.error('Failed to update food entry:', error);
		throw new Error('Failed to update food entry');
	}
};

export const analyseFood = async (formData: FormData): Promise<any> => {
	try {
		const response = await axiosInstance.post('/Food/Analyse', formData, {
			headers: {
				'Content-Type': 'multipart/form-data', // Ensure correct header for form-data
			},
		});
		return DietTrackerService.ConvertMatchFoodToFoodByIdResponse(response.data ? response.data.value : response.data);
	} catch (error) {
		console.error('API error in analyseFoodAPI:', error);
		throw new Error('Failed to analyze food.');
	}
};

export const getFoodById = async (foodId: string, Editquantity: number, selectedUnit: string, gram_weight: number): Promise<FoodByIdResponse> => {
	try {
		const response = await axiosInstance.get(`/Food/${foodId}`);
		const state = store.getState() as RootState;
		const isSimplifiedDiet = selectIsSimplifiedDiet(state);
		return DietTrackerService.ConvertToFoodByIdResponse(response.data, Editquantity, selectedUnit, isSimplifiedDiet, gram_weight);
	} catch (error) {
		console.error('API error in getFoodByIdV2:', error);
		throw new Error('Failed to fetch food by ID.');
	}
};

// export const getFoodById = async (foodId: string, Editquantity: number, selectedUnit: string, gram_weight: number, selectedFood: FoodEntryItem): Promise<FoodByIdResponse> => {
// 	try {
// 		if ((Editquantity && Editquantity > 1) || selectedUnit || gram_weight) {
// 			let body = {}
// 			if (selectedFood) {

// 			}
// 			const response = await axiosInstance.post(`/Food/GetFoodByEntry`, body);
// 			return DietTrackerService.ConvertToFoodByIdResponse(response.data, selectedUnit);
// 		}
// 		else {
// 			const response = await axiosInstance.get(`/Food/${foodId}`);
// 			return DietTrackerService.ConvertToFoodByIdResponse(response.data, selectedUnit);
// 		}
// 	} catch (error) {
// 		console.error('API error in getFoodById:', error);
// 		throw new Error('Failed to fetch food by ID.');
// 	}
// };
