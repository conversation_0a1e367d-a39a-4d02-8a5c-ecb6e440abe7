import axiosInstance from "@/services/axiosInstance";
import { TimezoneService } from "@/utils/timezoneService";

interface MoodLog {
  id?: number;
  score: number;
  dateLogged: string;
}

interface Task {
  id: number;
  title: string;
  description: string;
  dueDate: string;
  completed: boolean;
}

interface TaskStatusUpdate {
  taskId: number;
  patientRoutineId: number;
  actualDate: string;
  isCompleted: boolean;
}

interface Progress {
  date: string;
  percentage: number;
}

export const createMoodLog = async (payload: {
  score: number;
  dateLogged: string;
}): Promise<MoodLog> => {
  try {
    const response = await axiosInstance.post("/MoodLog", payload);
    return response.data;
  } catch (error: any) {
    console.error("Failed to create mood log:", error);
    throw new Error(
      error.response?.data?.message || "Failed to create mood log."
    );
  }
};

export const getRecentMoodLog = async (
  dateLogged: string
): Promise<MoodLog> => {
  try {
    const response = await axiosInstance.get(
      `/MoodLog/GetRecentMoodLog?dateLogged=${encodeURIComponent(dateLogged)}`
    );
    return response.data;
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message || "Failed to fetch recent mood log."
    );
  }
};

export const getDailyTasks = async (curentDate: string): Promise<Task[]> => {
  try {
    const convertToZoneId = TimezoneService.getTimezoneProps()?.convertToZoneId;
    const response = await axiosInstance.get(`/TaskManager/GetDailyTasksList`, {
      params: { curentDate, convertToZoneId },
    });
    return response.data;
  } catch (error: any) {
    return [];
  }
};

// API call to update task completion status
export const updateTaskCompleteStatus = async (
  payload: TaskStatusUpdate
): Promise<number> => {
  try {
    const response = await axiosInstance.put(
      "/TaskManager/UpdateTaskCompleteStatus",
      payload
    );

    // Assuming the API returns the `taskId` in the response data
    return response.data;
  } catch (error: any) {
    console.error("Failed to update task completion status:", error);
    throw new Error(
      error.response?.data?.message ||
        "Failed to update task completion status."
    );
  }
};

export const getWeeklyProgress = async (
  fromDate: string,
  toDate: string
): Promise<Progress[]> => {
  try {
    const convertToZoneId = TimezoneService.getTimezoneProps()?.convertToZoneId;
    const response = await axiosInstance.get(
      `/TaskManager/GetWeeklyTasksList`,
      {
        params: { fromDate, toDate, convertToZoneId },
      }
    );
    return response.data;
  } catch (error: any) {
    console.error("Failed to fetch weekly tasks:", error);
    throw new Error(
      error.response?.data?.message || "Failed to fetch weekly tasks."
    );
  }
};

export const getMonthlyProgress = async (
  month: string,
  year: string
): Promise<Progress[]> => {
  try {
    const convertToZoneId = TimezoneService.getTimezoneProps()?.convertToZoneId;
    const response = await axiosInstance.get(
      `/TaskManager/GetMonthlyTasksList`,
      {
        params: { month, year, convertToZoneId },
      }
    );
    return response.data;
  } catch (error: any) {
    console.error("Failed to fetch monthly tasks:", error);
    throw new Error(
      error.response?.data?.message || "Failed to fetch monthly tasks."
    );
  }
};
