import axiosInstance from "@/services/axiosInstance";
import { LabPayload, PheResult } from "@/types/schemas/labs";

export const createLabEntry = async (payload: LabPayload): Promise<any> => {
  try {
    const response = await axiosInstance.post("/Lab", payload);

    return response;
  } catch (error: any) {
    console.error("Failed to create lab entry: ", error);
    throw new Error(
      error.response?.data?.message || "Failed to create lab entry"
    );
  }
};

export const updateLabEntry = async (payload: LabPayload): Promise<any> => {
  try {
    const response = await axiosInstance.put("/Lab", payload);

    return response.data;
  } catch (error) {
    console.error("Failed to update lab entry:", error);
    throw new Error("Failed to update lab entry");
  }
};

export const getRecentPheResults = async (): Promise<PheResult> => {
  try {
    const response = await axiosInstance.get("/Lab/GetRecentPheResults");
    return response.data;
  } catch (error: any) {
    console.error("Failed to fetch recent Phe results: ", error);
    throw new Error(
      error.response?.data?.message || "Failed to fetch recent Phe results"
    );
  }
};

export const deleteLabEntry = async (id: number): Promise<any> => {
  try {
    const response = await axiosInstance.delete("/Lab", {
      data: { id }, // Pass the ID in the request body
    });
    return response.data;
  } catch (error: any) {
    console.error("Failed to delete lab entry: ", error);
    throw new Error(
      error.response?.data?.message || "Failed to delete lab entry"
    );
  }
};

export const getLabPheTrends = async (
  fromDate: string,
  toDate: string
): Promise<any> => {
  try {
    const response = await axiosInstance.post("/Lab/GetLabPheTrends", {
      fromDate,
      toDate,
    });
    return response.data;
  } catch (error: any) {
    console.error("Failed to fetch lab Phe trends: ", error);
    throw new Error(
      error.response?.data?.message || "Failed to fetch lab Phe trends."
    );
  }
};
