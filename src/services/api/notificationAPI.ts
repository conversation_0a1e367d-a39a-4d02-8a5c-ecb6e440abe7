import { DisablePushNotification, PushNotification } from '@/types/schemas/PushNotification';
import axiosInstance from '../axiosInstance';

export const registerDevice = async (payload: PushNotification) => {
  try {
    const response = await axiosInstance.post(
      '/Notification/RegisterDevice',
      payload
    );
    return response?.data;
  } catch (error) {
    throw error;
  }
};

export const disableBackgroundNotify = async (payload: DisablePushNotification) => {
  try {
    const response = await axiosInstance.patch(
      '/Notification/togglenotification',
      payload
    );
    return response?.data;
  } catch (error) {
    throw error;
  }
};

