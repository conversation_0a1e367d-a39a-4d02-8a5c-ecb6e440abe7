import axiosInstance from "../axiosInstance";
import { PostSurveyPayload } from "@/types/schemas/onbaording";

export const postIntentionSurveyQuestion = async (payload: PostSurveyPayload): Promise<any> => {
  try {
    const response = await axiosInstance.post("/IntentionSurvey", payload);
    return response.data;
  } catch (error: any) {
    console.error("Failed post survey questions", error);
    throw error;
  }
};

export const getIntentionQuestionsById = async (id: number) => {
  try {
    const response = await axiosInstance.get(`/IntentionSurvey/${id}`);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const deleteAuth0Id = async (id: string) => {
  try {
    const response = await axiosInstance.delete(`/Auth0Authentication/${id}`);
    return response.data;
  } catch (error) {
    throw error;
  }
};
