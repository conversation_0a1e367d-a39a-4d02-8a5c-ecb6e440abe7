import axiosInstance from "@/services/axiosInstance";
import { PatientContact } from "@/types/schemas/patientContact";

export const createContact = async (payload: PatientContact): Promise<any> => {
  try {
    const response = await axiosInstance.post("/Contact", payload);
    return response.data;
  } catch (error: any) {
    console.error("Failed to create contact: ", error);
    throw new Error(
      error.response?.data?.message || "Failed to create contact"
    );
  }
};

export const getContacts = async (): Promise<PatientContact[]> => {
  try {
    const response = await axiosInstance.get("/Contact");
    return response.data;
  } catch (error: any) {
    if (error.response.status == 404) {
      return []
    }
    throw new Error(
      error.response?.data?.message || "Failed to fetch contacts"
    );
  }
};

export const updateContact = async (
  payload: PatientContact
): Promise<PatientContact> => {
  try {
    const response = await axiosInstance.put("/Contact", payload);
    return response.data;
  } catch (error: any) {
    console.error("Failed to update contact: ", error);
    throw new Error(
      error.response?.data?.message || "Failed to update contact"
    );
  }
};

export const deleteContact = async (id: number): Promise<void> => {
  try {
    await axiosInstance.delete("/Contact", {
      data: { id },
    });
  } catch (error: any) {
    console.error("Failed to delete contact: ", error);
    throw new Error(
      error.response?.data?.message || "Failed to delete contact"
    );
  }
};
