import axios from "axios";
import axiosRetry from "axios-retry";
import { store } from "@/store";
import { logout } from "@/store/slices/authSlice";
import { resetProfile } from "@/store/slices/userSlice";
import { resetPheState } from "@/store/slices/pheAllowanceSlice";
import { BASE_URL } from "@/utils/environment";

// Create the Axios instance
const axiosInstance = axios.create({
  baseURL: BASE_URL,
  timeout: 180000,
});

// Add retry logic to the axios instance
axiosRetry(axiosInstance, {
  retries: 3, // Number of retry attempts
  retryDelay: (retryCount) => retryCount * 1000, // Exponential delay (1s, 2s, 3s)
  retryCondition: async (error) => {
    // Retry on network errors or 5xx status codes
    if (axiosRetry.isNetworkError(error)) {
      return true;
    }
    if (error.response?.status && error.response.status >= 500) {
      return true;
    }
    return false; // Don't retry otherwise
  },
});

// Request interceptor
axiosInstance.interceptors.request.use(
  async (config) => {
    let state = store.getState();
    let idToken = state?.auth?.idToken;
    // console.log(`Bearer ${idToken}`,"`Bearer ${idToken}`");
    if (idToken) {
      config.headers["Authorization"] = `Bearer ${idToken}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor
axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized errors by resetting state and logging out
      store.dispatch(resetProfile());
      store.dispatch(resetPheState());
      store.dispatch(logout());
    }
    return Promise.reject(error);
  }
);

export default axiosInstance;
