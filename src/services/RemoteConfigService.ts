import remoteConfig from "@react-native-firebase/remote-config";

// Exported Interface
export interface IRemoteConfigValues {
  linkPrivacyPolicy: string;
  supportPhoneNumber: string;
  supportEmail: string;
  enableOnboarding: boolean;
  linkTermsAndConditions: string;
}

// Exported Default Values
export const defaultValues: IRemoteConfigValues = {
  linkPrivacyPolicy: "https://cyclepharma.com/our-company/compliance-ethics/",
  supportPhoneNumber: "",
  supportEmail: "<EMAIL>",
  enableOnboarding: false,
  linkTermsAndConditions:
    "https://cyclepharma.com/our-company/compliance-ethics/terms-conditions/",
};

class RemoteConfig {
  private static instance: RemoteConfig | null = null;

  private constructor() {} // Private to enforce singleton

  public static getInstance(): RemoteConfig {
    if (!RemoteConfig.instance) {
      RemoteConfig.instance = new RemoteConfig();
    }
    return RemoteConfig.instance;
  }

  public async fetch(): Promise<IRemoteConfigValues> {
    try {
      await remoteConfig().setDefaults(defaultValues as { [key: string]: any });
      await remoteConfig().fetch(300);
      await remoteConfig().fetchAndActivate();

      return {
        linkPrivacyPolicy: remoteConfig()
          .getValue("linkPrivacyPolicy")
          .asString(),
        linkTermsAndConditions: remoteConfig()
          .getValue("linkTermsAndConditions")
          .asString(),
        supportPhoneNumber: remoteConfig()
          .getValue("supportPhoneNumber")
          .asString(),
        supportEmail: remoteConfig().getValue("supportEmail").asString(),
        enableOnboarding: remoteConfig()
          .getValue("enableOnboarding")
          .asBoolean(),
      };
    } catch (error) {
      console.error("[REMOTE CONFIG ERROR]", error);
      return defaultValues;
    }
  }
}

export const RemoteConfigService = RemoteConfig.getInstance();
