/** @format */

import { Fonts } from "@/constants";
import { config, SCREEN_WIDTH } from "@/theme/_config";
import { Theme } from "@/types/theme/theme";
import { Platform } from "react-native";
import { ms, ScaledSheet } from "react-native-size-matters";

const getLoginStyle = (theme: Theme) =>
  ScaledSheet.create({
    container: {
      flex: 1,
      paddingTop: 0,
      justifyContent: "center",
      paddingHorizontal: "32@ms",
    },
    loginText: {
      textAlign: "center",
      marginVertical: "80@vs",
      // color: theme.colors.textPrimary,
      fontFamily: Fonts.RALEWAY_BOLD,
    },
    authText: {
      textAlign: "center",
      marginLeft: "10@ms",
      letterSpacing: -0.01,
      color: config.colors.black,
      fontFamily: Fonts.RALEWAY_SEMI_BOLD,
    },
    row: {
      flexDirection: "row",
      justifyContent: "center",
      alignItems: "center",
    },
    btnGroupView: {},
    logo: {
      alignSelf: "center",
    },
    logoFill: {
      color: theme.colors.textPrimary,
    },
    baseView: {
      minWidth: "202@ms",
      flexDirection: "row",
      justifyContent: "flex-start",
      alignItems: "center",
      marginleft: "18@ms",
    },
    modalCustomGroupStyle: {
      marginTop: ms(-8),
    },
    modalHeaderStyle: {
      width: SCREEN_WIDTH * 0.6,
      marginTop: ms(-8),
    },
    bottomText: {
      fontFamily: Fonts.RALEWAY_BOLD,
    },
    bottomTextView: {
      justifyContent: "center",
      marginTop: ms(12),
      flexDirection: "row",
    },
    loginLinkText: {
      color: theme.colors.textPrimaryYellow,
      fontFamily: Fonts.RALEWAY_BOLD,
    },
       backIcon: {
          position: "absolute",
          top:30,
          alignSelf:'flex-start',
          marginLeft: ms(10)
        },
  });

export default getLoginStyle;
