import { useEffect, useState } from 'react';
import {
	View,
	ActivityIndicator,
	Text,
	TouchableOpacity,
	ScrollView,
	Alert,
	Image,
} from 'react-native';
import i18next from 'i18next';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@tanstack/react-query';
import { ImagePickerResponse, launchCamera, launchImageLibrary } from 'react-native-image-picker';

import { Brand } from '@/components/molecules';
import { ImageVariant } from '@/components/atoms';
import { SafeScreen } from '@/components/template';
// import { fetchOne } from '@/services/users';

import { isImageSourcePropType } from '@/types/guards/image';

import { useTheme } from '@/theme';
// import SendImage from '@/theme/assets/images/send.png';
import TranslateImage from '@/theme/assets/images/translate.png';
// import ColorsWatchImage from '@/theme/assets/images/colorswatch.png';
import NotificationScheduler from '@/components/molecules/NotificationsExample';
import AreaChart from '@/components/atoms/AreaChart/AreaChart';

function Example() {
	const { t } = useTranslation(['example', 'welcome']);

	const {
		colors,
		variant,
		changeTheme,
		layout,
		gutters,
		fonts,
		components,
		backgrounds,
	} = useTheme();

	const [currentId, setCurrentId] = useState(-1);

	const { isSuccess, data, isFetching } = useQuery({
		queryKey: ['example', currentId],
		queryFn: () => {
			return fetchOne(currentId);
		},
		enabled: currentId >= 0,
	});

	useEffect(() => {
		if (isSuccess) {
			Alert.alert(t('example:welcome', data.name));
		}
	}, [isSuccess, data]);

	const onChangeTheme = () => {
		changeTheme(variant === 'default' ? 'dark' : 'default');
	};

	const onChangeLanguage = (lang: 'es' | 'en') => {
		void i18next.changeLanguage(lang);
	};

	if (
		// !isImageSourcePropType(SendImage) ||
		// !isImageSourcePropType(ColorsWatchImage) ||
		!isImageSourcePropType(TranslateImage)
	) {
		throw new Error('Image source is not valid');
	}

	const [image, setImage] = useState<String | null>(null);

	const handleImagePicker = async () => {
		const result = await launchImageLibrary({
			mediaType: 'photo',
			quality: 1,
		});
	};

	const handleCamera = async () => {
		const result = await launchCamera({
			mediaType: 'photo',
			quality: 1,
		});
		setImage(result?.assets?.[0]?.uri ?? null);
	};

	return (
		<SafeScreen>
			<ScrollView>
				<View
					style={[
						layout.justifyCenter,
						layout.itemsCenter,
						gutters.marginTop_80,
					]}
				>
					<View
						style={[layout.relative, backgrounds.darkGray, components.circle250]}
					/>

					<View style={[layout.absolute, gutters.paddingTop_80]}>
						{image ? (
							<Image
								source={{ uri: image as string }}
								style={{ resizeMode: 'contain', width: 300, height: 300 }}
							/>
						) : <Brand height={300} width={300} />}
					</View>
				</View>

				<View style={[gutters.paddingHorizontal_32, gutters.marginTop_40]}>
					<View style={[gutters.marginTop_40]}>
						<Text style={[fonts.size_40, fonts.mediumGray, fonts.bold]} onPress={handleCamera}>
							{t('welcome:open_camera')}
						</Text>
						<Text
							style={[
								fonts.mediumGray,
								fonts.bold,
								fonts.size_24,
								gutters.marginBottom_32,
							]}
						>
							{t('welcome:subtitle')}
						</Text>
						{/* <Text
							style={[fonts.size_16, fonts.gray200, gutters.marginBottom_40]}
						>
							{t('welcome:description')}
						</Text> */}
						<AreaChart />
						<NotificationScheduler />
					</View>

					<View
						style={[
							layout.row,
							layout.justifyBetween,
							layout.fullWidth,
							gutters.marginTop_16,
						]}
					>
						<TouchableOpacity
							testID="fetch-user-button"
							style={[components.buttonCircle, gutters.marginBottom_16]}
							onPress={() => setCurrentId(Math.ceil(Math.random() * 10 + 1))}
						>
							{isFetching ? (
								<ActivityIndicator />
							) : (
								<ImageVariant
									// source={SendImage}
									style={{ tintColor: colors.pink }}
								/>
							)}
						</TouchableOpacity>

						<TouchableOpacity
							testID="change-theme-button"
							style={[components.buttonCircle, gutters.marginBottom_16]}
							onPress={() => onChangeTheme()}
						>
							<ImageVariant
								// source={ColorsWatchImage}
								style={{ tintColor: colors.pink }}
							/>
						</TouchableOpacity>

						<TouchableOpacity
							testID="change-language-button"
							style={[components.buttonCircle, gutters.marginBottom_16]}
							onPress={() =>
								onChangeLanguage(i18next.language === 'es' ? 'en' : 'es')
							}
						>
							<ImageVariant
								source={TranslateImage}
								style={{ tintColor: colors.pink }}
							/>
						</TouchableOpacity>
					</View>
				</View>
			</ScrollView>
		</SafeScreen>
	);
}

export default Example;
