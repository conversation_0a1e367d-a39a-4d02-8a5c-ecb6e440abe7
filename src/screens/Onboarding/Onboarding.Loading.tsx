import React, { useCallback, useEffect, useState } from "react";
import { useFocusEffect, useNavigation } from "@react-navigation/native";

import { useMMKV } from "react-native-mmkv";
import Loading from "@/components/atoms/Loading/Loading";
import { useAppDispatch, useAppSelector } from "@/store";
import { selectOnboarding } from "@/store/slices/onboardingSlice";
import { selectAuth } from "@/store/slices/authSlice";

const LoadingScreen = () => {
  const navigation = useNavigation();
  const [selectedMode, setSelectedMode] = useState("dark");
  const { isLoggedIn } = useAppSelector(selectAuth);

  const { isPreferenceSelected } = useAppSelector(selectOnboarding);

  const { user, loading } = useAppSelector((state) => state.user);
  const mmkv = useMMKV();

  useFocusEffect(
    useCallback(() => {
      if (isLoggedIn) {
        if (!loading) {
          if (user?.onBoarded) {
            if (!isPreferenceSelected) {
              navigation.navigate("UserPreferences");
            }
          } else {
            navigation.navigate("UserProfile");

          }
        }
      }
    }, [loading, isLoggedIn, user, isPreferenceSelected, navigation])
  );

  useEffect(() => {
    const uiMode = mmkv.getString("theme");
    setSelectedMode(uiMode || "dark");
  }, [selectedMode]);

  return (
    <Loading />
  );
};

export default LoadingScreen;
