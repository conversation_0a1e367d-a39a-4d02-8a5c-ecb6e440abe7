import React, { useRef, useState, useCallback } from "react";
import { Pressable, View, ActivityIndicator } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { getOnboardingStyle } from "./Onboarding.style";
import { Button, Typography } from "@/components/atoms";
import {
  useNavigation,
  useFocusEffect,
  useIsFocused,
} from "@react-navigation/native";
import Video, { VideoRef } from "react-native-video";
import Icons from "@/theme/assets/images/svgs/icons";
import { useAppDispatch, useAppSelector } from "@/store";
import {
  completeOnboarding,
  selectOnboarding,
} from "@/store/slices/onboardingSlice";
import { analyticsEventType, useAnalytics } from "@/hooks/useAnalytics";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { ONBOARDING_VIDEOS } from "@/constants/onboarding";
import { useTheme } from "@/theme";

const MedicationGuide = () => {
  const navigation = useNavigation();
  const isFocused = useIsFocused();

  const videoRef = useRef<VideoRef>(null);
  const [isPaused, setPaused] = useState<boolean>(true);
  const [useDash, setUseDash] = useState<boolean>(false);
  const [attemptedFallback, setAttemptedFallback] = useState<boolean>(false);
  const [isVideoLoaded, setIsVideoLoaded] = useState<boolean>(false);
  const { setAnalyticsEvent } = useAnalytics();
  const styles: any = useDynamicStyles(getOnboardingStyle);
  const { colors } = useTheme();
  const { isTutorial } = useAppSelector(selectOnboarding);

  const video = ONBOARDING_VIDEOS.diet_tracker;


  function handleNextPress() {
    navigation.navigate("PheResultGuide" as never);
  }

  const dispatch = useAppDispatch();
  function handleSkipAll() {
    dispatch(completeOnboarding());

    if (isTutorial) {
      (navigation as any).navigate("MainTabs", {
        screen: "Home",
      });
      return;
    }

    setAnalyticsEvent(analyticsEventType.custom, {
      event: "onboarded",
      item_id: "onboarded",
      action: "User completes onboarding",
    });
  }

  async function handleVideoPlayer() {
    if (isPaused) {
      videoRef?.current?.resume();
      setPaused(false);
    } else {
      videoRef?.current?.pause();
      setPaused(true);
    }
  }

  useFocusEffect(
    useCallback(() => {
      // When screen comes into focus
      return () => {
        // When screen goes out of focus
        if (videoRef?.current) {
          videoRef.current.pause();
          setPaused(true);
        }
      };
    }, [])
  );

  return (
    <SafeAreaView>
      <View style={styles.flex_center}>
        <View style={styles.contentContainer}>
          <View style={styles.videoContainer}>
            <Pressable onPress={handleVideoPlayer}>
              {!isVideoLoaded && (
                <View pointerEvents="none" style={styles.abs0CenterZ3}>
                  <ActivityIndicator size="large" color={colors.primary} />
                </View>
              )}
              <Video
                resizeMode="contain"
                source={{ uri: (useDash ? video.playbackUrl.dash : video.playbackUrl.hls) }}
                paused={isPaused || !isFocused}
                muted={false}
                onLoad={() => {
                  setIsVideoLoaded(true);
                  videoRef?.current?.pause();
                }}
                onError={() => {
                  setIsVideoLoaded(false);
                  if (!attemptedFallback) {
                    setUseDash(true);
                    setAttemptedFallback(true);
                  }
                }}
                ref={videoRef}
                style={styles.backgroundVideo}
              />
              {isPaused && isVideoLoaded && (
                <View
                  style={{
                    position: "absolute",
                    alignSelf: "center",
                    top: "45%",
                    zIndex: 3,
                  }}
                >
                  <Icons.PlayIcon />
                </View>
              )}
            </Pressable>
          </View>
          <View style={styles.guidesContainer}>
            <View style={styles.titleContainer}>
              <Typography.H2>A Guide to</Typography.H2>
              <Typography.H1 style={styles.appTitle}>
                Dieting Tracking
              </Typography.H1>

              <View style={styles.textContainerMini}>
                <Typography.B2>
                  Monitor your Phe and protein intake, and plan meals with a
                  searchable database of 9,000+ foods.
                </Typography.B2>
              </View>

              <Button.Yellow
                style={styles.btnContainer}
                onPress={handleNextPress}
                activeOpacity={0.85}
              >
                <Typography.B2 style={styles.btnText}>Next</Typography.B2>
              </Button.Yellow>

              <Pressable style={styles.skipAll} onPress={handleSkipAll}>
                <Typography.B2 style={styles.primaryText}>
                  Skip All
                </Typography.B2>
              </Pressable>
            </View>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default MedicationGuide;
