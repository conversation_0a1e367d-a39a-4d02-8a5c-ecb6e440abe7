import { View } from "react-native";
import React, { useState } from "react";
import { useNavigation } from "@react-navigation/native";
import { Button, Typography } from "@/components/atoms";
import { getOnboardingStyle } from "./Onboarding.style";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { SafeScreen } from "@/components/template";
import { Header } from "@/components/molecules";
import { useTheme } from "@/theme";
import { RoleSelector } from "@/components/molecules/AnimatedSelector";
import { ROLE_OPTIONS } from "@/constants/onboardingSurvey";
import {
  fetchIntentionQuestionsById,
  setRole,
} from "@/store/slices/onboardingSlice";
import { useAppDispatch } from "@/store";
import { IRoleOptions } from "@/components/molecules/AnimatedSelector/RoleSelector";
import Loading from "@/components/atoms/Loading/Loading";
import { useAnalytics, analyticsEventType } from "@/hooks/useAnalytics";
import { AnalyticsFirebase } from "@/constants";

const RoleOptions = () => {
  const navigation = useNavigation();
  const { variant } = useTheme();
  const dispatch = useAppDispatch();
  const { setAnalyticsEvent } = useAnalytics();

  const styles: any = useDynamicStyles(getOnboardingStyle);
  const [selectedRole, setSelectedRole] = useState<IRoleOptions | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showError, setShowError] = useState(false);

  const getRoleAnalyticsValue = (roleId: number): string => {
    switch (roleId) {
      case 1:
        return "patient";
      case 2:
        return "carer";
      case 3:
        return "HCP";
      default:
        return "unknown";
    }
  };

  const handleContinueBtn = async () => {
    if (!selectedRole?.id) {
      setShowError(true);
      return;
    }

    setShowError(false);
    setIsLoading(true);

    try {
      const roleValue = getRoleAnalyticsValue(selectedRole.id);
      setAnalyticsEvent(
        analyticsEventType.custom,
        AnalyticsFirebase.CUSTOM_EVENTS.USER_IS(roleValue)
      );

      dispatch(setRole(selectedRole));

      const response = await dispatch(
        fetchIntentionQuestionsById(selectedRole?.id)
      ).unwrap();

      if (response) {
        navigation.navigate("IntentionOptions" as never);
      }
    } catch (error) {
    } finally {
      setIsLoading(false);
    }
  }

  const handleBackPress = () => {
    navigation.goBack();
  };

  const handleRoleSelect = (item: IRoleOptions) => {
    setSelectedRole(item);
    setShowError(false); 
  };
  return (
    <SafeScreen containerStyle={styles.safeAreaView}>
      {isLoading ? (
        <Loading />
      ) : (
        <>
          <View style={styles.container}>
            <Header
              title=""
              onBackPress={handleBackPress}
              isDark={variant === "dark"}
            />

            <Typography.H2 style={styles.intentionOptionsTitle}>
              {"I am a"}
            </Typography.H2>

            {ROLE_OPTIONS.map((item) => (
              <View style={styles.optionsView}>
                <RoleSelector
                  item={item}
                  isSelected={selectedRole?.id === item.id}
                  onToggle={handleRoleSelect}
                />
              </View>
            ))}
          </View>

          {showError && (
            <Typography.B1 style={styles.errorView}>
              {"Please choose from the above options"}
            </Typography.B1>
          )}

          <Button.Yellow
            style={styles.intentionOptionsBtnContainer}
            onPress={handleContinueBtn}
          >
            <Typography.B2 style={styles.intentionOptionBtnText}>
              Continue
            </Typography.B2>
          </Button.Yellow>
        </>
      )}
    </SafeScreen>
  );
};

export default RoleOptions;
