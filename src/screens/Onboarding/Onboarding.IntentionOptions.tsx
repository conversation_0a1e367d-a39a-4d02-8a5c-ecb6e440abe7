import { FlatList, View } from "react-native";
import React, { useState } from "react";
import { useNavigation } from "@react-navigation/native";
import { Button, Typography } from "@/components/atoms";
import { getOnboardingStyle } from "./Onboarding.style";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { SafeScreen } from "@/components/template";
import { Header } from "@/components/molecules";
import { useTheme } from "@/theme";
import { IntentionSelector } from "@/components/molecules/AnimatedSelector";
import { useSelector } from "react-redux";
import {
  selecRole,
  selectIntentionQuestionsById,
  setSurveyResponses,
} from "@/store/slices/onboardingSlice";
import { IOptions } from "@/components/molecules/AnimatedSelector/IntentionSelector";
import { useAppDispatch } from "@/store";

const IntentionOptions = () => {
  const navigation = useNavigation();
  const { variant } = useTheme();
  const styles: any = useDynamicStyles(getOnboardingStyle);
  const [selectedIntentions, setSelectedIntentions] = useState<IOptions[]>([]);
  const { id } = useSelector(selecRole);
  const surveyQuestions = useSelector(selectIntentionQuestionsById);
  const dispatch = useAppDispatch(); 

  const handleBackPress = () => {
    navigation.goBack();
  };

  const renderTitle = () =>
    id === 1 ? "I am interested in" : "My patients are interested in";

  const onSelectedItem = (item: IOptions) => {
    setSelectedIntentions((prevSelected) => {
      const isAlreadySelected = prevSelected.some((i) => i.id === item.id);
      return isAlreadySelected
        ? prevSelected.filter((i) => i.id !== item.id)
        : [...prevSelected, item];
    });
  };

  const buildSurveyPayload = () =>
    surveyQuestions.map((item) => ({
      question: item.question,
      response: selectedIntentions.some((selected) => selected.id === item.id)
        ? "1"
        : "0",
    }));

  const handleContinueBtn = () => {
    const responses = buildSurveyPayload();
    dispatch(setSurveyResponses(responses));
    navigation.navigate("CreateAccount" as never);
  };
  return (
    <SafeScreen containerStyle={styles.safeAreaView}>
      <View style={styles.container}>
        <Header
          title=""
          onBackPress={handleBackPress}
          isDark={variant === "dark"}
        />

        <Typography.H2 style={styles.intentionOptionsTitle}>
          {renderTitle()}
        </Typography.H2>
        <FlatList
          data={surveyQuestions}
          keyExtractor={(item) => String(item.id)}
          renderItem={({ item }) => (
            <IntentionSelector
              item={item}
              selectedItems={selectedIntentions}
              onToggle={onSelectedItem}
            />
          )}
          contentContainerStyle={styles.intentionOptionsFlatListView}
          scrollEnabled={false}
        />
      </View>

      <Button.Yellow
        style={styles.intentionOptionsBtnContainer}
        onPress={handleContinueBtn}
      >
        <Typography.B2 style={styles.intentionOptionBtnText}>
          Continue
        </Typography.B2>
      </Button.Yellow>
    </SafeScreen>
  );
};

export default IntentionOptions;
