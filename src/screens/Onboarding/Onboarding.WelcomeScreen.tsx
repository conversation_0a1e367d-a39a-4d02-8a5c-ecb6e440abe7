import { View } from "react-native";
import React, { useEffect, useState } from "react";
import { useNavigation } from "@react-navigation/native";
import { SafeAreaView } from "react-native-safe-area-context";

import Icons from "@/theme/assets/images/svgs/icons";
import { Button, Typography } from "@/components/atoms";
import { useMMKV } from "react-native-mmkv";
import { getOnboardingStyle } from "./Onboarding.style";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";

const WelcomeScreen = () => {
  const navigation = useNavigation();
  const [selectedMode, setSelectedMode] = useState("dark");

  const mmkv = useMMKV();
  const styles: any = useDynamicStyles(getOnboardingStyle);

  useEffect(() => {
    const uiMode = mmkv.getString("theme");
    setSelectedMode(uiMode || "dark");
  }, [selectedMode]);

  function handleGetStarted() {
    navigation.navigate("UserConfiguration");
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.welcomeContainer}>
        {selectedMode === "dark" ? (
          <Icons.Logo style={styles.logo} />
        ) : (
          <Icons.LogoDark style={styles.logo} />
        )}

        <View style={styles.titleContainer}>
          <Typography.H2>Welcome</Typography.H2>
          <View>
            <Typography.B2 style={styles.textContainer}>
              At Cycle Vita, we understand that as a patient with PKU, or their
              caregiver, it can be challenging to access your medication, while
              keeping on top of everything else, like formula and diet. The
              Cycle Vita PKU app is designed with your needs in mind to offer
              you flexible and timely support when you need it.
            </Typography.B2>
          </View>
        </View>

        <Button.Yellow
          style={styles.btnContainer}
          onPress={handleGetStarted}
          activeOpacity={0.85}
        >
          <Typography.B2 style={styles.btnText}>Get Started</Typography.B2>
        </Button.Yellow>
      </View>
    </SafeAreaView>
  );
};

export default WelcomeScreen;
