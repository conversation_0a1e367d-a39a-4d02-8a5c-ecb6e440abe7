import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  View,
} from "react-native";
import React, { useEffect } from "react";
import { useNavigation } from "@react-navigation/native";
import Icons from "@/theme/assets/images/svgs/icons";
import { Button, Typography } from "@/components/atoms";
import { getOnboardingStyle } from "./Onboarding.style";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { SCREEN_WIDTH } from "@/theme/_config";
import { useTheme } from "@/theme";
import { useAnalytics, analyticsEventType } from "@/hooks/useAnalytics";
import { AnalyticsFirebase } from "@/constants";
import {
  resetOnboarding,
  selectReOnBoarded,
  setReOnboarded,
} from "@/store/slices/onboardingSlice";
import { useDispatch } from "react-redux";
import { logout } from "@/store/slices/authSlice";
import { useAuth0 } from "react-native-auth0";
import { AppDispatch, useAppSelector } from "@/store";

const DisclaimerScreen = ({ route }) => {
  const navigation = useNavigation();
  const styles: any = useDynamicStyles(getOnboardingStyle);
  const { variant } = useTheme();
  const { setAnalyticsEvent } = useAnalytics();
  const dispatch = useDispatch<AppDispatch>();
  const { clearCredentials } = useAuth0();
  const reOnboarded = useAppSelector(selectReOnBoarded);

  const handleGetStarted = () => {
    setAnalyticsEvent(
      analyticsEventType.custom,
      AnalyticsFirebase.CUSTOM_EVENTS.DISCLAIMER_UNDERSTOOD(
        AnalyticsFirebase.actionBool.TRUE
      )
    );
    navigation.navigate("RoleOptions");
  };

  useEffect(() => {
    const handleReOnboard = async () => {
      if (reOnboarded) {
        await clearCredentials();
        dispatch(logout());
      }
    };

    handleReOnboard();
  }, [reOnboarded]);

  const handleDisclaimerPress = async () => {
    if (route?.params?.isSignup) {
      if (navigation.canGoBack()) {
        navigation.goBack();
      } else {
        navigation.navigate("InitialScreen");
      }
    } else {
      await clearCredentials();
      dispatch(setReOnboarded(false));
      dispatch(resetOnboarding());
      dispatch(logout());

      navigation.navigate("InitialScreen");
    }
  };

  useEffect(() => {
    const onBackPress = () => {
      handleDisclaimerPress();
      return true;
    };

    const subscription = BackHandler.addEventListener(
      "hardwareBackPress",
      onBackPress
    );

    return () => subscription.remove();
  }, [handleDisclaimerPress]);

  return (
    <View style={styles.disclamerContainer}>
      <StatusBar
        backgroundColor="white"
        barStyle={variant === "dark" ? "light-content" : "dark-content"}
        translucent={false}
      />

      <ScrollView
        contentContainerStyle={styles.disclaimerScrollContainer}
        showsVerticalScrollIndicator={false}
        bounces={false}
      >
        <Icons.TopBackground width={"100%"} />
        <Pressable onPress={handleDisclaimerPress} style={styles.backIcon}>
          <Icons.BackArrow color={"black"} />
        </Pressable>
        <Icons.LogoDark style={styles.disclaimerlogo} />
        <View style={styles.disclaimerTitleContainer}>
          <Typography.H1 style={styles.disclaimerTitle}>
            Disclaimer
          </Typography.H1>
          <Typography.B4 style={styles.disclaimerSubTitle}>
            The information provided through this application, including any
            medical or health-related content, is intended for informational and
            educational purposes only. It is not intended as a substitute for
            professional medical advice, diagnosis, or treatment. Always seek
            the advice of a qualified healthcare provider for any questions you
            may have regarding a medical condition. Never disregard professional
            medical advice or delay in seeking it because of something you have
            read or accessed through this application.
          </Typography.B4>
        </View>

        <View style={{ width: SCREEN_WIDTH, position: "relative" }}>
          <Icons.BottomBackground
            width={SCREEN_WIDTH}
            height={SCREEN_WIDTH * 0.99}
            preserveAspectRatio="xMidYMax meet"
          />

          <View style={styles.disclaimerBottomView}>
            <Button.Yellow
              style={styles.disclaimerBtnContainer}
              onPress={handleGetStarted}
            >
              <Typography.B2 style={styles.btnText}>I understand</Typography.B2>
            </Button.Yellow>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default DisclaimerScreen;
