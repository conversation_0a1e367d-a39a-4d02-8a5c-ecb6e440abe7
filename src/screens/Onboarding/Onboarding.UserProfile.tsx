import React, { useEffect, useState } from "react";
import { ScrollView, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import RNFS from "react-native-fs";
import { useNavigation } from "@react-navigation/native";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { yupResolver } from "@hookform/resolvers/yup";
import {
  updateSimplifiedDietThunk,
  updateUser,
  updateUserProfileByPatchThunk,
  userOnboardingThunk,
} from "@/store/slices/userSlice";

import * as ImagePicker from "react-native-image-picker";

import Common from "@/theme/common.style";
import {
  getPheAllowanceUnit,
  setPheAllowanceValue,
  uploadFileToAzure,
} from "@/utils/helpers";
import Loading from "@/components/atoms/Loading/Loading";
import {
  createPheAllowance,
  fetchDailyConsumedPheAllowance,
  fetchPheAllowances,
} from "@/store/slices/pheAllowanceSlice";
import { PheAllowance } from "@/types/schemas/pheAllowance";
import { selectDietTracker } from "@/store/slices/dietTrackerSlice";
import { UserOnboarding } from "@/types/schemas/user";
import { Button, Typography } from "@/components/atoms";
import { RootState, useAppDispatch, useAppSelector } from "@/store";
import { getOnboardingStyle } from "./Onboarding.style";
import { getPreSignedUrl } from "@/services/api/userAPI";
import InputField from "@/components/molecules/Field/InputField";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import ProfileHeader from "@/components/molecules/ProfileHeader/ProfileHeader";
import { NEW_ONBOARDING_USER_VALIDATION_SCHEMA } from "@/shared/input-validations/onboarding-user-validation";
import { useAuth0 } from "react-native-auth0";
import {
  selectConsumptionUnit,
  setProfileTimeZone,
  setIsSimplifiedDiet as setSimplifiedDiet,
} from "@/store/slices/settingsSlice";
import { useTheme } from "@/theme";
import { useSelector } from "react-redux";
import { postSurveyResponses } from "@/store/slices/onboardingSlice";
import { selecRole } from "@/store/slices/onboardingSlice";
import { TimezoneService } from "@/utils/timezoneService";

const UserProfile = () => {
  const navigation = useNavigation();
  const { t } = useTranslation(["onBoardingUser"]);
  const { user } = useAppSelector((state) => state.user);
  const { user: authUser } = useAuth0();
  const dispatch = useAppDispatch();
  const consumptionType = useAppSelector(selectConsumptionUnit);
  const [avatar, setAvatar] = useState({});
  const [preSignedUrl, setPreSignUrl] = useState("");
  const [profileImage, setProfileImage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [profileName, setProfileName] = useState("");
  const { selectedDietDate } = useAppSelector(selectDietTracker);
  const { colors } = useTheme();
  const { accountData } = useAppSelector((state) => state.onboarding);
  const { isSimplifiedDiet, ...filteredAccountData } = accountData || {};
  const [focusedField, setFocusedField] = useState<{ [key: string]: boolean }>(
    {}
  );

  const { id } = useSelector(selecRole);
  const surveyResponses = useSelector(
    (state: RootState) => state.onboarding.surveyResponses
  );
  const styles: any = useDynamicStyles(getOnboardingStyle);

  const { control, handleSubmit, setValue, trigger } = useForm<UserOnboarding>({
    mode: "all",
    resolver: yupResolver(NEW_ONBOARDING_USER_VALIDATION_SCHEMA),
    context: { consumptionType },
    defaultValues: {
      name: user?.name || "",
      email: user?.email || "",
      phoneNumber: user?.phoneNumber || "",
      // isCareTaker: false,
    },
  });

console.warn("consumptionType 1 ===|>",consumptionType)

  async function handleContinue(data: UserOnboarding) {
    const { name, pheAllowance, ...onboardingData } = data as UserOnboarding;
    const onboardingRequest = {
      ...onboardingData,
      ...filteredAccountData,
      roleId: id,
      name: profileName || name,
    };
    try {
      setIsLoading(true);
      const result = await dispatch(userOnboardingThunk(onboardingRequest));

      if (userOnboardingThunk.fulfilled.match(result)) {
        try {
          const typedAvatar = avatar as { uri?: string; fileName?: string };

        
            
          const userObject = {
            ...user,
            ...onboardingRequest,
            pheAllowance: setPheAllowanceValue(
              pheAllowance?.toString(),
              consumptionType
            ),
            profilePictureUrl: typedAvatar.fileName,
            timeZoneId: TimezoneService.getDeviceTimeZone()
          };

          await uploadProfileImage();
          dispatch(updateUser(userObject));
          dispatch(setSimplifiedDiet(isSimplifiedDiet));
          dispatch(
            updateSimplifiedDietThunk({ isSimplifiedDiet: isSimplifiedDiet, })
          );
          // await updatePheAllowance(onboardingRequest?.pheAllowance?.toString());
          await dispatch(postSurveyResponses({ survey: surveyResponses, roleId: id }));
          dispatch(setProfileTimeZone(Intl.DateTimeFormat().resolvedOptions().timeZone));
          (navigation.navigate as any)("UserPreferences");
        } catch (error) {
          console.error("Error during post-onboarding steps:", error);
        }
      } else if (userOnboardingThunk.rejected.match(result)) {
        console.error("Unable to onboard user", result);
      }
    } catch (error) {
      console.error("Error during onboarding:", error);
    } finally {
      setIsLoading(false);
    }
  }

  const updatePheAllowance = async (dailyPheAllowance: string) => {
    const body: PheAllowance = {
      pheAllowanceAmount: setPheAllowanceValue(
        dailyPheAllowance,
        consumptionType
      ),
      pheAllowanceUnit: getPheAllowanceUnit(consumptionType),
    };

    dispatch(createPheAllowance(body))
      .unwrap()
      .then(() => {
        const formattedDate = new Date(selectedDietDate).toISOString(); // Extract the date part in YYYY-MM-DD format

        dispatch(fetchDailyConsumedPheAllowance(formattedDate));
        dispatch(fetchPheAllowances());
      })
      .catch((error) => {
        // console.error("createPheAllowance error: ", error);
      });
  };

  async function uploadProfileImage() {
    try {
      const typedAvatar = avatar as { uri?: string; fileName?: string };
      if (typedAvatar?.uri && preSignedUrl) {
        await uploadFileToAzure(typedAvatar, preSignedUrl)
          .then(() => {
            dispatch(
              updateUserProfileByPatchThunk({
                url: typedAvatar?.fileName || "",
              })
            );
          })
          .catch((error) => {
            console.error("Error uploading image:", error);
          });
      }
      return true;
    } catch (error) {
      console.error("Error in uploadProfileImage:", error);
      return false;
    }
  }

  useEffect(() => {
    setProfileName(user?.name ?? "");

    const fields: [keyof UserOnboarding, any][] = [
      ["name", user?.name || ""],
      ["email", user?.email || ""],
      ["phoneNumber", user?.phoneNumber || ""],
    ];

    fields.forEach(([name, value]) => {
      setValue(name, value);
    });
  }, [JSON.stringify(user), setValue]);

  useEffect(() => {
    async function signedImage() {
      if (authUser?.picture) {
        const randomNum = Math.floor(Math.random() * 100000);
        const fileName = `downloadedImage_${randomNum}.jpg`;
        await downloadImage(fileName);
      }
    }
    signedImage();
  }, [user]);

  const downloadImage = async (fileName: string) => {
    try {
      // Define path to save image
      const path = `${RNFS.DocumentDirectoryPath}/${fileName}`;

      // Download image
      const response = await RNFS.downloadFile({
        fromUrl: authUser?.picture,
        toFile: path,
      }).promise;

      if (response.statusCode === 200) {
        setProfileImage(`file://${path}` || "");
        setAvatar({
          uri: `file://${path}` || "",
          fileName: fileName,
          type: "image/jpg",
        });
        getPreSignedUrl(fileName || "")
          .then((response) => {
            setPreSignUrl(response || "");
          })
          .catch((error) => {
            console.error("Error getting pre-signed URL:", error);
          });
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleImageEdit = () => {
    ImagePicker.launchImageLibrary({ mediaType: "photo" }, (response) => {
      if (response.didCancel) {
        return;
      }

      if (response.errorCode) {
        console.error("ImagePicker Error: ", response.errorMessage);
        return;
      }

      const selectedImage = response?.assets?.[0];
      if (selectedImage?.uri) {
        // Handle the selected image URI
        setProfileImage(selectedImage.uri || "");
        setAvatar(selectedImage);

        try {
          getPreSignedUrl(selectedImage?.fileName || "DefaultName.jpg")
            .then((response) => {
              setPreSignUrl(response || "");
            })
            .catch((error) => {
              console.error("Error getting pre-signed URL:", error);
            });
        } catch (error) {
          console.error("Error in handleImageEdit:", error);
        }
      }
    });
  };
  return (
    <SafeAreaView style={styles.container}>
      {isLoading ? (
        <Loading />
      ) : (
        <ScrollView
          contentContainerStyle={Common.flexOne}
          showsVerticalScrollIndicator={false}
        >
          <View style={Common.justifySpaceFlex}>
            <View>
              <View style={[Common.rowJustifyCenter, { marginBottom: 10 }]}>
                <Typography.H3 style={Common.textBold}>
                  {'Your Details'}
                </Typography.H3>
              </View>

              <ProfileHeader
                name={profileName || ""}
                imageUrl={profileImage}
                isEditing={true}
                onEditPress={handleImageEdit}
              />

              <InputField
                name="name"
                control={control}
                value={profileName}
                onChangeText={(text: string) => {
                  setProfileName(text || "");
                  setValue("name", text || "");
                }}
                label={t("name")}
                placeholder={t("enterYourName")}
                onFocus={() =>
                  setFocusedField((prev) => ({ ...prev, name: true }))
                }
                onBlur={() =>
                  setFocusedField((prev) => ({ ...prev, name: false }))
                }
                containerStyle={{
                  borderColor: focusedField.name
                    ? colors?.yellow
                    : colors?.mediumGray,
                }}
              />

              <InputField
                name="email"
                keyboardType="email-address"
                control={control}
                label={t("email")}
                editable={!user?.email}
                placeholder={t("enterYourEmail")}
              />

              <InputField
                name="phoneNumber"
                control={control}
                keyboardType="phone-pad"
                label={t("phoneNumber")}
                editable={!user?.phoneNumber}
                placeholder={t("enterYourPhone")}
                onFocus={() =>
                  setFocusedField((prev) => ({ ...prev, phoneNumber: true }))
                }
                onBlur={() =>
                  setFocusedField((prev) => ({ ...prev, phoneNumber: false }))
                }
                containerStyle={{
                  borderColor: focusedField.phoneNumber
                    ? colors?.yellow
                    : colors?.mediumGray,
                }}
              />
            </View>
            <View>
              <Button.Yellow
                style={styles.createAccountBtnConatiner}
                onPress={handleSubmit(handleContinue)}
                activeOpacity={0.85}
              >
                <Typography.B2 style={styles.btnText}>Continue</Typography.B2>
              </Button.Yellow>
            </View>
          </View>
        </ScrollView>
      )}
    </SafeAreaView>
  );
};

export default UserProfile;
