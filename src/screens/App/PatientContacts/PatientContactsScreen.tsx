import React, { useEffect, useState } from "react";
import { View, TouchableOpacity, ScrollView, Linking } from "react-native";
import patientContactStyle from "./PatientContactsScreen.style";
import { Typography, <PERSON><PERSON> } from "@/components/atoms";
import Icons from "@/theme/assets/images/svgs/icons";
import Header from "@/components/molecules/Header/Header";
import { SafeScreen } from "@/components/template";
import Common from "@/theme/common.style";
import PatientContactsBottomSheet from "@/components/molecules/PatientContactsBottomSheet/PatientContactsBottomSheet";
import { analyticsEventType, useAnalytics } from "@/hooks/useAnalytics";
import { useTheme } from "@/theme";
import { useAppDispatch, useAppSelector } from "@/store";
import {
  editContact,
  fetchContacts,
  removeContact,
  selectContactLoading,
  selectContacts,
  submitContact,
} from "@/store/slices/patientContactSlice";
import { PatientContact } from "@/types/schemas/patientContact";
import Loading from "@/components/atoms/Loading/Loading";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";

const PatientContactsScreen: React.FC = () => {
  const [isBottomSheetVisible, setBottomSheetVisible] = useState(false);
  const [mode, setMode] = useState<"add" | "edit">("add");
  const [selectedContact, setSelectedContact] = useState<PatientContact | null>(
    null
  );
  const { setAnalyticsEvent } = useAnalytics();
  const { colors, variant } = useTheme();
  const styles: any = useDynamicStyles(patientContactStyle);

  const dispatch = useAppDispatch();
  const contacts = useAppSelector(selectContacts);
  const loading = useAppSelector(selectContactLoading);

  const [confirmationModal, setConfirmationModal] = useState(false);

  useEffect(() => {
    dispatch(fetchContacts());
  }, [dispatch]);

  const handleAddContact = () => {
    setMode("add");
    setSelectedContact(null);
    setBottomSheetVisible(true);
  };

  const handleEditContact = (contact: PatientContact) => {
    setMode("edit");
    setSelectedContact(contact);
    setBottomSheetVisible(true);
  };

  const handleSaveContact = async (data: PatientContact) => {
    try {
      setBottomSheetVisible(false);

      if (mode === "add") {
        await dispatch(
          submitContact({
            name: data.name,
            role: data.role,
            emailAddress: data.emailAddress,
            phoneNumber: data.phoneNumber,
            address: data.address || "",
          })
        ).unwrap();
      } else if (mode === "edit" && selectedContact) {
        await dispatch(
          editContact({
            id: data.contactId,
            name: data.name,
            role: data.role,
            emailAddress: data.emailAddress,
            phoneNumber: data.phoneNumber,
            address: data.address || "",
          })
        ).unwrap();
      }

      dispatch(fetchContacts());

      setAnalyticsEvent(analyticsEventType.custom, {
        event: "support_contact_added",
        item_id: "support_contact_added",
        action: "User added or updated contact",
      });
    } catch (error) {
      console.error("Failed to save contact:", error);
    }
  };

  const handleDeleteContact = () => {
    setConfirmationModal(true);
  };

  const handleCall = (phone: string) => {
    setAnalyticsEvent(analyticsEventType.custom, {
      event: "support_contact_phone_tapped",
      item_id: "support_contact_phone_tapped",
      action: "User initiated contact phone call",
    });
    Linking.openURL(`tel:${phone}`);
  };

  const handleEmail = async (email: string) => {
    try {
      setAnalyticsEvent(analyticsEventType.custom, {
        event: "support_contact_email_tapped",
        item_id: "support_contact_email_tapped",
        action: "User initiated contact email",
      });
      await Linking.openURL(`mailto:${email}`);
    } catch (error) {
      console.error("Failed to open email link:", error);
    }
  };

  return (
    <SafeScreen>
      <View style={styles.container}>
        <Header showHamburgerMenu title="Care Team" isDark={variant === "dark"} />

        <View style={styles.sectionHeader}>
          <Typography.B1 style={[Common.textBold, styles.contactsTitle]}>
            Contacts List
          </Typography.B1>
          <Button.Outline onPress={handleAddContact} style={styles.addButton}>
            <View style={styles.iconTextWrapper}>
              <Icons.Plus color={colors.textPrimary} />
              <Typography.B2 style={[Common.textBold, styles.textButton]}>
                Add
              </Typography.B2>
            </View>
          </Button.Outline>
        </View>

        {contacts.length === 0 ? (
          <View style={styles.emptyStateContainer}>
            <Typography.B2>No contacts yet</Typography.B2>
          </View>
        ) : (
          <ScrollView contentContainerStyle={styles.contactsList}>
            {contacts.map((contact) => (
              <TouchableOpacity
                key={contact.contactId}
                onPress={() => handleEditContact(contact)}
              >
                <View style={styles.contactItem}>
                  <View>
                    <Typography.B1 style={styles.contactName}>
                      {contact.name}
                    </Typography.B1>
                    <Typography.B3 style={styles.contactRole}>
                      {contact.role}
                    </Typography.B3>
                  </View>
                  <View style={styles.contactActions}>
                    {contact.phoneNumber && (
                      <TouchableOpacity
                        style={styles.actionButton}
                        onPress={() => handleCall(contact.phoneNumber)}
                      >
                        <Icons.Phone
                          width={15}
                          height={15}
                          color={colors.yellowBlue}
                        />
                      </TouchableOpacity>
                    )}

                    {contact.emailAddress && (
                      <TouchableOpacity
                        style={[
                          styles.actionButton,
                          !contact.emailAddress && { opacity: 0.5 },
                        ]}
                        onPress={() =>
                          contact.emailAddress &&
                          handleEmail(contact.emailAddress)
                        }
                      >
                        <Icons.Mail
                          width={15}
                          height={15}
                          color={colors.yellowBlue}
                        />
                      </TouchableOpacity>
                    )}
                  </View>
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
        )}
      </View>

      <PatientContactsBottomSheet
        mode={mode}
        onSave={handleSaveContact}
        isVisible={isBottomSheetVisible}
        onClose={() => setBottomSheetVisible(false)}
        onDelete={handleDeleteContact}
        selectedContact={selectedContact || undefined}
        showDeleteModal={confirmationModal}
        onToggleDeleteModal={() => setConfirmationModal((prev) => !prev)}
        onConfirmDelete={async () => {
          if (selectedContact?.contactId != null) {
            try {
              await dispatch(removeContact(selectedContact.contactId)).unwrap();
              dispatch(fetchContacts());
              setBottomSheetVisible(false);
            } catch (error) {
              console.error("Failed to delete contact:", error);
            }
          }
          setConfirmationModal(false);
        }}
      />

      {loading && <Loading />}
    </SafeScreen>
  );
};

export default PatientContactsScreen;
