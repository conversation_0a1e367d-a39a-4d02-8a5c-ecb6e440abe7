import { ms, ScaledSheet } from "react-native-size-matters";
import { config } from "@/theme/_config";

import { Fonts } from "@/constants";
import { Theme } from "@/types/theme/theme";
import { RFValue } from "react-native-responsive-fontsize";

const patientContactStyle = (theme: Theme) =>
  ScaledSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: ms(20),
    },
    sectionHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "flex-end",
    },
    contactsTitle: {},
    addButton: {
      borderRadius: ms(8),
      width: ms(80),
      alignItems: "center",
      justifyContent: "center",
    },
    iconTextWrapper: {
      flexDirection: "row",
      alignItems: "center",
      gap: ms(5), // Space between the icon and text
    },
    textButton: {
      marginLeft: ms(10),
    },
    contactsList: {
      marginTop: ms(10),
      backgroundColor: theme.colors.contactCard,
      borderRadius: ms(16),
      padding: ms(15),
    },
    contactItem: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      backgroundColor: theme.colors.contactItem,
      marginVertical: ms(5),
      borderRadius: ms(10),
      padding: ms(10),
    },
    contactActions: {
      flexDirection: "row",
    },
    actionButton: {
      marginHorizontal: ms(5),
      backgroundColor: theme.colors.contactAction,
      padding: ms(10),
      borderRadius: ms(50),
      alignItems: "center",
      justifyContent: "center",
    },
    emptyStateContainer: {
      flex: 1,
      alignItems: "center",
      justifyContent: "center",
    },
    iconColor: {
      color: theme.colors.yellowBlue,
    },
  });

export default patientContactStyle;
