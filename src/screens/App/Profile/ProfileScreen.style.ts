import { Fonts } from "@/constants";
import { SCREEN_HEIGHT } from "@/theme/_config";
import { Theme } from "@/types/theme/theme";
import { StyleSheet } from "react-native";
import { ms } from "react-native-size-matters";

const getProfileStyle = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: ms(16),
    },
    scrollContent: {
      paddingBottom: ms(80), // Prevent content from overlapping with the sticky button
      paddingTop: ms(20),
    },
    content: {
      marginTop: ms(20),
    },
    settingsContainer: {
      marginBottom: ms(10),
    },
    accordionContainer: {
      flexDirection: "row",
      justifyContent: "space-around",
      backgroundColor: theme.colors.tile_bg,
      borderBottomRightRadius: ms(12),
      borderBottomLeftRadius: ms(12),
      paddingVertical: ms(12),
      marginTop: ms(-10),
      padding: ms(15),
    },
    modeButton: {
      borderRadius: ms(8),
      borderWidth: ms(1),
      alignItems: "center",
      justifyContent: "center",
      width: ms(90),
      borderColor: theme.colors.textPrimary,
    },
    selectedButton: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    editContainer: {
      marginTop: ms(20),
    },
    bottomButtonContainer: {
      marginTop: SCREEN_HEIGHT > 640 ? ms(60) : ms(52),
      paddingHorizontal: ms(16),
    },
    buttonOutlineText: {
      fontFamily: Fonts.RALEWAY_BOLD,
      // fontSize: ms(13),
      lineHeight: ms(15.5),
    },
    deleteAccountButtonContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      backgroundColor: theme.colors.tile_bg,
      borderRadius: ms(12),
    },
    selectedButtonText: {
      color: theme.colors.white,
    },
    deleteButton: {
      borderWidth: 0,
      justifyContent: "flex-end",
      width: ms(90),
      paddingBottom: ms(10),
    },
    deleteButtonText: {
      fontFamily: Fonts.RALEWAY_BOLD,
      color: theme.colors.delete,
      borderWidth: 0,
    },
    mainBtn: {
      paddingVertical: ms(0),
      justifyContent: "center",
    },
    stickyButtonContainer: {
      top: ms(50),
      left: 0,
      right: 0,
      paddingHorizontal: ms(16),
      zIndex: 10,
    },
    inputView: {
      marginBottom: ms(10),
    },
    foodDetailsContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      borderWidth: 1,
      borderColor: "#B0B0B0",
      borderRadius: ms(12),
      paddingVertical: ms(10),
      paddingHorizontal: ms(20),
      marginTop: ms(8),
    },
    foodDetailsText: {
      fontSize: ms(11),
      flexShrink: 1,
      maxWidth: '100%',
      overflow: 'hidden',
      marginRight: ms(8),
    },
    foodDetailsIcon: {
      alignSelf: 'center',
    },
    toastMessage: {
      backgroundColor: theme.colors.gray,
      borderRadius: ms(5),
      paddingVertical: ms(6),
      paddingHorizontal: ms(18),
      shadowColor: theme.colors.black,
      shadowOpacity: 0.15,
      shadowRadius: 6,
      elevation: 3,
    },
    dropdownButton: {
      backgroundColor: theme.colors.item_secondary_bg,
      borderRadius: ms(10),
      paddingVertical: ms(10),
      paddingHorizontal: ms(12),
      minHeight: ms(40),
    },
    dropdownButtonOpen: {
      borderBottomLeftRadius: 0,
      borderBottomRightRadius: 0,
    },
    dropdownText: {
      color: "#fff",
      fontSize: ms(14),
    },
    dropdownStyle: {
      backgroundColor: theme.colors.item_secondary_bg,
    },
    textYellow: {
      color: theme.colors.yellow,
      fontFamily: Fonts.RALEWAY_BOLD,
      fontSize: ms(14),
    },
    timeZoneSheetContainer: {
      flex: 1,
      padding: ms(24),
    },
    timeZoneSheetTitle: {
      textAlign: "center",
      marginBottom: ms(24),
      fontFamily: Fonts.RALEWAY_BOLD,
    },
    timeZoneSection: {
      marginBottom: ms(16),
    },
    timeZoneDropdownWrapper: {
      marginTop: ms(8),
    },
    timeZoneUpdateButton: {
      marginTop: ms(32),
    },
  });

export default getProfileStyle;
