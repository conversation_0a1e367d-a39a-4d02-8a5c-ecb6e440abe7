import { Fonts } from "@/constants";
import { Theme } from "@/types/theme/theme";
import { ms, ScaledSheet } from "react-native-size-matters";

const getDietTrackStyles = (theme: Theme) => ScaledSheet.create({
  container: {
    flex: 1,
    paddingTop: 0,
    paddingHorizontal: "10@ms",
  },
  headerView: {
    paddingHorizontal: "16@ms",
  },
  headerText: {
    fontSize: ms(22),
  },
  header2Text: {
    fontSize: ms(17),
    marginBottom: ms(10),
  },
  header3Text: {
    fontSize: ms(12),
    marginLeft: ms(10),
  },
  bodyText: {
    fontSize: ms(12),
  },
  row: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  row2: {
    flexDirection: "row",
    alignItems: "center",
  },
  daysRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginTop: ms(10),
  },
  logo: {
    alignSelf: "center",
  },
  scrollViewContent: {
    rowGap: "16@ms",
    paddingTop: "8@vs",
    paddingBottom: "20@vs",
  },
  weeklyBox: {
    gap: "16@ms",
    width: "100%",
    height: "110@ms",
    backgroundColor: theme.colors.tile_bg,
    borderRadius: "16@ms",
  },
  pheAllowanceView: {
    paddingVertical: "14@ms",
    paddingHorizontal: "16@ms",
    width: "100%",
    height: "115@ms",
    backgroundColor: theme.colors.tile_bg,
    borderRadius: "16@ms",
    overflow: "hidden",
  },
  mealListView: {
    paddingVertical: "14@ms",
    paddingHorizontal: "16@ms",
    width: "100%",
    backgroundColor: theme.colors.tile_bg,
    borderRadius: "16@ms",
    overflow: "hidden",
  },
  dailypheallowance: {
    marginLeft: ms(15),
  },
  dayContainer: {
    width: ms(36),
    height: ms(36),
    borderRadius: ms(18),
    backgroundColor: theme.colors.gray, // Default day background
    justifyContent: "center",
    alignItems: "center",
    position: "relative",
    marginHorizontal: ms(4),
  },
  activeDayContainer: {
    backgroundColor: theme.colors.primary, // Active day background
  },
  dayText: {
    fontSize: ms(14),
    fontFamily: Fonts.RALEWAY_BOLD,
    color: theme.colors.white, // Default day text color
  },
  activeDayText: {
    color: theme.colors.white, // Active day text color
  },
  dotAbove: {
    width: ms(8),
    height: ms(8),
    borderRadius: ms(4),
    backgroundColor: theme.colors.primary, // Dot color (pink)
    position: "absolute",
    top: -ms(15), // Position above the day
  },

  addButtonText: {
    fontSize: ms(14),
    fontFamily: Fonts.RALEWAY_BOLD,
    color: theme.colors.gray,
  },
  totalPheText: {
    fontSize: ms(12),
  },
  mealList: {
    marginTop: "10@ms",
    minHeight: "100@vs"
  },
  mealItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: ms(5),
    padding: "12@ms",
    backgroundColor: theme.colors.item_bg,
    borderRadius: "8@ms",
  },
  mealName: {
    fontSize: ms(13),
  },
  mealDetailsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  mealDetails: {
    fontSize: ms(11),
  },
  saveButton: {
    marginTop: "20@ms",
    paddingVertical: "12@ms",
    alignItems: "center",
    backgroundColor: theme.colors.primary,
    borderRadius: "8@ms",
  },
  saveButtonText: {
    fontSize: ms(16),
    fontFamily: Fonts.RALEWAY_BOLD,
    color: theme.colors.white,
  },
  addButton: {
    borderRadius: ms(8),
    width: ms(78),
    height: ms(32),
    paddingHorizontal: ms(8),
    marginLeft: ms(10),
  },
  iconTextWrapper: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  textButton: {
    marginLeft: ms(10),
    fontSize: ms(15),
  },
  tag: {
    marginHorizontal: ms(5),
    height: ms(23),
  },
  tagSpacing: {
    marginRight: ms(12),
  },

  tabSelectorContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: ms(8),
  },
  saveMealContainer: {
    paddingVertical: ms(5),
    paddingHorizontal: ms(5),
    borderRadius: ms(12),
  },
  saveMealRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  saveIcon: {
    marginRight: ms(8),
    marginBottom: ms(5),
  },
  saveMealText: {
    color: theme.colors.textPrimaryYellow,
  },
  header: {
    alignItems: "center",
    marginBottom: ms(40),
    width: "100%",
  },
  title: {
    marginTop: ms(8),
    fontSize: ms(22),
    width: "100%",
    textAlign: "center",
  },
  foodItemChild: {
    borderRadius: ms(8),
    borderTopLeftRadius: ms(0),
    borderTopRightRadius: ms(0),
  },
  border_0: {
    borderRadius: 0,
    borderTopWidth: 0.8,
    borderColor: theme.colors.mediumGray,
  },
  foodItemText: {
    fontFamily: Fonts.RALEWAY_REGULAR,
    marginLeft: ms(4),
  },
  emptyMealView: {
    flex: 1,
    minHeight: "98@vs",
    justifyContent: 'center',
    alignItems: 'center'
  }
});

export default getDietTrackStyles;
