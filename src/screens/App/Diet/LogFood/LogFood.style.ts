import { Fonts } from "@/constants";
import { Theme } from "@/types/theme/theme";
import { StyleSheet } from "react-native";
import { ms, vs } from "react-native-size-matters";

const getLogFoodStyles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: ms(16),
      backgroundColor: theme.colors.tile_bg,
    },
    header: {
      fontSize: ms(15),
      color: theme.colors.textPrimary,
      marginBottom: ms(20),
      marginTop: ms(10),
      fontFamily: Fonts.RALEWAY_MEDIUM,
    },
    nutrientsContainer: {
      flexDirection: "row",
      justifyContent: "space-around",
      flex: 1,
      alignItems: "center",
      marginBottom: vs(12),
      marginHorizontal: vs(40),
    },
    nutrientBox: {
      alignItems: "center",
      flex: 1,
    },
    nutrientValue: {
      color: theme.colors.textPrimaryYellow,
      fontSize: ms(20),
    },
    nutrientLabel: {
      color: theme.colors.mediumGray,
      marginTop: vs(4),
    },
    footer: {
      marginTop: ms(30),
      marginBottom: ms(30),
      paddingHorizontal: ms(28),
    },
    safeScreenView: {
      backgroundColor: theme.colors.tile_bg,
      flex: 1,
    },
    scrollViewContainer: {
      paddingBottom: ms(78),
    },
    btnTextColor: {
      color: theme.colors.white,
    },
    mealHeading: {
      textAlign: "center",
      marginBottom: ms(10),
      marginTop: ms(20)
    },
  });

export default getLogFoodStyles;
