/** @format */

import { Button, Typography } from "@/components/atoms";
import React, { useMemo, useState } from "react";
import { ScrollView, View } from "react-native";
import { AISuggestionsItem, Header } from "@/components/molecules";
import { useAppDispatch, useAppSelector } from "@/store";
import {
  createFoodEntry,
  editingMatchFood,
  fetchFoodEntries,
  selectDietTracker,
} from "@/store/slices/dietTrackerSlice";
import Common from "@/theme/common.style";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { FoodEntryItem, TimeSlot } from "@/types/schemas/dietTracker";
import { isNonEmptyArray, updateDateWithTimeSlot } from "@/utils/helpers";
import { SafeScreen } from "@/components/template";
import { useNavigation } from "@react-navigation/native";
import getLogFoodStyles from "./LogFood.style";
import Loading from "@/components/atoms/Loading/Loading";
import { useTheme } from "@/theme";
import { SCREEN_HEIGHT } from "@/theme/_config";
import { useDisableDrawerSwipe } from "@/hooks/useDisableDrawerSwipe";
import { ms } from "react-native-size-matters";

const LogFood = () => {
  const dispatch = useAppDispatch();
  const { selectedDietDate, selectedTimeSlot, matchFoods } = useAppSelector(selectDietTracker);
  const navigation = useNavigation();
  const [showLoader, setShowLoader] = useState(false);
  const { variant } = useTheme();
  const styles: any = useDynamicStyles(getLogFoodStyles);
  const { colors } = useTheme();
  const [fieldErrors, setFieldErrors] = useState<Record<string, boolean>>({});
  useDisableDrawerSwipe();

  const summary = useMemo(() => {
    let totalPhe = 0;
    let totalProtein = 0;

    if (!matchFoods) return { phe: 0, protein: 0 };

    matchFoods?.forEach((food: FoodByIdResponse) => {
      if (food?.user_flags?.is_free) {
        return;
      }
      totalPhe += Number(food.phe || 0);
      totalProtein += Number(food.protein || 0);
    });

    const areAllFoodsFree = matchFoods?.every((food: FoodByIdResponse) => food?.user_flags?.is_free);
    return {
      phe: totalPhe?.toFixed(2),
      protein: totalProtein?.toFixed(2),
      areAllFoodsFree,
    };
  }, [JSON.stringify(matchFoods)]);

  const handleItemUpdate = (index: number, item: FoodEntryItem) => {
    const updatedItems = [...(matchFoods || [])];
    updatedItems[index] = {
      ...updatedItems[index],
      ...item,
      user_flags: { ...updatedItems[index].user_flags, ...item.user_flags },
    };

    dispatch(editingMatchFood(updatedItems));
  };

  const handleBackPress = () => {
    navigation.goBack();
  };
  const onCancelPress = () => {
    navigation.navigate("DietScreen" as never);
  };
  const handleErrorChange = (hasError: boolean, itemId: string) => {
    setFieldErrors((prev) => ({
      ...prev,
      [itemId]: hasError,
    }));
  };
  const hasAnyError = Object.values(fieldErrors).some(Boolean);

  const renderSuggestionsSection = () => {
    if (isNonEmptyArray(matchFoods)) {
      return matchFoods.map((item, index) => (
        <AISuggestionsItem
          key={item?.food_id?.toString?.()}
          item={item}
          onUpdate={(updatedFields: any) => handleItemUpdate(index, updatedFields)}
          onErrorChange={(hasError) => handleErrorChange(hasError, item?.food_id || item?._id)}
        />
      ));
    }

    return null;
  };

  const createEntryWithDelay = async (
    item: any,
    index: number,
    selectedDietDate: Date,
    selectedTimeSlot: TimeSlot,
    dispatch: any
  ) => {
    const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));
    await delay(500 * index);

    let baseTime = updateDateWithTimeSlot(`${selectedDietDate}`, selectedTimeSlot);
    if (baseTime) {
      baseTime.setUTCMilliseconds(baseTime.getUTCMilliseconds() + index);
    }

    const payload = {
      description: item?.description,
      items: [
        {
          description: item?.description,
          quantity: parseFloat(item.quantity),
          unit: item.unit,
          gram_weight: parseFloat(item.gram_weight),
          phe: parseFloat(item.phe),
          protein: parseFloat(item.protein),
          food_id: item._id,
          user_flags: {
            is_free: !!item?.user_flags?.is_free,
          },
          detection_id: item?.detection_id || "",
        },
      ],
      time: baseTime?.toISOString(),
      analysis_id: item.analysis_id,
    };
    return payload;
    // await dispatch(createFoodEntry(payload));
  };

  const handleLogFoodPress = async () => {
    setShowLoader(true);
    try {
      // const foodsArray = await matchFoods?.map((item, index) =>
      //   createEntryWithDelay(item, index, selectedDietDate, selectedTimeSlot, dispatch)
      // );
      let baseTime = updateDateWithTimeSlot(`${selectedDietDate}`, selectedTimeSlot);
      const entryObject = {
        description: matchFoods[0]?.description,
        items: matchFoods?.map((item, index) => {
          const entry = {
            description: item?.description,
            quantity: parseFloat(item.quantity),
            unit: item.unit,
            gram_weight: parseFloat(item.gram_weight),
            phe: parseFloat(item.phe),
            protein: parseFloat(item.protein),
            food_id: item._id,
            user_flags: {
              is_free: !!item?.user_flags?.is_free,
            },
            detection_id: item?.detection_id || "",
          };
          return entry;
        }),
        time: baseTime?.toISOString(),
        analysis_id: matchFoods[0]?.analysis_id,
      };
      await dispatch(createFoodEntry(entryObject));
    } catch (error) {
      console.error("Error occurred:", error);
    } finally {
      dispatch(
        fetchFoodEntries({
          date: new Date(selectedDietDate).toISOString(),
        })
      );
      setShowLoader(false);
      navigation.navigate("DietScreen");
    }
  };

  if (showLoader) {
    return <Loading />;
  }
  return (
    <SafeScreen
      containerStyle={{
        backgroundColor: colors.tile_bg,
        minHeight: SCREEN_HEIGHT,
      }}
    >
      <View style={styles.container}>
        <Header
          title="Log Food"
          onBackPress={handleBackPress}
          isDark={variant === "dark"}
          isCancel={true}
          onCancelPress={onCancelPress}
          customStyle={{ marginLeft: ms(54) }}
        />
        <View>
          <ScrollView contentContainerStyle={styles.scrollViewContainer} showsVerticalScrollIndicator={false}>
            {/*  AI Suggestions */}
            <View style={Common.flexOne}>
              <Typography.B2 style={styles.header}>AI Suggestions</Typography.B2>
              {renderSuggestionsSection()}
            </View>

            {isNonEmptyArray(matchFoods) ? (
              <>
                <Typography.B1 style={[Common.textBold, styles.mealHeading]}>Your meal has</Typography.B1>

                <View style={styles.nutrientsContainer}>
                  <View style={styles.nutrientBox}>
                    <Typography.H1 style={styles.nutrientValue}>
                      {summary?.areAllFoodsFree ? "FREE" : `${summary?.phe || "0"} mg`}
                    </Typography.H1>
                    <Typography.B2 style={styles.nutrientLabel}>Phe</Typography.B2>
                  </View>

                  <View style={styles.nutrientBox}>
                    <Typography.H1 style={styles.nutrientValue}>
                      {summary?.areAllFoodsFree ? "FREE" : `${summary?.protein || "0"} g`}
                    </Typography.H1>
                    <Typography.B2 style={styles.nutrientLabel}>Protein</Typography.B2>
                  </View>
                </View>

                <View style={styles.footer}>
                  <Button.Main onPress={handleLogFoodPress} disabled={hasAnyError}>
                    <Typography.B1 style={[Common.textBold, styles.btnTextColor]}>Log Food</Typography.B1>
                  </Button.Main>
                </View>
              </>
            ) : null}
          </ScrollView>
        </View>
      </View>
    </SafeScreen>
  );
};

export default LogFood;
