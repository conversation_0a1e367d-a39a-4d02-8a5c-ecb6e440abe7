/** @format */

import Typography from "@/components/atoms/Typography/Typography";
import MealItem from "@/components/molecules/MealItem/MealItem";
import { SafeScreen } from "@/components/template";
import { analyticsEventType, useAnalytics } from "@/hooks/useAnalytics";
import { useAppDispatch, useAppSelector } from "@/store";
import {
  cleanApiState,
  clearScanData,
  clearSearch,
  createFoodEntry,
  fetchFoodEntries,
  selectDietTracker,
  selectFoodItemsByFrequency,
} from "@/store/slices/dietTrackerSlice";
import Icons from "@/theme/assets/images/svgs/icons";
import Common from "@/theme/common.style";
import { FoodEntryItem } from "@/types/schemas/dietTracker";

interface FoodSearchResponse {
  id?: string;
  description?: string;
  quantity?: number;
  unit?: string;
  phe?: number;
  protein?: number;
  pheText?: string;
  user_flags?: {
    is_free?: boolean;
  };
}
import {
  removeTrailingZeros,
  roundNumber,
  updateDateWithTimeSlot,
} from "@/utils/helpers";
import { PortionConverter } from "@/utils/portions";
import React, { useCallback, useEffect, useRef, useState } from "react";
import {
  ActivityIndicator,
  FlatList,
  Keyboard,
  Linking,
  RefreshControl,
  View,
} from "react-native";
import { useSelector } from "react-redux";

import { useTheme } from "@/theme";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { useCameraPermission } from "react-native-vision-camera";
import GenericModal from "@/components/molecules/GenericModal/GenericModal";
import getLogFoodStyle from "./LogFoodMain.style";
import { getFrequentEntries } from "@/services/api/dietTrackerAPI";
import { config, SCREEN_HEIGHT } from "@/theme/_config";
import {
  selectConsumptionUnit,
  selectIsSimplifiedDiet,
} from "@/store/slices/settingsSlice";
import { Header } from "@/components/molecules";
import { useNavigation } from "@react-navigation/native";
import SearchListInput, {
  SearchListInputRef,
} from "@/components/atoms/SearchListInput/SearchListInput";
import ActionButton from "@/components/molecules/ActionButton/ActionButton";
import CameraModal from "@/components/molecules/CameraModal/CameraModal";
import ManualEntryModal from "@/components/molecules/ManualEntryModal/ManualEntryModal";
import LogFoodInputModal from "@/components/molecules/LogFoodInputModal/LogFoodInputModal";
import { useDisableDrawerSwipe } from "@/hooks/useDisableDrawerSwipe";

const LogFoodMain = () => {
  const [refreshing, setRefreshing] = useState(false);
  const [selectedFood, setSelectedFood] = useState<FoodEntryItem | null>(null); // State to track the selected meal
  const [isManualEntry, setIsManualEntry] = useState(false);
  const [isVisibleCamera, seIsVisibleCamera] = useState(false);
  const [frequentLoading, setFrequentLoading] = useState(false);
  const consumptionType = useSelector(selectConsumptionUnit);
  const isSimplifiedDiet = useAppSelector(selectIsSimplifiedDiet);

  const [frequentFood, setFrequentFood] = useState<FoodSearchResponse[] | null>(
    []
  );
  const [showPermissionModal, setShowPermissionModal] = useState(false);
  const { setAnalyticsEvent } = useAnalytics();

  const { colors } = useTheme();

  const styles: any = useDynamicStyles(getLogFoodStyle);

  const { requestPermission } = useCameraPermission();

  const dispatch = useAppDispatch();
  const searchResults = useSelector(selectFoodItemsByFrequency);

  const { selectedDietDate, selectedTimeSlot, scanStatus } =
    useAppSelector(selectDietTracker);
  const { user } = useAppSelector((state) => state.user);

  const navigation = useNavigation();
  const { variant } = useTheme();
  const searchInputRef = useRef<SearchListInputRef>(null);

  useDisableDrawerSwipe();


  useEffect(() => {
    getFreqFood();
    dispatch(clearScanData());
    return () => {
      dispatch(clearSearch());
    };
  }, [dispatch, isSimplifiedDiet]);

  const getFreqFood = async () => {
    try {
      setFrequentLoading(true);
      const v = await getFrequentEntries(consumptionType);
      setFrequentFood(v as any);
    } catch (error) {
      console.error("Error fetching frequent foods:", error);
    } finally {
      setFrequentLoading(false);
    }
  };

  const handleSelectItem = (item: any) => {
    Keyboard.dismiss();
    setSelectedFood(item as FoodEntryItem);

    setAnalyticsEvent(analyticsEventType.custom, {
      event: "diet_search_food_logged",
      item_id: "diet_search_food_logged",
      action: "User logged searched food",
    });
  };

  const renderMealItem = ({ item }: { item: FoodSearchResponse }) => {
    const valueToDisplay =
      isSimplifiedDiet && (item as any).user_flags?.is_free
        ? "FREE"
        : consumptionType === "Protein"
          ? `${Number(roundNumber(item.protein || 0)) || 0}`
          : `${Number(roundNumber(item.phe || 0)) || 0}`;

    item.isFreeFood = item?.user_flags?.is_free;

    return (
      <View>
        <MealItem
          key={item.id}
          name={item.description || "No description available"}
          quantity={`${removeTrailingZeros(item.quantity || 0)}`}
          unit={item.unit || ""}
          phe={valueToDisplay}
          icon={
            <View style={styles.icon}>
              <Icons.Plus width={10} height={10} color={colors.textPrimary} />
            </View>
          }
          onPress={() => setSelectedFood(item as unknown as FoodEntryItem)} // Type cast to FoodEntryItem
          backgroundColor={colors.dropDownGray} // Custom background color
          tagBackgroundColor={colors.tile_bg} // Custom tag background color
          textColor={colors.textPrimary} // Custom text color
          isTextBold
          isFreeFood={item?.user_flags?.is_free}
        />
      </View>
    );
  };

  const onLogFood = async ({
    quantity,
    phe,
    unit,
    protein,
    gram_weight,
    isFreeFood,
  }: {
    quantity: number;
    phe: number;
    unit: string;
    protein: number;
    gram_weight: number;
    isFreeFood?: boolean;
  }) => {
    try {
      setAnalyticsEvent(analyticsEventType.custom, {
        event: "diet_custom_food_started",
        item_id: "diet_custom_food_started",
        action: "User tapped custom food",
      });

      const time = updateDateWithTimeSlot(
        `${selectedDietDate}`,
        selectedTimeSlot
      );
      const _oldGramWeight = PortionConverter.toGrams(
        unit || selectedFood?.unit,
        quantity || selectedFood?.quantity
      );
      const payload = {
        description: selectedFood?.description,
        time: time,
        items: [
          {
            description: selectedFood?.description,
            quantity: quantity || selectedFood?.quantity,
            unit: unit || selectedFood?.unit,
            gram_weight: gram_weight || _oldGramWeight,
            phe: phe || selectedFood?.phe,
            protein: protein || selectedFood?.protein,
            food_id: selectedFood?.id,
            user_flags: {
              is_free: isFreeFood,
            },
            detection_id: "",
          },
        ],
      };

      const response = await dispatch(createFoodEntry(payload as any));
      const formattedDate = new Date(selectedDietDate);

      if (createFoodEntry.fulfilled.match(response)) {
        await dispatch(fetchFoodEntries({ date: formattedDate }));
        getFreqFood();
        navigation.navigate("DietScreen");

      }
      setSelectedFood(null);
      dispatch(cleanApiState());

      setAnalyticsEvent(analyticsEventType.custom, {
        event: "diet_custom_food_logged",
        item_id: "diet_custom_food_logged",
        action: "User logged custom food",
      });
    } catch (e) {
      console.error("Error logging food:", e);
    }
  };

  const checkPermission = async () => {
    Keyboard.dismiss();
    setAnalyticsEvent(analyticsEventType.custom, {
      event: "diet_scan_meal_started",
      item_id: "diet_scan_meal_started",
      action: "User tapped scan meal",
    });
    const isGranted = await requestPermission();
    if (!isGranted) {
      setShowPermissionModal(true); // Show your generic modal
    } else {
      seIsVisibleCamera(true);
    }
  };

  const manualEntryHandler = useCallback(() => {
    Keyboard.dismiss();
    setIsManualEntry(true);
  }, [Keyboard]);

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      // Your refresh logic here (e.g., fetch updated data)
      await fetchUpdatedFoodEntries();
    } catch (error) {
      console.error("Error refreshing food entries:", error);
    } finally {
      setRefreshing(false);
    }
  };

  const fetchUpdatedFoodEntries = async () => {
    try {
      // Dispatch the action to fetch food entries
      await getFreqFood();
    } catch (error) {
      console.error("Error fetching updated food entries:", error);
    }
  };

  const handleBackPress = () => {
    navigation.goBack();
  };

  const LoadingIndicator = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color={config.colors.primary} />
    </View>
  );

  const EmptyFoodListMessage = ({ text }: { text: string }) => {
    const { colors } = useTheme();
    return (
      <View style={{ marginVertical: "3%" }}>
        <Typography.H4
          style={{
            ...styles.actionButtonText,
            color: colors.textPrimary,
          }}
        >
          {text}
        </Typography.H4>
      </View>
    );
  };
  return (
    <SafeScreen
      containerStyle={{
        backgroundColor: colors.tile_bg,
        minHeight: SCREEN_HEIGHT,
      }}
    >
      <View style={styles.container}>
        <Header
          title="Log Food"
          onBackPress={handleBackPress}
          isDark={variant === "dark"}
        />
        <GenericModal
          isVisible={showPermissionModal}
          onClose={() => setShowPermissionModal(false)}
          onConfirm={() => Linking.openSettings()}
          headerText="Camera Permission Required"
          bodyText="To access the camera, please grant permission in your device settings."
          confirmText="Enable"
          closeText="Cancel"
          customHeader={null}
          customBody={null}
          customFooter={null}
        />
        <View style={styles.sectionContainer}>
          <SearchListInput
            ref={searchInputRef}
            showTitle
            onSelectItem={handleSelectItem}
            onAddNew={() => {
              Keyboard.dismiss();
              setIsManualEntry(true)}}
            searchResults={searchResults}
            placeholder="Search for Foods"
          />
          <View style={styles.reverse_zIndex_1}>
            <View style={styles.actionButtonsContainer}>
              <ActionButton
                onPress={checkPermission}
                icon={<Icons.Scan width={37} height={37} />}
                label="Scan Meal"
                style={styles.actionButton}
                textStyle={styles.actionButtonText}
              />

              <ActionButton
                onPress={manualEntryHandler}
                icon={
                  <Icons.CirclePlus
                    width={37}
                    height={37}
                    stroke={colors.textPrimary}
                  />
                }
                label="Manual Entry"
                style={[styles.manualEntryColor, styles.actionButton]}
                textStyle={[
                  styles.actionButtonText2,
                  { color: colors.textPrimary },
                ]}
              />
            </View>
            {/* Food List */}
            <Typography.H1 style={[Common.textBold, styles.foodsLabel]}>
              {user?.name ? `${user?.name}'s meals` : "Your meal(s)"}
            </Typography.H1>
            <View style={styles.separator} />
            {frequentLoading ? (
              <LoadingIndicator />
            ) : (
              <FlatList
                data={frequentFood}
                renderItem={renderMealItem} // Render each FoodEntry
                keyExtractor={(item, index) =>
                  (item.id ?? index).toString() + index
                } // Unique key for each FoodEntry
                style={styles.foodListView}
                ItemSeparatorComponent={<View style={styles.gap_8} />}
                contentContainerStyle={styles.foodList}
                showsVerticalScrollIndicator
                refreshControl={
                  <RefreshControl
                    refreshing={refreshing}
                    onRefresh={onRefresh}
                    tintColor={config.colors.primary}
                    colors={[config.colors.primary]}
                  />
                }
                ListEmptyComponent={
                  <EmptyFoodListMessage text="The foods you eat the most often will appear here" />
                }
              />
            )}
          </View>
        </View>
        <CameraModal
          isVisible={isVisibleCamera}
          onClose={() => {
            seIsVisibleCamera(false);
            setShowPermissionModal(false);
          }}
          seIsVisibleCamera={seIsVisibleCamera}
        />
        <ManualEntryModal
          isVisible={isManualEntry}
          onClose={() => {
            setIsManualEntry(false);
          }}
          triggeredScreen={"LogFoodMain"}
          setPayLoadForEntry={(val) => console.log(val, "selectedpaylod")}
        />
        <LogFoodInputModal
          selectedFood={selectedFood}
          isVisible={selectedFood ? true : false}
          onClose={() => {
            setSelectedFood(null);
            dispatch(clearSearch());
            searchInputRef.current?.reset();
          }}
          onLogFood={onLogFood}
        />
      </View>
    </SafeScreen>
  );
};

export default LogFoodMain;
