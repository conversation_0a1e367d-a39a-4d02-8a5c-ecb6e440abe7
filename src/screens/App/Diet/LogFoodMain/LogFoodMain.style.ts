import { ms, ScaledSheet, vs } from "react-native-size-matters";
import { Dimensions } from "react-native";
import { Theme } from "@/types/theme/theme";
import { SCREEN_HEIGHT } from "@/theme/_config";

const { width } = Dimensions.get("window");

const addMealStyles = (theme: Theme) =>
  ScaledSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: ms(20),
      backgroundColor: theme.colors.tile_bg,
    },
    header: {
      alignItems: "center",
      marginBottom: ms(40),
    },
    title: {
      marginTop: ms(20),
      fontSize: ms(22),
      textAlign: "center",
    },
    actionButtonsContainer: {
      flexDirection: "row",
      alignItems: "center",
      width: "100%",
      justifyContent: "space-between", // Ensures even spacing between buttons
      marginVertical: ms(10),
      columnGap: 8,
    },
    actionButton: {
      alignItems: "center",
      justifyContent: "center",
      borderRadius: ms(16),
      height: ms(143), // Adjusted height for responsiveness
      width: width * 0.44, // Dynamically calculates width (e.g., 42% of screen width)
      padding: ms(10), // Adds internal padding
    },
    manualEntryColor: {
      backgroundColor: theme.colors.app_bg, // Custom background for manual entry
    },
    actionButtonText: {
      marginTop: ms(8),
      fontSize: ms(14),
      color: theme.colors.white,
      textAlign: "center", // Center-aligns text
    },
    foodsLabel: {
      fontSize: ms(16),
      color: theme.colors.textPrimary,
      marginVertical: ms(15),
    },
    separator: {
      marginBottom: ms(8),
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.mediumGray,
    },
    foodList: {
      paddingBottom: ms(80),
      paddingTop: ms(8),
    },
    itemName: {
      fontSize: ms(14),
      marginBottom: ms(3),
    },
    itemDetails: {
      fontSize: ms(12),
    },
    icon: {
      marginLeft: 5,
    },
    icon_marginRight_5: {
      marginRight: 5,
    },
    reverse_zIndex_1: {
      zIndex: -1,
    },
    relative_position: { position: "relative" },
    foodListView: {
      height: SCREEN_HEIGHT * 0.5,
      gap: 8,
    },
    gap_8: {
      marginVertical: ms(8),
    },
    loadingContainer: {
      flex: 1,
      height: ms(280),
      alignItems: "center",
      justifyContent: "center",
      marginTop: SCREEN_HEIGHT * 0.125,
    },
    sectionContainer: {
      marginTop: ms(20),
    },
    actionButtonText2: {
      marginTop: ms(8),
      fontSize: ms(14),
      textAlign: "center", // Center-aligns text
    },
  });

export default addMealStyles;
