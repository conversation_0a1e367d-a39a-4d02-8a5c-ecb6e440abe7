/** @format */

import { Fonts } from "@/constants";
import { Theme } from "@/types/theme/theme";
import { ScaledSheet, ms } from "react-native-size-matters";

const getAISuggestionsStyle = (theme: Theme) =>
  ScaledSheet.create({
    //Main Styles
    mainBackground: {
      flex: 1,
      backgroundColor: theme.colors.aiBackground,
      justifyContent: "center",
    },
    imageContainer: {
      height: ms(200),
      width: "100%",
    },
    // Header section
    headerContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: ms(16),
      paddingVertical: ms(10),
      marginVertical: ms(5),
    },
    headerTitle: {
      flex: 1,
      textAlign: "center",
      fontSize: ms(20),
      fontFamily: Fonts.RALEWAY_BOLD,
      color: theme.colors.textPrimary,
    },
    cancelText: {
      color: theme.colors.textPrimary,
      fontSize: ms(15),
      lineHeight: ms(20),
      fontFamily: Fonts.RALEWAY_REGULAR,
    },

    // Main content
    contentContainer: {
      flex: 10,
      paddingHorizontal: ms(16),
      paddingTop: ms(12),
    },
    somethingMissingText: {
      fontSize: ms(15),
      color: theme.colors.textPrimary,
      marginBottom: ms(12),
      fontFamily: Fonts.RALEWAY_REGULAR,
      lineHeight: ms(20),
    },
    emptyListText: {
      fontSize: ms(15),
      color: theme.colors.textPrimary,
      marginBottom: ms(12),
      fontFamily: Fonts.RALEWAY_REGULAR,
      lineHeight: ms(20),
      textAlign: "center",
    },
    aiSuggestionsTitle: {
      fontFamily: Fonts.RALEWAY_REGULAR,
      lineHeight: ms(20),
      fontSize: ms(15),
      color: theme.colors.textPrimary,
      marginBottom: ms(16),
    },

    // Search bar
    searchBarContainer: {
      flexDirection: "row",
      alignItems: "center",
      borderWidth: 1,
      borderColor: theme.colors.mediumGray,
      borderRadius: ms(8),
      paddingHorizontal: ms(12),
      marginBottom: ms(20),
    },
    searchInput: {
      flex: 1,
      marginLeft: ms(8),
      fontSize: ms(14),
      color: theme.colors.textPrimary,
    },

    // Section title
    aiSuggestionsTitle: {
      fontSize: ms(16),
      fontWeight: "bold",
      color: theme.colors.textPrimary,
      marginBottom: ms(16),
    },
    suggestionsList: {
      flexGrow: 1,
      paddingBottom: ms(50),
    },

    // Bottom buttons
    bottomButtonsContainer: {
      flex:1,
      justifyContent: "flex-end",
      paddingHorizontal: ms(50),
      marginTop: ms(20),
      zIndex:-100
    },
    buttons: {
      borderRadius: ms(16),
      height: ms(47),
      marginBottom: ms(10),
    },
    buttonsText: {
      textAlign: "center",
      fontSize: ms(17),
      fontFamily: Fonts.RALEWAY_BOLD,
      lineHeight: ms(17),
      color:theme.colors.textPrimary
    },
    continueText:{
      color:theme.colors.white
    }
  });

export default getAISuggestionsStyle;
