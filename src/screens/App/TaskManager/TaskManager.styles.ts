import {
  ms,
  vs,
  scale,
  ScaledSheet,
  verticalScale,
} from "react-native-size-matters";
import { Fonts } from "@/constants";
import { Dimensions } from "react-native";

const { width: screenWidth } = Dimensions.get("screen");

const getTaskManagerStyles = (theme) =>
  ScaledSheet.create({
    container: {
      flex: 1,
    },
    scrollViewContent: {
      paddingBottom: "20@vs",
      columnGap: ms(8),
      flexGrow: 1,
    },
    emptyListContainer: {
      flexGrow: 1,
      justifyContent: "space-between",
      alignItems: "center",
    },
    emptyListText: {
      textAlign: "center",
      marginTop: ms(18),
      color: theme.colors.mediumGray,
    },
    addButton: {
      flexDirection: "row",
      width: screenWidth * 0.7,
      minHeight: ms(70),
      justifyContent: "flex-start",
      alignItems: "center",
      paddingHorizontal: scale(48),
      marginBottom: verticalScale(13.2),
      borderColor: theme.colors.gray,
    },

    noMedication: {
      alignItems: "center",
      justifyContent: "center",
      marginHorizontal: scale(60),
      paddingVertical: verticalScale(16),
    },
    headerContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },

    listContainer: {
      paddingVertical: 10,
    },
    separator: {
      height: ms(4), // Adjust this for more or less spacing
    },

    addBtnSmall: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      paddingHorizontal: scale(12),
      minWidth: ms(74),
      minHeight: ms(30),
      borderColor: theme.colors.gray,
    },
    centerRow: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
    },
    addText: {
      marginLeft: ms(6),
      fontFamily: Fonts.RALEWAY_BOLD,
    },
    plusIcon: {
      marginRight: ms(6),
      marginTop: vs(1),
    },
  });

export default getTaskManagerStyles;
