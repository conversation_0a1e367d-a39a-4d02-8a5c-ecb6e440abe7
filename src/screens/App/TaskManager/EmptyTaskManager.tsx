import React, { FC } from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';

import { useTheme } from '@/theme';
import RNstyles from './TaskManager.styles';
import Icons from '@/theme/assets/images/svgs/icons';
import { Button, Typography } from '@/components/atoms';
import { useDynamicStyles } from '@/theme/hooks/useDynamicStyles';
import getTaskManagerStyles from './TaskManager.styles';

type EmptyTaskManagerProps = {
  handleAddMedication: () => void;
  handleAddFormula: () => void;
};

const EmptyTaskManager: FC<EmptyTaskManagerProps> = ({
  handleAddMedication,
  handleAddFormula
}) => {
  const { t } = useTranslation('taskManager');
  const { gutters, colors } = useTheme();
  const styles: any = useDynamicStyles(getTaskManagerStyles)
  return (
    <View style={styles.emptyListContainer}>
      <View />

      <View style={styles.noMedication}>
        <Icons.NoMedication />
        <Typography.H4 style={styles.emptyListText}>
          {t('emptyListingMessage')}
        </Typography.H4>
      </View>

      <View>
        <Button.Outline style={styles.addButton} onPress={handleAddMedication}>
          <Icons.CirclePlus stroke={colors.textPrimary} />
          <Typography.B1 style={gutters.marginLeft_20}>
            {t('addMedication')}
          </Typography.B1>
        </Button.Outline>

        <Button.Outline style={styles.addButton} onPress={handleAddFormula}>
          <Icons.CirclePlus stroke={colors.textPrimary} />
          <Typography.B1 style={gutters.marginLeft_20}>
            {t('addFormula')}
          </Typography.B1>
        </Button.Outline>
      </View>
    </View>
  );
};

export default EmptyTaskManager;
