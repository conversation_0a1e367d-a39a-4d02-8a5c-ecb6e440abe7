import { useTranslation } from "react-i18next";
import { ms } from "react-native-size-matters";
import React, { useCallback, useEffect, useState } from "react";
import { FlatList, RefreshControl, ScrollView, View } from "react-native";

import {
  Header,
  FormulaCard,
  MedicationCard,
  MedicationBottomSheet,
} from "@/components/molecules";
import {
  fetchFormulaTasks,
  fetchFrequencyData,
  fetchMedicationTasks,
  fetchMedicationCategories,
} from "@/store/slices/taskManager/taskManager.middleware";
import { useTheme } from "@/theme";
import Common from "@/theme/common.style";
import EmptyTaskManager from "./EmptyTaskManager";
import { SafeScreen } from "@/components/template";
import { useTaskManager } from "@/hooks/taskManager";
import Icons from "@/theme/assets/images/svgs/icons";
import { useAppDispatch, useAppSelector } from "@/store";
import getTaskManagerStyles from "./TaskManager.styles";
import { classifyTasksByPeriod } from "@/utils/taskSorter";
import { But<PERSON>, <PERSON><PERSON>, Typography } from "@/components/atoms";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import SwipeableRow from "@/components/atoms/SwipeableRow/SwipeableRow";
import { selectTask } from "@/store/slices/taskManager/taskManager.slice";
import GenericModal from "@/components/molecules/GenericModal/GenericModal";
import useTaskManagerContainer from "@/containers/taskManager/useTaskManagerContainer";
import FormulaBottomSheet from "@/components/molecules/MedicationBottomSheet/FormulaBottomSheet";

const TaskManagerScreen = () => {
  const [selectedTaskId, setSelectedTaskId] = useState<number | null>(null);
  const { t } = useTranslation("taskManager");
  const [refreshing, setRefreshing] = useState<boolean>(false);

  const dispatch = useAppDispatch();
  const { variant } = useTheme();
  const styles: any = useDynamicStyles(getTaskManagerStyles);
  const { formulaTasks, medicationTasks, status, taskDetails, formulaDetails } =
    useAppSelector(selectTask);
  const { deleteMedTask } = useTaskManager();
  const {
    medModalRef,
    formulaModalRef,
    handleAddFormula,
    handleAddMedication,
    handleMedicationSummary,
    handleFormulaSummary,
    medFormStep,
    formulaFormStep,
    selectedTaskId: innerSelectedTaskId,
    handleMedModalNextPress,
    handleFormulaModalNextPress,
    handleMedModalBackPress,
    handleFormulaModalBackPress,
    medicationFormControl,
    formulaFormControl,
    setFormulaValue,
    getFooterButtonText,
    getFormulaFooterButtonPress,
    getFooterButtonPress,
    getFormulaFooterButtonText,
    handleMedModalClosePress,
    handleFormulaModalClosePress,
    populateFormulaDetails,
    populateMedicationDetails,
    getFrequencyName,
    customUpdateHandler,
    handleAddDose,
    handleRemoveDose,
  } = useTaskManagerContainer();

  const isMedFormulaEmpty = !medicationTasks.length && !formulaTasks.length;
  const { colors } = useTheme();
  const [confirmationModal, setConfirmationModal] = useState(false);
  const toggleConfirmation = useCallback(() => {
    if (selectedTaskId && typeof selectedTaskId !== "number") {
      setSelectedTaskId(null);
    }
    setConfirmationModal((prev) => !prev);
  }, [confirmationModal, selectedTaskId]);

  const swipeDeletehandler = useCallback(
    (id: any) => () => {
      setSelectedTaskId(id);
      toggleConfirmation();
    },
    [selectedTaskId]
  );

  const onRefresh = () => {
    setRefreshing(true);
    dispatch(fetchMedicationTasks());
    dispatch(fetchFormulaTasks());
    setTimeout(() => {
      setRefreshing(false);
    }, 300);
  };

  useEffect(() => {
    dispatch(fetchMedicationCategories());
    dispatch(fetchFrequencyData());
    dispatch(fetchMedicationTasks());
    dispatch(fetchFormulaTasks());
  }, [dispatch]);

  const isLoading =
    status?.medicationTasks === "loading" ||
    status?.formulaTasks === "loading" ||
    status?.deleteTask === "loading" ||
    status?.fetchFormulaById === "loading" ||
    status?.fetchTaskById === "loading";

  const sortedFormulaTasks = classifyTasksByPeriod(formulaTasks);
  const sortedMedicationTasks = classifyTasksByPeriod(medicationTasks);
  return (
    <SafeScreen>
      <View style={[styles.container, Common.appMarginHorizontal]}>
        <Header title="Tasks" showHamburgerMenu isDark={variant === "dark"} />

        {isLoading ? (
          <Loader />
        ) : (
          <ScrollView
            contentContainerStyle={styles.scrollViewContent}
            showsVerticalScrollIndicator={false}
            nestedScrollEnabled
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
          >
            {isMedFormulaEmpty ? (
              <EmptyTaskManager
                handleAddMedication={handleAddMedication}
                handleAddFormula={handleAddFormula}
              />
            ) : (
              <View>
                {/* Medication Listing Header */}
                <View style={styles.headerContainer}>
                  <Typography.H4 style={Common.textBold}>
                    {t("medication")}
                  </Typography.H4>

                  <Button.Outline
                    style={[styles.addBtnSmall, styles.centerRow]}
                    onPress={handleAddMedication}
                  >
                    <Icons.Plus
                      width={ms(8)}
                      height={ms(8)}
                      color={colors.textPrimary}
                      style={styles.plusIcon}
                    />
                    <Typography.B2 style={[Common.textBold, styles.addText]}>
                      {t("addDose")}
                    </Typography.B2>
                  </Button.Outline>
                </View>

                {/* Medication Listing */}
                <FlatList
                  data={[
                    ...sortedMedicationTasks.morning,
                    ...sortedMedicationTasks.afternoon,
                    ...sortedMedicationTasks.evening,
                  ]}
                  renderItem={({ item }) => (
                    <SwipeableRow onDelete={swipeDeletehandler(item.id)}>
                      <MedicationCard
                        {...item}
                        period={item.period}
                        onPress={() => handleMedicationSummary(item?.id, true)}
                      />
                    </SwipeableRow>
                  )}
                  keyExtractor={(item) => String(item.id)}
                  ItemSeparatorComponent={() => (
                    <View style={styles.separator} />
                  )}
                  contentContainerStyle={styles.listContainer}
                />

                <View style={styles.separator} />
                {/* Formula Listing Header */}
                <View style={styles.headerContainer}>
                  <Typography.H4 style={Common.textBold}>
                    {t("formula")}
                  </Typography.H4>

                  <Button.Outline
                    style={[styles.addBtnSmall, styles.centerRow]}
                    onPress={handleAddFormula}
                  >
                    <Icons.Plus
                      width={ms(8)}
                      height={ms(8)}
                      color={colors.textPrimary}
                      style={styles.plusIcon}
                    />
                    <Typography.B2 style={[Common.textBold, styles.addText]}>
                      {t("addFormula")}
                    </Typography.B2>
                  </Button.Outline>
                </View>

                {/* Formula Listing */}
                <FlatList
                  data={[
                    ...sortedFormulaTasks.morning,
                    ...sortedFormulaTasks.afternoon,
                    ...sortedFormulaTasks.evening,
                  ]}
                  renderItem={({ item }) => {
                    return(
                    <SwipeableRow onDelete={swipeDeletehandler(item.id)}>
                      <FormulaCard
                        {...item}
                        period={item.period}
                        onPress={() => handleFormulaSummary(item?.id, true)}
                      />
                    </SwipeableRow>
                  )}}
                  keyExtractor={(item) => String(item.id)}
                  ItemSeparatorComponent={() => (
                    <View style={styles.separator} />
                  )}
                  contentContainerStyle={styles.listContainer}
                />
              </View>
            )}
          </ScrollView>
        )}
      </View>

      <MedicationBottomSheet
        ref={medModalRef}
        selectedTaskId={taskDetails?.id}
        medFormStep={medFormStep}
        getFrequencyName={getFrequencyName}
        populateMedicationDetails={populateMedicationDetails}
        onDeletePress={() => {
          if (selectedTaskId) {
            deleteMedTask(selectedTaskId);
          }
          if (innerSelectedTaskId) {
            deleteMedTask(innerSelectedTaskId);
            medModalRef?.current?.close();
            formulaModalRef?.current?.close();
          }
        }}
        handleMedModalNextPress={handleMedModalNextPress}
        handleMedModalBackPress={handleMedModalBackPress}
        control={medicationFormControl}
        isLoading={status?.medicationTasks === "loading"}
        customUpdateHandler={customUpdateHandler}
        getFooterButtonText={getFooterButtonText}
        handleAddDose={handleAddDose}
        handleRemoveDose={handleRemoveDose}
        getFooterButtonPress={getFooterButtonPress}
        handleMedModalClosePress={handleMedModalClosePress}
      />

      <FormulaBottomSheet
        ref={formulaModalRef}
        selectedTaskId={formulaDetails?.id}
        onDeletePress={() => {
          if (formulaDetails?.id) {
            deleteMedTask(formulaDetails.id);
            formulaModalRef?.current?.close(); // close the modal after delete
          }
        }}
        formulaFormStep={formulaFormStep}
        getFrequencyName={getFrequencyName}
        customUpdateHandler={customUpdateHandler}
        populateFormulaDetails={populateFormulaDetails}
        handleFormulaModalNextPress={handleFormulaModalNextPress}
        handleFormulaModalBackPress={handleFormulaModalBackPress}
        control={formulaFormControl}
        setFormulaValue={setFormulaValue}
        isLoading={status?.formulaTasks === "loading"}
        getFormulaFooterButtonText={getFormulaFooterButtonText}
        getFormulaFooterButtonPress={getFormulaFooterButtonPress}
        handleFormulaModalClosePress={handleFormulaModalClosePress}
      />

      <GenericModal
        isVisible={confirmationModal}
        onClose={toggleConfirmation}
        onConfirm={() => {
          if (selectedTaskId) {
            deleteMedTask(selectedTaskId);
          }
          toggleConfirmation();
        }}
        headerText={"Confirm Delete"}
        bodyText={"Are you sure you want to delete?"}
        confirmText={"Yes"}
        closeText={"No"}
      />
    </SafeScreen>
  );
};

export default TaskManagerScreen;
