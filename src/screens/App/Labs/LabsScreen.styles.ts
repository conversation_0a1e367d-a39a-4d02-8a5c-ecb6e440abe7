import { Fonts } from "@/constants";

import { Theme } from "@/types/theme/theme";
import { ms, ScaledSheet } from "react-native-size-matters";

const getLabsStyles = (theme: Theme) =>
  ScaledSheet.create({
    container: {
      flex: 1,
      marginHorizontal: ms(15),
      overflow: "hidden",
    },
    loadingContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      marginTop: ms(15),
    },
    dropdownViewClose: {
      height: ms(28),
      width: ms(80),
      minWidth: ms(80),
      backgroundColor: theme.colors.primary,
      borderRadius: ms(16),
    },
    dropdownItemView: {
      backgroundColor: theme.colors.primary,
      borderBottomLeftRadius: ms(16),
      borderBottomRightRadius: ms(16),
      paddingHorizontal: ms(2),
    },
    dropdownViewOpen: {
      height: ms(28),
      width: ms(80),
      minWidth: ms(80),
      backgroundColor: theme.colors.primary,
      borderTopEndRadius: ms(16),
      borderBottomEndRadius: ms(0),
      borderTopLeftRadius: ms(16),
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.white,
    },
    dropdownOptionText: {
      lineHeight: "14@ms",
      fontSize: ms(11),
      color: theme.colors.white,
      fontFamily: Fonts.RALEWAY_REGULAR,
    },
    header: {
      flexDirection: "row",
      justifyContent: "flex-end",
      alignItems: "center",
    },
    headerContainer: {
      marginHorizontal: ms(16),
    },
    customChartHeader: {
      height: ms(55),
      width: "100%",
      backgroundColor: theme.colors.tile_bg,
      borderTopRightRadius: ms(10),
      borderTopLeftRadius: ms(10),
      alignItems: "center",
      justifyContent: "space-between",
      flexDirection: "row",
      paddingHorizontal: ms(10),
    },
    editButton: {
      alignSelf: "flex-end",
      borderRadius: ms(8),
      width: ms(70),
      height: ms(32),
    },
    textButton: {
      marginLeft: ms(10),
    },
    headerText: {
      color: "white",
      fontSize: 20,
    },
    addButton: {
      flexDirection: "row",
      alignItems: "center",
      borderWidth: 1,
      borderColor: theme.colors.gray,
      paddingHorizontal: ms(10),
      borderRadius: 4,
      height: ms(32),
    },
    addButtonText: {
      color: theme.colors.textPrimary,
      marginLeft: 4,
    },

    chartHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 5,
    },
    chartTitle: {
      color: theme.colors.textPrimary,
      fontSize: 16,
    },

    dropdownSpacing: {
      width: 10,
    },
    chartWrapper: {
      backgroundColor: theme.colors.tile_bg,
      alignItems: "center",
      borderRadius: ms(10),
      borderColor: theme.colors.tile_bg,
    },

    resultsTitle: {
      color: theme.colors.textPrimary,
      fontSize: 18,
      fontWeight: "bold",
      marginBottom: 10,
    },

    resultDate: {
      color: theme.colors.textPrimary,
      fontSize: 16,
    },

    resultsContainer: {
      borderRadius: ms(10),
      paddingVertical: ms(10),
      maxHeight: ms(280),
      flex: 1,
    },
    resultItem: {
      backgroundColor: theme.colors.labsGray,
      borderRadius: ms(10),
      padding: ms(14.8),
      marginBottom: ms(4.8),
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    resultDateChevron: {
      flexDirection: "row",
      alignItems: "center",
      gap: ms(15),
    },
    sectionHeader: {
      paddingHorizontal: ms(10),
      borderRadius: ms(8),
    },
    buttonContent: {
      flexDirection: "row",
      alignItems: "center",
    },
    iconStyle: {
      marginRight: 8, // Adjust spacing between icon and text
    },

    dropdownContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      gap: 10, // Add some spacing between dropdown and button,
      overflow:'hidden'
    },
    selectDateButton: {
      height: ms(28), // Ensure consistent height
      width: ms(92),
    },
    iconTextWrapper: {
      flexDirection: "row",
      alignItems: "center",
      gap: 5, // Space between the icon and text
    },

    cardContainer: {
      paddingHorizontal: ms(10),
      marginTop: ms(10),
    },
    scrollViewContent: {
      paddingBottom: "20@vs",
      columnGap: ms(8),
      paddingHorizontal: ms(4),
    },
    noDataText: {
      textAlign: "center",
      paddingVertical: ms(10),
    },
    pointerStyle: {
      height: 12,
      width: 12,
      borderRadius: 8,
      backgroundColor: theme.colors.primary,
      justifyContent: "center",
      alignItems: "center",
      marginLeft: -4,
    },
    xAxisLabelTextStyle: {
      color: theme.colors.mediumGray,
      fontSize: 10,
      marginTop: 5,
    },
    yAxisTextStyle: {
      color: theme.colors.mediumGray,
      fontSize: 10,
    },
    dateLabel: {
      color: theme.colors.white,
      fontSize: ms(12),
      textAlign: "center",
      flex: 1,
    },
    chartRangeTitle: {
      alignSelf: "flex-start",
      color: theme.colors.mediumGray,
      fontFamily: Fonts.RALEWAY_BOLD,
      fontSize: ms(12),
      lineHeight: ms(14),
      marginBottom: ms(20),
      marginTop: ms(5),
    },
    rangeDropdownViewClose: {
      height: ms(28),
      width: ms(110),
      minWidth: ms(110),
      backgroundColor: theme.colors.primary,
      borderRadius: ms(16),
    },
    rangeDropDownViewOpen: {
      height: ms(28),
      width: ms(110),
      minWidth: ms(110),
      backgroundColor: theme.colors.primary,
      borderTopEndRadius: ms(16),
      borderBottomEndRadius: ms(0),
      borderTopLeftRadius: ms(16),
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.white,
    },
    optionTextStyle:{
      fontSize: ms(11),
      fontFamily: Fonts.RALEWAY_REGULAR,
      lineHeight: ms(14),
      color: theme.colors.white

    }
  });

export default getLabsStyles;
