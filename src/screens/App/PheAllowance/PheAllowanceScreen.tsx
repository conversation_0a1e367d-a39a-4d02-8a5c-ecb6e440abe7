import { useSelector } from "react-redux";
import React, { useState, useCallback } from "react";
import { FlatList, Keyboard, View } from "react-native";
import { Button, Typography } from "@/components/atoms";
import { SafeScreen } from "@/components/template";

import Common from "@/theme/common.style";
import { useNavigation } from "@react-navigation/native";
import Header from "@/components/molecules/Header/Header";
import {
  AppDispatch,
  RootState,
  useAppDispatch,
  useAppSelector,
} from "@/store";
import {
  createPheAllowance,
  fetchDailyConsumedPheAllowance,
  fetchPheAllowances,
} from "@/store/slices/pheAllowanceSlice";
import Loading from "@/components/atoms/Loading/Loading";
import { PheAllowance } from "@/types/schemas/pheAllowance";
import getPHEAllowanceStyle from "./PheAllowanceScreen.style";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { selectDietTracker } from "@/store/slices/dietTrackerSlice";
import {
  formatDate,
  getPheAllowanceUnit,
  getPheAllowanceValue,
  setPheAllowanceValue,
} from "@/utils/helpers";
import CustomTextInput from "@/components/atoms/CustomTextInput/CustomTextInput";
import { useTheme } from "@/theme";
import { selectConsumptionUnit } from "@/store/slices/settingsSlice";

const PheAllowanceScreen = () => {
  const navigation = useNavigation();
  const dispatch = useAppDispatch<AppDispatch>();
  const [inputError, setInputError] = useState("");

  const { colors, variant } = useTheme();
  const styles: any = useDynamicStyles(getPHEAllowanceStyle);

  const [pheAllowanceAmount, setPheAllowanceAmount] = useState("");
  const [isFocused, setIsFocused] = useState(false);

  const handleFocus = () => setIsFocused(true);
  const handleBlur = () => setIsFocused(false);

  const pheAllowanceState = useSelector(
    (state: RootState) => state.pheAllowance
  );

  const { selectedDietDate } = useAppSelector(selectDietTracker);
  const consumptionType = useSelector(selectConsumptionUnit);

  const loading = pheAllowanceState?.loading;
  const [showInputError, setShowInputError] = useState(false);

  const pheAllowanceData = useSelector(
    (state: RootState) => state.pheAllowance.data
  );

  const currentPheAllowance =
    Array.isArray(pheAllowanceData) && pheAllowanceData.length > 0
      ? `${getPheAllowanceValue(pheAllowanceData[0]?.amount || 0, consumptionType)} ${getPheAllowanceUnit(consumptionType) || pheAllowanceData[0]?.unit}`
      : `0 ${getPheAllowanceUnit(consumptionType)}`;

  const openProfile = useCallback(() => {
    navigation.goBack();
  }, []);

  const updatePheAllowance = async () => {
    Keyboard.dismiss();

    if (!pheAllowanceAmount.trim()) {
      setInputError(
        `Please specify ${consumptionType === "Protein" ? "PRO" : "Phe"} allowance`
      );
      setShowInputError(true);
      return;
    }

    setInputError("");

    const body: PheAllowance = {
      pheAllowanceAmount: setPheAllowanceValue(
        pheAllowanceAmount,
        consumptionType
      ),
      pheAllowanceUnit: getPheAllowanceUnit(consumptionType),
    };

    dispatch(createPheAllowance(body))
      .unwrap()
      .then(() => {
        const formattedDate = new Date(selectedDietDate).toISOString();
        dispatch(fetchDailyConsumedPheAllowance(formattedDate));
        dispatch(fetchPheAllowances());
        setPheAllowanceAmount("");
      })
      .catch((error) => {
        // handle error if needed
      });
  };

  // Validate input allowing up to 8 digits before decimal and up to 2 after
  const handlePheAllowanceChange = (text: string) => {
    const regex = /^(\d{0,8})(\.?(\d{0,2})?)?$/;
    if (regex.test(text)) {
      setPheAllowanceAmount(text);
      if (text.trim() !== "") {
        setShowInputError(false);
      } else {
        setShowInputError(true);
      }
    }
  };

  // Dynamically calculate maxLength:
  // - If input contains a decimal, allow 11 characters (8 digits + '.' + 2 digits)
  // - Otherwise, if input length is exactly 8 (i.e. 8 digits), allow one more character for a decimal
  // - For less than 8 digits, cap at 8.
  const computeMaxLength = useCallback(() => {
    if (pheAllowanceAmount.includes(".")) {
      return 11;
    }
    return pheAllowanceAmount.length === 8 ? 9 : 8;
  }, [pheAllowanceAmount]);

  return (
    <SafeScreen>
      <View style={Common.appMarginHorizontal}>
        <Header
          title={`${consumptionType === "Protein" ? "PRO" : "Phe"} Allowance`}
          isEditing={true}
          isDark={variant === "dark"}
          onBackPress={openProfile}
        />
        <View style={styles.currentPheView}>
          <Typography.B1
            style={[Common.textBold, { color: colors.pheTextPrimary }]}
          >
            Current {consumptionType === "Protein" ? " PRO" : "Phe"}:{" "}
            {currentPheAllowance}
          </Typography.B1>
        </View>

        <Typography.B2 style={styles.updateLabel}>
          Update {consumptionType === "Protein" ? "PRO" : "Phe"} Allowance
        </Typography.B2>
        <View
          style={[
            styles.pheInputContent,
            isFocused && styles.pheTextInputFocused,
          ]}
        >
          <CustomTextInput
            value={pheAllowanceAmount}
            onChangeText={handlePheAllowanceChange}
            placeholder={`Enter ${consumptionType === "Protein" ? "PRO" : "Phe"} allowance`}
            keyboardType="numeric"
            style={styles.pheInputView}
            inputStyle={styles.pheTextInput}
            onFocus={handleFocus}
            onBlur={handleBlur}
            maxLength={computeMaxLength()}
          />

          <Typography.B2 style={styles.pheUnitText}>
            {getPheAllowanceUnit(consumptionType)}
          </Typography.B2>

          <Button.Main
            onPress={updatePheAllowance}
            style={styles.pheUpdateButton}
          >
            <Typography.B2 style={[Common.textBold, { color: colors.white }]}>
              Update
            </Typography.B2>
          </Button.Main>
        </View>

        {showInputError && !pheAllowanceAmount.trim() && (
          <Typography.B2 style={styles.errorText}>
            Please specify {consumptionType === "Protein" ? "PRO" : "Phe"}{" "}
            allowance
          </Typography.B2>
        )}

        <View style={styles.pheLogView}>
          <View style={styles.logTitle}>
            <Typography.H3 style={Common.textBold}>History</Typography.H3>
          </View>

          <View style={styles.headingRow}>
            <View style={styles.logDateCol}>
              <Typography.B2>Date</Typography.B2>
            </View>
            <View style={styles.logPheCol}>
              <Typography.B2>
                {consumptionType === "Protein" ? "PRO" : "Phe"} Allowance
              </Typography.B2>
            </View>
          </View>

          <FlatList
            data={Array.isArray(pheAllowanceData) ? pheAllowanceData : []}
            keyExtractor={(item, index) =>
              item?.id ? item.id.toString() : index.toString()
            }
            renderItem={({ item }) => {
              const formattedDate = item?.dateAdded
                ? formatDate(item.dateAdded)
                : "N/A";
              const allowanceAmount = getPheAllowanceValue(
                item?.amount || 0,
                consumptionType
              );
              const allowanceUnit = getPheAllowanceUnit(consumptionType);
              return (
                <View style={styles.contentView}>
                  <View style={styles.contentRow}>
                    <View style={styles.logDateColStart}>
                      <Typography.B2>{formattedDate}</Typography.B2>
                    </View>
                    <View style={styles.logPheColStart}>
                      <Typography.B2>{`${allowanceAmount} ${allowanceUnit}`}</Typography.B2>
                    </View>
                  </View>
                </View>
              );
            }}
            ListEmptyComponent={
              <View style={styles.contentView}>
                <Typography.B2>
                  No {consumptionType === "Protein" ? "PRO" : "Phe"} Allowance
                  data available
                </Typography.B2>
              </View>
            }
          />
        </View>
      </View>

      {loading && <Loading />}
    </SafeScreen>
  );
};

export default PheAllowanceScreen;
