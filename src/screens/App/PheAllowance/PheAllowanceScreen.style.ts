import { Fonts } from "@/constants";
import { Theme } from "@/types/theme/theme";
import { RFValue } from "react-native-responsive-fontsize";
import { ms, ScaledSheet } from "react-native-size-matters";

const getPHEAllowanceStyle = (theme: Theme) =>
  ScaledSheet.create({
    pheScrollView: { paddingBottom: "36@vs", rowGap: 18 },
    pheLogView: {
      width: "100%",
      borderRadius: "16@ms",
      paddingVertical: "20@ms",
      backgroundColor: theme.colors.tile_bg,
      rowGap: 18,
      marginTop: ms(10),
      elevation: 1,
    },
    pheInputContent: {
      width: "100%",
      alignItems: "center",
      justifyContent: "space-between",
      borderRadius: 8,
      borderWidth: 1,
      height: ms(40.5),
      flexDirection: "row",
      borderColor: theme.colors.gray,
      marginBottom: ms(5),
    },
    pheTextInput: {
      flex: 1,
      fontSize: RFValue(13.5),
      color: theme.colors.textPrimary,
      fontFamily: Fonts.RALEWAY_REGULAR,
    },
    pheInputView: { flex: 1, paddingLeft: ms(12), borderWidth: 0 },
    logTitle: { paddingHorizontal: "28@ms" },
    headingRow: {
      width: "95%",
      height: "36@vs",
      flexDirection: "row",
      paddingHorizontal: "28@ms",
      justifyContent: "space-between",
      alignSelf: "center",
      backgroundColor: theme.colors.pheGray,
      borderRadius: 8,
    },
    logDateCol: { flex: 0.56, justifyContent: "center" },
    logPheCol: { flex: 0.38, justifyContent: "center" },

    logDateColStart: { flex: 0.585, justifyContent: "flex-start" },
    logPheColStart: { flex: 0.36, justifyContent: "flex-start" },
    contentView: {
      width: "100%",
      height: "28@vs",
      flexDirection: "row",
      paddingHorizontal: "28@ms",
    },
    contentRow: {
      flex: 1,
      flexDirection: "row",
      justifyContent: "space-between",
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.gray,
      marginTop: ms(6),
    },
    currentPheView: { marginVertical: ms(10) },
    updateLabel: { marginBottom: ms(10) },
    pheUnitText: {
      color: theme.colors.textPrimaryYellow,
      marginHorizontal: ms(10),
      fontFamily: Fonts.RALEWAY_REGULAR,
    },
    pheUpdateButton: {
      width: ms(85),
      height: ms(40.5),
      borderRadius: 8,
      paddingVertical: 0,
      paddingHorizontal: 0,
      left: 1,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: theme.colors.primary,
    },
    pheTextInputFocused: { borderColor: theme.colors.yellow, borderWidth: 1 },
    errorText: { color: theme.colors.red, marginBottom: ms(5) },
  });

export default getPHEAllowanceStyle;
