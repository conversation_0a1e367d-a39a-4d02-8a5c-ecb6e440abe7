// MedicationFormulaScreen.styles.ts
import { config } from "@/theme/_config";
import { ms, ScaledSheet } from "react-native-size-matters";

const styles = ScaledSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  headerContainer: {
    marginHorizontal: ms(20),
  },

  footerContainer: {
    flexDirection: "row",
    marginVertical: ms(10),
  },
  contentContainer: {
    paddingHorizontal: ms(10),
  },
  cardSpace: {
    marginVertical: ms(1),
  },
  cardBigSpace: {
    marginVertical: ms(5),
  },
});

export default styles;
