/** @format */

import { RouteProp } from "@react-navigation/native";
import type { StackNavigationProp, StackScreenProps } from "@react-navigation/stack";

export type RootStackParamList = {
  Onboarding: undefined;
  Auth: undefined;
  App: undefined;
  UserPreferences: undefined;
};

export type RootScreenProps<S extends keyof RootStackParamList = keyof RootStackParamList> = StackScreenProps<
  RootStackParamList,
  S
>;

export type AuthStackParamList = {
  InitialScreen: undefined;
  LoginScreen: { isSignup: boolean | undefined };
  DisclaimerScreen: { isSignup: boolean | undefined }
};

export type AuthNavProp = StackNavigationProp<AuthStackParamList, "InitialScreen">;
export type LoginScreenRouteProp = RouteProp<AuthStackParamList, "LoginScreen">;
