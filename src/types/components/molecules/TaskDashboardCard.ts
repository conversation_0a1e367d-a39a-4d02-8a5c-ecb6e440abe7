export interface Task {
  medicineName: string;
  activeIngredient: string;
  intakeTime: string;
  fromDate: string;
  toDate: string;
  iconId: number;
  period: 'Morning' | 'Afternoon' | 'Evening';
  taskDate: string;
  id: number;
  isCompleted: boolean;
  isFormula: boolean;
  quantity: number;
  strength: number;
  medicineTypeId: number;
  taskId: number;
  patientRoutineDetailList?: any[];
  formulaUnitId?: number;
}
