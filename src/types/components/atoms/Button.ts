import { PropsWithChildren } from "react";
import { StyleProp, ViewStyle } from "react-native";
import { getButtonStyle } from "@/components/atoms/Button/Button.style";

export type ButtonStyleKeys = keyof ReturnType<typeof getButtonStyle>;

export interface AuthButtonProps {
  onPress?: () => void | Promise<void>;
  children: PropsWithChildren;
  style?: StyleProp<ViewStyle>;
  disabled?: boolean;
}
