import { PropsWithChildren } from "react";
import { KeyboardTypeOptions, TextStyle, StyleProp } from "react-native";
import { getTextInputStyle } from "@/components/atoms/CustomTextInput/CustomTextInput.style";

export type TextInputStyleKeys = keyof ReturnType<typeof getTextInputStyle>;

export interface TextInputProps extends PropsWithChildren {
  style?: StyleProp<TextStyle>;
  value?: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  keyboardType?: KeyboardTypeOptions;
  maxLength?: number;
}
