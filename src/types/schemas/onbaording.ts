export interface SurveyItem {
  question: string;
  response: string;
}

export interface PostSurveyPayload {
  survey: SurveyItem[];
  roleId: number
}

export interface IntentionQuestion {
  id: number;
  question: string;
}

export interface SurveyQuestionResponse {
  value: IntentionQuestion[];
  formatters: any[];       
  contentTypes: any[];
  declaredType: string | null;
  statusCode: number;
}