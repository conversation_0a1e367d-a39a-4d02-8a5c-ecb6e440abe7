export interface LabPayload {
  id?: number;
  pheLevel: number;
  pheMetrics: number;
  sampleDate: string;
  testDate: string;
}

export interface PheResult {
  id: number; // Unique identifier for the result
  pheMicromolePerLiter: number; // Phe value in micromoles per liter
  pheMilligramPerDeciliter: number; // Phe value in milligrams per deciliter
  pheMetrics: number; // Metric used (e.g., 1 for μmol/L, 2 for mg/dl)
  sampleDate: string; // Date of the sample in ISO format
  testDate: string; // Date of the test in ISO format
}
export interface DateRange {
  fromDate: string;
  toDate: string;
}

export enum DateRangeLabel {
  Last7Days = 'Last 7 days',
  Last14Days = 'Last 14 days',
  Last30Days = 'Last 30 days',
  Last90Days = 'Last 90 days',
  Last6Months = 'Last 6 months',
  Last12Months = 'Last 12 months',
  Custom = 'Custom',
}
