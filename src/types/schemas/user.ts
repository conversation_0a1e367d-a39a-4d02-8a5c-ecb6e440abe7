export interface User {
  name: string;
  email: string;
  nickName?: string;
  phoneNumber?: string;
  profilePictureUrl?: string;
  userSubId?: string;
  timeZoneId?: string;
  onBoarded: boolean;
  subscribedToUpdates: boolean;
  dataUsageConsent: boolean;
  isSimplifiedDiet?: boolean;
  isCareTaker?: boolean;
  deviceTimeZone?: string;
  timeZoneToggle?: boolean;
}

export interface UserOnboarding {
  name: string;
  email: string;
  phoneNumber: string;
  isCareTaker: boolean;
  pheAllowance: number;
  agreedToTerms: boolean;
  subscribedToUpdates: boolean;
  dataUsageConsent: boolean;
  zoneId: string;
}
