
export interface MedicationCategory {
  medicationTypeId: number;
  name: string;
  statusId: number | null;
  medicines: any[];
  patientRoutines: any[];
  status: any | null;
}

export interface IFrequency {
  id: number;
  text: string;
  isCustom?: boolean;
}

export interface IReminderTime {
  id: number;
  text: string;
}

export interface IWeekIndex {
  id: number;
  text: string;
}

export interface IDay {
  id: number;
  text: string;
}

export interface FrequencyResponse {
  frequencies: IFrequency[];
  reminderTimes: IReminderTime[];
  weekIndexes: IWeekIndex[];
  days: IDay[];
}

export interface MedicationTask {
  medicineName: string;
  activeIngredient: string;
  intakeTime: string;
  fromDate: string;
  toDate: string | null;
  iconId: number;
  period: string;
  upcoming: string;
  id: number;
}

export interface MedicationTask {
  medicineName: string;
  activeIngredient: string;
  intakeTime: string;
  fromDate: string;
  toDate: string | null;
  iconId: number;
  period: string;
  upcoming: string;
  id: number;
}

export interface FormulaTask {
  intakeTime: string;
  formulaName: string;
  fromDate: string;
  toDate?: string;
  period: string;
  upcoming: string;
  id: number;
}

export enum TaskStatus {
  IDLE = "idle",
  LOADING = "loading",
  SUCCESS = "success",
  ERROR = "error",
}
export enum Sections {
  MEDICATION = 1,
  FORMULA = 2,
}


export interface ITaskStatus {
  medCategories: TaskStatus;
  medicationTasks: TaskStatus;
  formulaTasks: TaskStatus;
  frequencies: TaskStatus;
  deleteTask: TaskStatus;
  fetchTaskById: TaskStatus;
  fetchFormulaById: TaskStatus;
  createMedicationStatus: TaskStatus;
  createFormulaTaskStatus: TaskStatus;
  updateMedicationDetails: TaskStatus;
  updateFormulaStatus: TaskStatus;
  formulaUnitListStatus: TaskStatus;
}

// CustomScheduleDetails Type
export interface CustomScheduleDetails {
  frequencyTypeId: number;
  interval: number;
  monthDates: number[];
  months: number[] | null;
  weekDays: number[];
  weekIndex: number | null;
}

// Task Details Type
export interface TaskDetails {
  medicineName: string;
  activeIngredient: string;
  categoryTypeId: number;
  quantity: number;
  strength: number;
  frequencyTypeId: number;
  intakeTime: string; // e.g., "01:30:00"
  alertBefore: number;
  fromDate: string; // ISO date string
  toDate: string | null; // Can be null
  iconId: number;
  id: number;
  customScheduleDetails: CustomScheduleDetails;
  medicationDetails: Array<{
    quantity: string | number;
    strength: string | number;
  }>;
}

// Formula Details Type
export interface FormulaDetails {
  frequencyTypeId: number;
  intakeTime: string; // e.g., "14:20:00"
  alertBefore: number;
  fromDate: string; // ISO date string
  toDate: string | null; // Can be null
  id: number;
  customScheduleDetails: CustomScheduleDetails;
}

export interface CreateMedicationDetailsPayload {
  id?: number;
  medicineName: string;
  activeIngredient: string;
  categoryTypeId: number;
  quantity: number;
  strength: number;
  frequencyTypeId: number;
  intakeTime: string;
  alertBefore: number;
  fromDate: string;
  toDate: string | null;
  iconId: number;
  customScheduleDetails: CustomScheduleDetails;
}

export interface UpdateMedicationDetailsPayload extends CreateMedicationDetailsPayload {
  id: number
}
export interface UpdateFormulaDetailsPayload extends FormulaDetails {
}

export interface CustomScheduleDetails {
  frequencyTypeId: number;
  interval: number;
  monthDates: number[];
  months: number[] | null;
  weekDays: number[];
  weekIndex: number | null;
}

export interface CreateFormulaDetailsPayload {
  id?: number;
  frequencyTypeId: number;
  intakeTime: string;
  alertBefore: number;
  fromDate: string;
  toDate: string;
  customScheduleDetails: CustomScheduleDetails;
}

export interface TaskState {
  status: ITaskStatus;
  error?: string;
  categories: MedicationCategory[];
  frequencies: IFrequency[];
  reminderTimes: IReminderTime[];
  weekIndexes: IWeekIndex[];
  days: IDay[];
  medicationTasks: MedicationTask[];
  formulaTasks: FormulaTask[];
  taskDetails: TaskDetails | null;
  formulaDetails: FormulaDetails | null;
  createdMedicationId: number | null;
  updatedRecordId: number | null;
  fromUpdate: number | null;
  formulaUnitList: []
}
