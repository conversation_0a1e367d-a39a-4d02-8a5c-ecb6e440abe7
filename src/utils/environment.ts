/**
 * Environment types supported by the application
 */
export enum Environment {
  DEVELOPMENT = 'dev',
  UAT = 'qa',
  PRODUCTION = 'prod',
  UNKNOWN = 'unknown'
}

export enum VERSION {
  versionOne = 'v1',
  versionTwo = 'v2',
}


export const BASE_URL = `https://api-cycle-${Environment.UAT}.azurewebsites.net/api/${VERSION.versionTwo}`;

/**
 * Determines the current environment based on the BASE_URL
 * @returns The current environment
 */
export function getEnvironment(): Environment {  
  if (!BASE_URL) {
    console.warn('BASE_URL is not defined in environment variables');
    return Environment.UNKNOWN;
  }

  if (BASE_URL.includes('cyclevita-pku.com')) {
    return Environment.PRODUCTION;
  } else if (BASE_URL.includes('api-cycle-qa.azurewebsites.net')) {
    return Environment.UAT;
  } else if (BASE_URL.includes('api-cycle-dev.azurewebsites.net')) {
    return Environment.DEVELOPMENT;
  }

  console.warn(`Unknown environment for BASE_URL: ${BASE_URL}`);
  return Environment.UNKNOWN;
}

/**
 * Checks if the current environment is production
 * @returns true if the current environment is production, false otherwise
 */
export function isProduction(): boolean {
  return getEnvironment() === Environment.PRODUCTION;
}

/**
 * Checks if the current environment is UAT
 * @returns true if the current environment is UAT, false otherwise
 */
export function isUAT(): boolean {
  return getEnvironment() === Environment.UAT;
}

/**
 * Checks if the current environment is development
 * @returns true if the current environment is development, false otherwise
 */
export function isDevelopment(): boolean {
  return getEnvironment() === Environment.DEVELOPMENT;
}

