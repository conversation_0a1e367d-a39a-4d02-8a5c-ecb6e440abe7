import { MMKV } from 'react-native-mmkv';

class MMKVStorage {
  private storage: MMKV;

  constructor() {
    this.storage = new MMKV();
  }

  setItem = async (key: string, value: string): Promise<boolean | undefined> => {
    this.storage.set(key, value)
    return Promise.resolve(true)
  };

  getItem = async (key: string): Promise<string | undefined> => {
    const value = this.storage.getString(key)
    return Promise.resolve(value)
  };

  removeItem = async (key: string): Promise<void> => {
    this.storage.delete(key)
    return Promise.resolve()
  };

  clear = async (): Promise<void> => {
    try {
      this.storage.clearAll();
    } catch (error) {
      console.error('Error clearing storage:', error);
      throw error;
    }
  };

  multiGet = async (keys: string[]): Promise<[string, string | null][]> => {
    try {
      const result: [string, string | null][] = keys.map((key) => {
        const value = this.storage.getString(key) ?? null;
        return [key, value];
      });
      return result;
    } catch (error) {
      console.error('Error getting multiple items:', error);
      throw error;
    }
  };

  multiSet = async (keyValuePairs: [string, string][]): Promise<void> => {
    try {
      keyValuePairs.forEach(([key, value]) => {
        this.storage.set(key, value);
      });
    } catch (error) {
      console.error('Error setting multiple items:', error);
      throw error;
    }
  };
}

export default new MMKVStorage();
