import { getTimeZoneOffsetDiffAdvanced } from './timezoneUtils';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

import { store } from '@/store';
import { updateUserTimeZoneThunk, setProfileTimeZone, setShowTimeZoneModal, setProfileTzToggle, updateDeviceTimeZoneThunk, updateTimeZoneToggleThunk } from '@/store/slices/settingsSlice';
import { fetchUserById } from '@/store/slices/userSlice';
import { fetchDailyTasks } from '@/store/slices/dashboardSlice';
import { fetchFormulaTasks, fetchMedicationTasks } from '@/store/slices/taskManager/taskManager.middleware';


dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * Timezone service that handles device vs profile timezone logic
 */
export class TimezoneService {
  /**
   * Get the current device timezone
   */
  static getDeviceTimeZone(): string {
    return Intl?.DateTimeFormat()?.resolvedOptions()?.timeZone || 'UTC';
  }

  /**
   * Check if a timezone is empty or UTC
   */
  static isEmptyOrUTC(timezone: string | undefined | null): boolean {
    return !timezone || timezone === 'UTC' || timezone.trim() === '';
  }

  /**
   * Initialize timezone handling based on the requirements:
   * - If profile timezone is empty/UTC, use device timezone and update user
   * - If profile timezone exists, compare with device timezone and show modal if different
   */
  static async initializeTimezone(): Promise<void> {
    const deviceTimeZone = this.getDeviceTimeZone();
    const state = store.getState();
    const devicePreTimeZone = state.settings.profileTimeZone;
    const userTimeZone = state.user.user?.timeZoneId;

    // Case 1: Profile timezone is empty or UTC
    if (this.isEmptyOrUTC(userTimeZone)) {

      // Update the profile timezone in Redux to device timezone
      if (devicePreTimeZone !== deviceTimeZone) {
        store.dispatch(setProfileTimeZone(deviceTimeZone));
      }

      // Update user timezone via API
      try {
        await store.dispatch(updateUserTimeZoneThunk({ timezoneId: deviceTimeZone })).unwrap();
      } catch (error) {
        console.error('🕐 TimezoneService: Failed to update user timezone:', error);
      }
      return;
    }

    if (devicePreTimeZone != deviceTimeZone && userTimeZone == deviceTimeZone) {
      store.dispatch(setProfileTimeZone(deviceTimeZone));
    }

    if (devicePreTimeZone != deviceTimeZone && userTimeZone != deviceTimeZone) {
      store.dispatch(setShowTimeZoneModal(true));
      // store.dispatch(updateUserTimeZoneThunk({ timezoneId: deviceTimeZone })).unwrap()
    }

  }

  /**
   * Check timezone on screen focus - specifically for HomeScreen focus events
   * This method is optimized for focus events and handles the case where
   * device timezone might have changed while the app was in background
   */
  static async checkTimezoneOnFocus(): Promise<void> {
    const deviceTimeZone = this.getDeviceTimeZone();
    const state = store.getState();
    const profileTimeZone = state.settings.profileTimeZone;
    const userTimeZone = state.user.user?.timeZoneId;
    // Only proceed if we have user data and the timezones are different
    // Only show modal if profileTimeZone !== current detected device timezone
    if (userTimeZone && (profileTimeZone !== deviceTimeZone)) {
      if (userTimeZone !== deviceTimeZone) {
        store.dispatch(setShowTimeZoneModal(true));
        // store.dispatch(updateUserTimeZoneThunk({ timezoneId: deviceTimeZone })).unwrap()
      }
    }
  }

  /**
   * Handle timezone change when user chooses to update to new timezone
   */
  static async updateToNewTimeZone(newTimeZone: string, isModal?: boolean, restrictFetchingUser?: boolean, isTimeOut?: boolean): Promise<void> {

    try {
      // store.dispatch(setProfileTzToggle(false));

      /* 
      **isModal** is used as indication that the user has interacted with the Timezone modal at Home Screen.

      **if (!isModal)** then updateUserTimeZoneThunk will be called. 
      Which means user has manually updated the timezone from TimezoneOptions component.

      */

      if (!this.getTimezoneProps().isKeepHomeTzVisible && !isTimeOut) {
        store.dispatch(updateTimeZoneToggleThunk(false))
      }
      if (isModal) {
        store.dispatch(setProfileTimeZone(newTimeZone));
        await store.dispatch(updateTimeZoneToggleThunk(false)).unwrap();
      }

      if (!isModal) {
        await store.dispatch(updateUserTimeZoneThunk({ timezoneId: newTimeZone, restrictFetchingUser: restrictFetchingUser })).unwrap();

      }

      if (isTimeOut) {
        // setTimeout(async () => {
        //   await store.dispatch(updateDeviceTimeZoneThunk()).unwrap();
        // }, 2500);
        //chore: we check this later
      }
      else {
        await store.dispatch(updateDeviceTimeZoneThunk()).unwrap();
      }

      store.dispatch(setShowTimeZoneModal(false));
    } catch (error) {
      throw error;
    }
  }

  /**
   * Handle timezone change when user chooses to keep current timezone
   * This updates the device timezone to match the detected timezone
   */
  static async keepCurrentTimeZone(): Promise<void> {
    const deviceTimeZone = this.getDeviceTimeZone();
    store.dispatch(setProfileTimeZone(deviceTimeZone));
    // Update the profile timezone in Redux to match the detected device timezone

    await Promise.all([
      store.dispatch(updateTimeZoneToggleThunk(true)).unwrap(),
      store.dispatch(updateDeviceTimeZoneThunk()).unwrap()
    ]);
    // Hide the modal
    store.dispatch(setShowTimeZoneModal(false));
  }

  /**
   * Refresh user data and re-initialize timezone
   */
  static async refreshUserAndTimezone(): Promise<void> {
    try {
      await store.dispatch(fetchUserById()).unwrap();
      await this.initializeTimezone();
    } catch (error) {
      console.error('🕐 TimezoneService: Failed to refresh user and timezone:', error);
    }
  }


  /**
   * Convert a time from a source timezone to a target timezone.
   * This function correctly handles timezone conversions, including DST,
   * by leveraging dayjs's timezone plugin which uses the IANA timezone database.
   *
   * @param intakeTime - The time to convert. Can be a time string (e.g., '20:00' or '8:00 PM') or a Date object.
   *   - If a string, it's interpreted as a time in the `sourceTz` on the current date.
   *   - If a Date object, its local time components (e.g., getHours()) are interpreted as being in the `sourceTz`.
   * @param sourceTz - The IANA timezone of the intakeTime (e.g., 'Asia/Karachi').
   * @param targetTz - The IANA timezone to convert to (e.g., 'Asia/Kolkata').
   * @param format - The desired output format string for the converted time.
   * @returns The converted time as a formatted string (e.g., '4:04 PM').
   */
  static convertToHomeTime(
    intakeTime: string | Date,
    sourceTz: string, // Renamed from deviceTz
    targetTz: string, // Renamed from homeTz
    format: string = 'h:mm A',
    isuUtcTimestamp: boolean = false
  ): string | number {
    let intakeDate;

    if (typeof intakeTime === 'string') {
      const today = dayjs().tz(sourceTz);
      const timeStr = `${today.format('YYYY-MM-DD')} ${intakeTime}`;
      // Try parsing with multiple formats
      intakeDate = dayjs.tz(timeStr, 'YYYY-MM-DD h:mm a', sourceTz);
      if (!intakeDate.isValid()) {
        intakeDate = dayjs.tz(timeStr, 'YYYY-MM-DD HH:mm', sourceTz);
      }
    } else {
      intakeDate = dayjs(intakeTime);
    }
    if (!intakeDate.isValid()) {
      console.error('convertToHomeTime: Could not parse the provided intakeTime:', intakeTime);
      return 'Invalid Time';
    }
    const offset = getTimeZoneOffsetDiffAdvanced(sourceTz, targetTz, 'convertToHomeTime');

    if (isNaN(offset.diffHours)) {
      return "N/A";
    }

    const homeTime = intakeDate.add(offset.diffHours, 'hours');

    if (isuUtcTimestamp) {
      return homeTime.utc().valueOf();
    }

    return homeTime.format(format);
  }


  static getTimezoneProps() {
    const state = store.getState();
    const userTimeZone = state.user?.user?.timeZoneId;
    const { timeZones, profileTimeZone, profileTzToggle } = state.settings || {};

    const isKeepHomeTzVisible = userTimeZone !== this.getDeviceTimeZone();
    const currentDeviceTimeZone = this.getDeviceTimeZone();

    const convertToZoneId = (currentDeviceTimeZone !== userTimeZone && profileTzToggle) ? currentDeviceTimeZone : "";

    return {
      profileTimeZone,
      userTimeZone,
      timeZones,
      currentDeviceTimeZone,
      convertToZoneId,
      isKeepHomeTzVisible
    };
  }

  static convertToHomeTimeUtc(intakeTime: string): string {
    const state = store.getState();
    const userTimeZone = state.user?.user?.timeZoneId || 'UTC';
    const { profileTzToggle } = state.settings || {};

    /*
    profileTzToggle 
    
    TOGGLE OFF --> SEND TIME PICKER VALUE TO THE POST API
    
    TOGGLE ON --> SEND CONVERTED HOME VALUE TO THE POST API

    TOGGLE HIDE || TZ SAME --> TIME PICKER VALUE TO THE POST API
 
    */

    const isTzDiff = userTimeZone !== this.getDeviceTimeZone();
    if (isTzDiff && !profileTzToggle || !isTzDiff) {
      return intakeTime;
    }

    if (isTzDiff && profileTzToggle) {
      const homeTime = this.convertToHomeTime(intakeTime, this.getDeviceTimeZone(), userTimeZone, '', true);
      return homeTime as any;
    }
  }

  static updateTzDependentAPIs() {
    const dispatch = store.dispatch;

    const selectedDate = dayjs().toISOString();
    dispatch(
      fetchDailyTasks(
        dayjs(selectedDate).endOf("day").format("YYYY-MM-DDT00:00:00.000")
      )
    );

    dispatch(fetchMedicationTasks());
    dispatch(fetchFormulaTasks());
  }
}
