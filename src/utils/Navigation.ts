import * as React from 'react';
import { CommonActions, DrawerActions, NavigationContainerRef } from '@react-navigation/native';
import { RootStackParamList } from '@/types/navigation';

export const navigationRef = React.createRef<NavigationContainerRef<RootStackParamList>>();

export function navigate<RouteName extends keyof RootStackParamList>(
  name: RouteName, 
  params?: RootStackParamList[RouteName]
) {
  navigationRef.current?.navigate(name, params);
}

export function navigateAndReset(routes: Array<{ name: keyof RootStackParamList }>, index = 0) {
  navigationRef.current?.dispatch(
    CommonActions.reset({
      index,
      routes,
    })
  );
}

export function navigateAndSimpleReset(name: keyof RootStackParamList, index = 0) {
  navigationRef.current?.dispatch(
    CommonActions.reset({
      index,
      routes: [{ name }],
    })
  );
}

export function openDrawer() {
  navigationRef.current?.dispatch(DrawerActions.openDrawer());
}

export function closeDrawer() {
  navigationRef.current?.dispatch(DrawerActions.closeDrawer());
}
