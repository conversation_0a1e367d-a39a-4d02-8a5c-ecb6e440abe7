import { MMKV } from "react-native-mmkv";
import DeviceInfo from "react-native-device-info";

const storage = new MMKV();
const STORAGE_KEY = "version_history_cycle_v";

export const getVersionHistory = (): string[] => {
    const data = storage.getString(STORAGE_KEY);
    return data ? JSON.parse(data) : [];
};

export const checkAndStoreCurrentVersion = (): boolean => {
    const currentVersion = DeviceInfo.getVersion();
    const history = getVersionHistory();
    const isNew = !history.includes(currentVersion);
    if (isNew) {
        history.push(currentVersion);
        storage.set(STORAGE_KEY, JSON.stringify(history));
    }

    return isNew;
};

export const clearVersionHistory = () => {
    storage.delete(STORAGE_KEY);
};

export const addVersionManually = (version: string) => {
    const history = getVersionHistory();
    if (!history.includes(version)) {
        history.push(version);
        storage.set(STORAGE_KEY, JSON.stringify(history));
    }
};

