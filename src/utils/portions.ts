
// Portion size base values
export const PORTION_SIZES = {
  Servings: 200,
  Serving: 200,
  Cups: 200,
  Cup: 200,
  Slices: 20,
  Slice: 20,
  Pieces: 20,
  Piece: 20,
  Tablespoons: 15,
  Tablespoon: 15,
  Teaspoons: 5,
  Teaspoon: 5,
  G: 1,
  g: 1,
  Oz: 28.3495,

  servings: 200,
  serving: 200,
  cups: 200,
  cup: 200,
  slices: 20,
  slice: 20,
  pieces: 20,
  piece: 20,
  tablespoons: 15,
  tablespoon: 15,
  teaspoons: 5,
  teaspoon: 5,
  oz: 28.3495,
};

type Portion = keyof typeof PORTION_SIZES;

export interface FoodPortion {
  portionName: Portion;
  baseGrams: number; // Grams for the API
}

export interface NutritionalValues {
  phe: number; // Phenylalanine in mg
  protein: number; // Protein in grams
  servings: number; // Serving size (grams)
}

export class PortionConverter {
  /**
   * Convert a portion size to grams
   * @param portionName Portion name (e.g., Cups, Tablespoons)
   * @param quantity Quantity of the portion
   * @returns Equivalent weight in grams
   */
  static toGrams(
    portionName: string,
    quantity: number,
    portions?: any[]
  ): number {
    if (portions?.length) {
      const portion = portions.find((p) => p.text === portionName);
      const baseGrams = portion?.gramWeight || PORTION_SIZES[portionName] || 0;
      return baseGrams * quantity;
    }
    const baseGrams = PORTION_SIZES[portionName] || 0;
    return baseGrams * quantity;
  }

  /**
   * Convert grams to a portion size
   * @param portionName Portion name (e.g., Cups, Tablespoons)
   * @param grams to convert
   * @returns Equivalent quantity in the given portion size
   */
  static fromGrams(
    portionName: string,
    grams: number,
    portions?: any[]
  ): number {
    if (portions?.length) {
      const portion = portions.find((p) => p.text === portionName);
      const baseGrams = portion?.gramWeight || PORTION_SIZES[portionName] || 0;
      return grams / baseGrams;
    }
    const baseGrams = PORTION_SIZES[portionName] || 0;
    return grams / baseGrams;
  }

  /**
   * Update nutritional values based on a new value for Phe, Protein, or Servings
   * @param values Current nutritional values
   * @param field Field to update (phe, protein, servings)
   * @param newValue New value for the field
   * @returns Updated nutritional values
   */
  static updateNutritionalValues(
    values: NutritionalValues,
    field: keyof NutritionalValues,
    newValue: number
  ): NutritionalValues {
    const { phe, protein, servings } = values;

    const protein_to_serving_factor = servings ? protein / servings : 0;
    const phe_to_serving_factor = servings ? phe / servings : 0;
    const serving_to_protein_factor = protein ? servings / protein : 0;
    const serving_to_phe_factor = phe ? servings / phe : 0;

    switch (field) {
      case "servings":
        return {
          servings: newValue,
          phe: newValue * phe_to_serving_factor,
          protein: newValue * protein_to_serving_factor,
        };
      case "protein":
        return {
          servings: newValue * serving_to_protein_factor,
          protein: newValue,
          phe: newValue * serving_to_protein_factor * phe_to_serving_factor,
        };
      case "phe":
        return {
          servings: newValue * serving_to_phe_factor,
          phe: newValue,
          protein: newValue * serving_to_phe_factor * protein_to_serving_factor,
        };
      default:
        throw new Error("Invalid field for update");
    }
  }
  static ConvertNutrientsValues({
    quantity,
    portion_gram_weight,
    protein,
    phe,
    field,
    phe_to_serving_factor,
    protein_to_serving_factor,
    serving_to_phe_factor,
    serving_to_protein_factor
  }: {
    quantity: number;
    portion_gram_weight: number;
    protein: number;
    protein_to_serving_factor: number;
    phe_to_serving_factor: number;
    serving_to_phe_factor: number;
    serving_to_protein_factor: number;
    phe: number;
    field: keyof NutritionalValues;
  }) {
    /*
        serving (g) = Item.quantity x portion.gram_weight
    */
    const servings = quantity * portion_gram_weight;

    switch (field) {
      case "servings":
        return {
          servings: quantity,
          phe: servings * phe_to_serving_factor,
          protein: servings * protein_to_serving_factor,
        };
      case "protein":
        return {
          protein: protein,
          servings: (protein * serving_to_protein_factor) / portion_gram_weight,
          phe: (protein * serving_to_protein_factor) * phe_to_serving_factor
        };
      case "phe":
        return {
          phe: phe,
          servings: (phe * serving_to_phe_factor) / portion_gram_weight,
          protein: (phe * serving_to_phe_factor) * protein_to_serving_factor
        };
      default:
        throw new Error("Invalid field for update");
    }
  }
}
