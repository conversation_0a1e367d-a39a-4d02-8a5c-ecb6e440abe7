import { Nutrient } from '@/types/schemas/dietTracker';

interface Portion {
	name: string;
	quantity: number;
	gram_weight: number;
}

interface Ingredient {
	_id: string;
	client_id: string;
	user_id: string | null;
	description: string;
	original_description: string;
	category: string;
	provider: string;
	quantity: number;
	unit: string;
	time: string;
	nutrients: Nutrient[];
	vector: null | any;
	portions: Portion[];
	ingredients: Ingredient[] | null;
}

interface MealEntry {
	description: string;
	category: string;
	quantity: number;
	unit: string;
	time: string;
	nutrients: Nutrient[];
	ingredients: Ingredient[];
	portions: Portion[];
	entryIds: string[];
	gram_weight: number;
}

/**
 * Adds missing "phe" nutrients to ingredients if not present.
 * Calculates "phe" as 50 mg per 1 g of "protein".
 * @param meal - The meal entry JSON object.
 * @returns The updated meal entry object.
 */
export function addMissingPheToIngredients(meal: MealEntry): MealEntry {
	const pheToProteinRatio = 50; // 50 mg of phe per 1 g of protein

	const hasPhe = meal.nutrients.some((nutrient) => nutrient.name.toLowerCase() === 'phe');

	if (!hasPhe) {
		const proteinNutrient = meal.nutrients.find((nutrient) => nutrient.name.toLowerCase() === 'protein');

		if (proteinNutrient) {
			// Calculate phe based on protein and given ratio
			const pheAmount = proteinNutrient.amount * pheToProteinRatio;
			const pheNutrient: Nutrient = {
				name: 'phe',
				amount: pheAmount,
				unit: 'mg',
			};

			// Append the new phe nutrient
			meal.nutrients.push(pheNutrient);
		}
	}

	return meal;
}
