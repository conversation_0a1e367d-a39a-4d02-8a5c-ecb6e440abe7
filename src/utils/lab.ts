import { DateRange, DateRangeLabel } from '@/types/schemas/labs';
import dayjs from 'dayjs';

export const getDateRangeFromLabel = (label: string): DateRange | null => {
  const today = dayjs().endOf('day');
  let fromDate: dayjs.Dayjs;

  switch (label) {
    case DateRangeLabel.Last7Days:
      fromDate = dayjs().subtract(6, 'day').startOf('day');
      break;
    case DateRangeLabel.Last14Days:
      fromDate = dayjs().subtract(13, 'day').startOf('day');
      break;
    case DateRangeLabel.Last30Days:
      fromDate = dayjs().subtract(29, 'day').startOf('day');
      break;
    case DateRangeLabel.Last90Days:
      fromDate = dayjs().subtract(89, 'day').startOf('day');
      break;
    case DateRangeLabel.Last6Months:
      fromDate = dayjs().subtract(6, 'month').startOf('day');
      break;
    case DateRangeLabel.Last12Months:
      fromDate = dayjs().subtract(12, 'month').startOf('day');
      break;
    case DateRangeLabel.Custom:
      return null; 
    default:
      return null;
  }

  return {
    fromDate: fromDate.format('YYYY-MM-DD'),
    toDate: today.format('YYYY-MM-DD'),
  };
};
