import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { store } from '@/store';
// Remove allTimeZones and timeZonesNames. Refactor getTimeZoneLabel to accept dynamic timeZones array.

dayjs.extend(utc);
dayjs.extend(timezone);

export interface ZoneEntry {
  id: string;              // IANA, e.g. "Asia/Karachi"
  offsetMinutes: number;   // e.g. 300  (= +05:00)
}

export function getTimeZoneLabel(tz: string, timeZones?: { id: string; displayName: string }[]): string {
  if (Array.isArray(timeZones) && timeZones.length > 0) {
    const found = timeZones.find((zone) => zone.id === tz);
    return found ? found.displayName : tz;
  }
  // fallback: show the IANA id if no data
  return tz;
}

export function getTimeZoneOffset(tz: string) {
  try {
    const now = new Date();
    const tzString = now.toLocaleString("en-US", { timeZone: tz });
    const tzDate = new Date(tzString);
    const localUtc = now.getTime() + now.getTimezoneOffset() * 60000;
    const tzUtc = tzDate.getTime() + tzDate.getTimezoneOffset() * 60000;
    const diffMinutes = (tzUtc - localUtc) / 60000;
    const offset = diffMinutes / 60;
    return offset;
  } catch {
    return 0;
  }
}

/**
 * Returns the offset difference (in hours and formatted string) between two timezones.
 * If a timeZones list is provided (with offsetMinutes), it uses that for accuracy; otherwise, falls back to dynamic calculation.
 */
export function getTimeZoneOffsetDiff(
  fromTz: string,
  toTz: string,
  timeZones?: { id: string; offsetMinutes: number }[]
): { diffHours: number; formatted: string } {
  let fromOffset: number;
  let toOffset: number;

  if (Array.isArray(timeZones) && timeZones.length > 0) {
    const from = timeZones.find((tz) => tz.id === fromTz);
    const to = timeZones.find((tz) => tz.id === toTz);
    fromOffset = from?.offsetMinutes ?? getTimeZoneOffset(fromTz) * 60;
    toOffset = to?.offsetMinutes ?? getTimeZoneOffset(toTz) * 60;
    // Convert to hours
    fromOffset = fromOffset / 60;
    toOffset = toOffset / 60;
  } else {
    fromOffset = getTimeZoneOffset(fromTz);
    toOffset = getTimeZoneOffset(toTz);
  }

  const diffHours = toOffset - fromOffset;
  let formatted: string;
  if (isNaN(diffHours)) {
    formatted = "N/A";
  } else if (diffHours === 0) {
    formatted = "0 hour";
  } else {
    formatted = `${diffHours > 0 ? "+" : ""}${diffHours} hour${Math.abs(diffHours) !== 1 ? "s" : ""}`;
  }
  return { diffHours, formatted };
}

export const getDeviceCurrentZone = () => Intl.DateTimeFormat().resolvedOptions().timeZone;


/**
 * @deprecated Use getTimeZoneOffsetDiff instead for more options and formatted output.
 */
export function getTimeZoneDiff(fromTz: string, toTz: string): number {
  const fromOffset = getTimeZoneOffset(fromTz);
  const toOffset = getTimeZoneOffset(toTz);
  return toOffset - fromOffset;
}


/**
 * DST‑aware diff between two IANA zones, using the supplied list as the
 * authoritative source on React‑Native.  Falls back to dayjs.tz if needed.
 */
export function getTimeZoneOffsetDiffAdvanced(
  fromTz: string,
  toTz: string,
  log: string
): { diffHours: number; formatted: string, fromMin: number, toMin: number } {
  let timeZones = store?.getState()?.settings?.timeZones;
  const lookup = (iana: string): number | undefined => {
    // From Noda Lib List
    const hit = timeZones.find((z) => z.id === iana);
    if (hit) return hit.offsetMinutes;

    return undefined;
  };

  const fromMin = lookup(fromTz);
  const toMin = lookup(toTz);

  if (fromMin === undefined || toMin === undefined) {
    return { diffHours: NaN, formatted: 'N/A', fromMin: NaN, toMin: NaN };
  }

  const diffMin = toMin - fromMin;   // + → toTz ahead of fromTz
  const diffHours = diffMin / 60;      // may be fractional (1.5, ‑5.75)

  if (diffMin === 0) return { diffHours, formatted: "0 hour", fromMin, toMin };

  const sign = diffMin > 0 ? '+' : '-';
  const absMin = Math.abs(diffMin);
  const h = Math.floor(absMin / 60);
  const m = absMin % 60;

  // const formatted =
  //   m === 0
  //     ? `${sign}${h} h`
  //     : `${sign}${h > 0 ? `${h} h ` : ''}${m} m`;

  const formatted = isNaN(diffHours) ? "N/A" : `${diffHours > 0 ? "+" : ""}${diffHours} hour${Math.abs(diffHours) !== 1 ? "s" : ""}`;


  return { diffHours, formatted, fromMin, toMin };
}

 export const formatLocalISOTimeZone= (date: Date): string => {
    const pad = (n: number) => n.toString().padStart(2, "0");
    return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())}`;
  };
