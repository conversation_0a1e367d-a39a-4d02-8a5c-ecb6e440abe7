import { TIME_SLOT } from '@/constants/timeSlots';
import { FoodItem, Portion, ResponseData } from '@/types/schemas/dietTracker';
import { PortionConverter } from './portions';

interface TimeRange {
	startTime: string; // HH:mm:ss
	endTime: string; // HH:mm:ss
}

export class FoodItemTransformer {
	private data: ResponseData[];

	// Time ranges for categorization
	private static timeRanges: Record<string, TimeRange> = TIME_SLOT;

	constructor(data: ResponseData[]) {
		this.data = data;
	}

	/**
	 * Categorizes fooditems into morning, afternoon, and evening based on the time of the top-level object.
	 */
	public categorizeFoodItems(): Record<
		string,
		{
			_id: string;
			time: string;
			client_id: string;
			user_id: string;
			description: string;
			fooditems: FoodItem[];
		}[]
	> {
		const categorized: Record<
			string,
			{
				_id: string;
				time: string;
				client_id: string;
				user_id: string;
				description: string;
				fooditems: FoodItem[];
			}[]
		> = {
			morning: [],
			afternoon: [],
			evening: [],
		};

		this.data.forEach((entry) => {
			const entryTime = entry.time.split('T')[1]; // Extract HH:mm:ss from ISO datetime
			const category = this.getTimeCategory(entryTime);
			if (category) {
				categorized[category].push(entry);
			}
		});

		return categorized;
	}

	/**
	 * Calculates total PHE of fooditems for each category (morning, afternoon, evening).
	 */
	public calculateTotalPHEByCategory(): Record<string, { totalPHE: number; unit: string | null }> {
		const categorized = this.categorizeFoodItems();
		const totalPHE: Record<string, { totalPHE: number; unit: string | null }> = {
			morning: { totalPHE: 0, unit: null },
			afternoon: { totalPHE: 0, unit: null },
			evening: { totalPHE: 0, unit: null },
		};

		for (const [category, entries] of Object.entries(categorized)) {
			let categoryUnit: string | null = null;

			totalPHE[category].totalPHE = entries.reduce((total, entry) => {
				return (
					total +
					entry.fooditems.reduce((sum: any, item: { nutrients: any[] }) => {
						const pheNutrient = item.nutrients.find((n: { name: string }) => n.name === 'phe');
						if (pheNutrient) {
							if (!categoryUnit) {
								categoryUnit = pheNutrient.unit;
							}
							return sum + pheNutrient.amount;
						}
						return sum;
					}, 0)
				);
			}, 0);

			totalPHE[category].unit = categoryUnit;
		}

		return totalPHE;
	}

	/**
	 * Calculates total protein of the fooditems separately based on morning, afternoon, and evening.
	 */
	public calculateTotalProteinByCategory(): Record<string, { totalProtein: number; unit: string | null }> {
		const categorized = this.categorizeFoodItems();
		const totalProtein: Record<string, { totalProtein: number; unit: string | null }> = {
			morning: { totalProtein: 0, unit: null },
			afternoon: { totalProtein: 0, unit: null },
			evening: { totalProtein: 0, unit: null },
		};

		for (const [category, entries] of Object.entries(categorized)) {
			let categoryUnit: string | null = null;

			totalProtein[category].totalProtein = entries.reduce((total, entry) => {
				return (
					total +
					entry.fooditems.reduce((sum, item) => {
						const proteinNutrient = item.nutrients.find((n) => n.name === 'protein');
						if (proteinNutrient) {
							if (!categoryUnit) {
								categoryUnit = proteinNutrient.unit;
							}
							return sum + proteinNutrient.amount;
						}
						return sum;
					}, 0)
				);
			}, 0);

			totalProtein[category].unit = categoryUnit;
		}

		return totalProtein;
	}

	public calculateTotalPHEAndProtein(
		separateByTimeSlot = false,
		timeSlot?: 'morning' | 'afternoon' | 'evening'
	):
		| {
			totalPHE: number;
			pheUnit: string | null;
			totalProtein: number;
			proteinUnit: string | null;
		}
		| Record<string, { totalPHE: number; pheUnit: string | null; totalProtein: number; proteinUnit: string | null }> {
		const categorized = this.categorizeFoodItems();

		if (timeSlot) {
			// Validate timeSlot
			if (!categorized[timeSlot]) {
				throw new Error(`Invalid timeSlot: ${timeSlot}. Valid options are "morning", "afternoon", "evening".`);
			}

			let totalPHE = 0;
			let pheUnit: string | null = null;
			let totalProtein = 0;
			let proteinUnit: string | null = null;

			categorized[timeSlot].forEach((entry) => {
				entry.fooditems.forEach((item) => {
					const pheNutrient = item.nutrients.find((n) => n.name === 'phe');
					if (pheNutrient) {
						totalPHE += pheNutrient.amount;
						if (!pheUnit) {
							pheUnit = pheNutrient.unit;
						}
					}

					const proteinNutrient = item.nutrients.find((n) => n.name === 'protein');
					if (proteinNutrient) {
						totalProtein += proteinNutrient.amount;
						if (!proteinUnit) {
							proteinUnit = proteinNutrient.unit;
						}
					}
				});
			});

			return { totalPHE, pheUnit, totalProtein, proteinUnit };
		}

		if (separateByTimeSlot) {
			const totalsByTimeSlot: Record<string, { totalPHE: number; pheUnit: string | null; totalProtein: number; proteinUnit: string | null }> = {
				morning: { totalPHE: 0, pheUnit: null, totalProtein: 0, proteinUnit: null },
				afternoon: { totalPHE: 0, pheUnit: null, totalProtein: 0, proteinUnit: null },
				evening: { totalPHE: 0, pheUnit: null, totalProtein: 0, proteinUnit: null },
			};

			for (const [timeSlot, entries] of Object.entries(categorized)) {
				entries.forEach((entry) => {
					entry.fooditems.forEach((item) => {
						const pheNutrient = item.nutrients.find((n) => n.name === 'phe');
						if (pheNutrient) {
							totalsByTimeSlot[timeSlot].totalPHE += pheNutrient.amount;
							if (!totalsByTimeSlot[timeSlot].pheUnit) {
								totalsByTimeSlot[timeSlot].pheUnit = pheNutrient.unit;
							}
						}

						const proteinNutrient = item.nutrients.find((n) => n.name === 'protein');
						if (proteinNutrient) {
							totalsByTimeSlot[timeSlot].totalProtein += proteinNutrient.amount;
							if (!totalsByTimeSlot[timeSlot].proteinUnit) {
								totalsByTimeSlot[timeSlot].proteinUnit = proteinNutrient.unit;
							}
						}
					});
				});
			}

			return totalsByTimeSlot;
		} else {
			let totalPHE = 0;
			let pheUnit: string | null = null;
			let totalProtein = 0;
			let proteinUnit: string | null = null;

			for (const entries of Object.values(categorized)) {
				entries.forEach((entry) => {
					entry.fooditems.forEach((item) => {
						const pheNutrient = item.nutrients.find((n) => n.name === 'phe');
						if (pheNutrient) {
							totalPHE += pheNutrient.amount;
							if (!pheUnit) {
								pheUnit = pheNutrient.unit;
							}
						}

						const proteinNutrient = item.nutrients.find((n) => n.name === 'protein');
						if (proteinNutrient) {
							totalProtein += proteinNutrient.amount;
							if (!proteinUnit) {
								proteinUnit = proteinNutrient.unit;
							}
						}
					});
				});
			}

			return {
				totalPHE,
				pheUnit,
				totalProtein,
				proteinUnit,
			};
		}
	}

	/**
	 * Calculates the total PHE and protein from the `items` array, categorized by morning, afternoon, and evening.
	 */
	public calculateTotalPHEAndProteinFromItemsByCategory(isSimplifiedDiet?: boolean): Record<string, { phe: { total: number; unit: string }; protein: { total: number; unit: string } }> {
		const categorized: Record<string, { phe: { total: number; unit: string }; protein: { total: number; unit: string } }> = {
			morning: { phe: { total: 0, unit: 'mg' }, protein: { total: 0, unit: 'g' } },
			afternoon: { phe: { total: 0, unit: 'mg' }, protein: { total: 0, unit: 'g' } },
			evening: { phe: { total: 0, unit: 'mg' }, protein: { total: 0, unit: 'g' } },
		};

		this.data.forEach((entry) => {
			const entryTime = entry.time.split('T')[1]; // Extract HH:mm:ss from ISO datetime
			const category = this.getTimeCategory(entryTime);
			const isMeal = entry?.category === "Meal" && entry?.items?.length > 1;

			if (!isMeal && entry?.items?.[0]?.isFreeFood && isSimplifiedDiet) {
				return
			}

			if (category) {
				categorized[category].phe.total += parseFloat(entry.phe) || 0;
				categorized[category].protein.total += parseFloat(entry.protein) || 0;

				// Ensure the units are set based on the first item
				categorized[category].phe.unit = entry?.phe ? 'mg' : categorized[category].phe.unit;
				categorized[category].protein.unit = entry?.protein ? 'g' : categorized[category].protein.unit;
			}

			// if (isMeal && isSimplifiedDiet && category) {
			// 	// Calculate totals while ignoring free food items
			// 	const totals = entry.items.reduce((acc, item) => {
			// 		if (!item.isFreeFood) {
			// 			acc.phe += parseFloat(String(item.phe || '0'));
			// 			acc.protein += parseFloat(String(item.protein || '0'));
			// 		}
			// 		return acc;
			// 	}, { phe: 0, protein: 0 });

			// 	categorized[category].phe.total = parseFloat(totals.phe) || 0;
			// 	categorized[category].protein.total = parseFloat(totals.protein) || 0;
			// }

			if (isMeal && category) {
				// For meals, always recalculate based on items
				// This ensures correct values regardless of when the meal was created

				// First, subtract the pre-calculated meal total that was added above
				categorized[category].phe.total -= parseFloat(entry.phe) || 0;
				categorized[category].protein.total -= parseFloat(entry.protein) || 0;

				// Then calculate the correct total based on current simplified diet state
				const totals = entry.items.reduce((acc, item) => {
					if (!(isSimplifiedDiet && item.isFreeFood)) {
						acc.phe += parseFloat(String(item.phe || '0'));
						acc.protein += parseFloat(String(item.protein || '0'));
					}
					return acc;
				}, { phe: 0, protein: 0 });

				// Add the recalculated totals
				categorized[category].phe.total += parseFloat(String(totals.phe)) || 0;
				categorized[category].protein.total += parseFloat(String(totals.protein)) || 0;
			}
		});

		return categorized;
	}

	/**
	 * Calculates the total PHE and protein from the `items` array.
	 * If `separateByTimeSlot` is true, totals are returned separately for each time slot.
	 * If `timeSlot` is provided, it calculates totals for that specific time slot only.
	 */
	public calculateTotalPHEAndProteinFromItems(
		separateByTimeSlot: boolean = false,
		timeSlot?: 'morning' | 'afternoon' | 'evening',
		isSimplifiedDiet?: boolean
	): Record<string, { phe: { total: number; unit: string }; protein: { total: number; unit: string } }> | { phe: { total: number; unit: string }; protein: { total: number; unit: string } } {
		const categorized: Record<string, { phe: { total: number; unit: string }; protein: { total: number; unit: string } }> = {
			morning: { phe: { total: 0, unit: 'mg' }, protein: { total: 0, unit: 'g' } },
			afternoon: { phe: { total: 0, unit: 'mg' }, protein: { total: 0, unit: 'g' } },
			evening: { phe: { total: 0, unit: 'mg' }, protein: { total: 0, unit: 'g' } },
		};

		this.data.forEach((entry) => {
			const entryTime = entry.time.split('T')[1]; // Extract HH:mm:ss from ISO datetime
			const category = this.getTimeCategory(entryTime);
			const isMeal = entry?.category === "Meal" && entry?.items?.length > 1;

			if (isSimplifiedDiet && entry?.items?.[0]?.isFreeFood && !isMeal) {
				return
			}
			if (category) {
				categorized[category].phe.total += parseFloat(entry.phe || 0);
				categorized[category].protein.total += parseFloat(entry.protein || 0);

				// Ensure the units are set based on the first item
				categorized[category].phe.unit = entry?.phe ? 'mg' : categorized[category].phe.unit;
				categorized[category].protein.unit = entry?.protein ? 'g' : categorized[category].protein.unit;
			}

			// if (isMeal && isSimplifiedDiet && category) {
			// 	// Calculate totals while ignoring free food items
			// 	const totals = entry.items.reduce((acc, item) => {
			// 		if (!item.isFreeFood) {
			// 			acc.phe += parseFloat(String(item.phe || '0'));
			// 			acc.protein += parseFloat(String(item.protein || '0'));
			// 		}
			// 		return acc;
			// 	}, { phe: 0, protein: 0 });

			// 	categorized[category].phe.total = parseFloat(totals.phe) || 0;
			// 	categorized[category].protein.total = parseFloat(totals.protein) || 0;
			// }

						if (isMeal && category) {
				// For meals, always recalculate based on items
				// This ensures correct values regardless of when the meal was created

				// First, subtract the pre-calculated meal total that was added above
				categorized[category].phe.total -= parseFloat(entry.phe || 0);
				categorized[category].protein.total -= parseFloat(entry.protein || 0);

				// Then calculate the correct total based on current simplified diet state
				const totals = entry.items.reduce((acc, item) => {
					if (!(isSimplifiedDiet && item.isFreeFood)) {
						acc.phe += parseFloat(String(item.phe || '0'));
						acc.protein += parseFloat(String(item.protein || '0'));
					}
					return acc;
				}, { phe: 0, protein: 0 });
				
				// Add the recalculated totals
				categorized[category].phe.total += parseFloat(String(totals.phe)) || 0;
				categorized[category].protein.total += parseFloat(String(totals.protein)) || 0;
			}
		});

		if (timeSlot) {
			if (!categorized[timeSlot]) {
				throw new Error(`Invalid timeSlot: ${timeSlot}. Valid options are "morning", "afternoon", "evening".`);
			}
			return categorized[timeSlot];
		}

		if (separateByTimeSlot) {
			return categorized;
		}

		// Calculate combined totals if not separated
		const totals = { phe: { total: 0, unit: 'mg' }, protein: { total: 0, unit: 'g' } };
		for (const slot of ['morning', 'afternoon', 'evening'] as const) {
			totals.phe.total += categorized[slot].phe.total;
			totals.protein.total += categorized[slot].protein.total;
		}

		return totals;
	}

	/**
	 * Checks if a meal exists in each category.
	 * A meal is identified by `fooditems[index].category === "Meal"`.
	 * Returns the existence and `_id` of the first meal found in each category.
	 */
	public checkMealExistenceByCategory(): Record<string, { exists: boolean; _id: string | null }> {
		const categorized = this.categorizeFoodItems();
		const mealExistence: Record<string, { exists: boolean; _id: string | null }> = {
			morning: { exists: false, _id: null },
			afternoon: { exists: false, _id: null },
			evening: { exists: false, _id: null },
		};

		for (const [category, entries] of Object.entries(categorized)) {
			for (const entry of entries) {
				const meal = entry.category === 'Meal';
				if (meal) {
					mealExistence[category] = { exists: true, _id: entry._id };
					break; // Stop checking once a meal is found
				}
			}
		}

		return mealExistence;
	}

	/**
	 * Determines the time category for a given time string (HH:mm:ss).
	 * @param time - Time string in the format HH:mm:ss
	 * @returns - The time category ('morning', 'afternoon', 'evening') or null if no match.
	 */
	private getTimeCategory(time: string): string | null {
		const morning = { startTime: '00:00:00', endTime: '11:59:59' };
		const afternoon = { startTime: '12:00:00', endTime: '17:59:59' };
		const evening = { startTime: '18:00:00', endTime: '23:59:59' };

		if (time >= morning.startTime && time <= morning.endTime) {
			return 'morning';
		}
		if (time >= afternoon.startTime && time <= afternoon.endTime) {
			return 'afternoon';
		}
		if (time >= evening.startTime && time <= evening.endTime) {
			return 'evening';
		}
		return null;
	}

	/**
	 * Checks if a given time is within a specified range.
	 * @param time - The time to check (HH:mm:ss)
	 * @param startTime - Start time of the range (HH:mm:ss)
	 * @param endTime - End time of the range (HH:mm:ss)
	 * @returns - True if the time is within range, false otherwise.
	 */
	private isTimeInRange(time: string, startTime: string, endTime: string): boolean {
		return time >= startTime && time <= endTime;
	}

	public getIngredientsAndPortionsByTimeSlot(
		timeSlot?: 'morning' | 'afternoon' | 'evening',
		isMeal?: boolean
	): { ingredients: any[]; portions: any[]; fooditems?: any[] } | Record<string, { ingredients: any[]; portions: any[]; fooditems?: any[] }> {
		const categorized = this.categorizeFoodItems();

		if (timeSlot) {
			// Validate the timeSlot
			if (!categorized[timeSlot]) {
				throw new Error(`Invalid timeSlot: ${timeSlot}. Valid options are "morning", "afternoon", "evening".`);
			}

			const results: { ingredients: any[]; portions: any[]; fooditems?: any[] } = {
				ingredients: [],
				portions: [],
			};

			if (isMeal) {
				results.fooditems = [];
			}

			categorized[timeSlot].forEach((entry) => {
				entry.fooditems.forEach((item) => {
					if (item.ingredients) {
						results.ingredients.push(...item.ingredients);
					}
					if (item.portions) {
						results.portions.push(...item.portions);
					}
					if (isMeal && results.fooditems) {
						results.fooditems.push(item);
					}
				});
			});

			return results;
		}

		// For all time slots
		const results: Record<string, { ingredients: any[]; portions: any[]; fooditems?: any[] }> = {
			morning: { ingredients: [], portions: [] },
			afternoon: { ingredients: [], portions: [] },
			evening: { ingredients: [], portions: [] },
		};

		if (isMeal) {
			results.morning.fooditems = [];
			results.afternoon.fooditems = [];
			results.evening.fooditems = [];
		}

		for (const [slot, entries] of Object.entries(categorized)) {
			entries.forEach((entry) => {
				entry.fooditems.forEach((item) => {
					if (item.ingredients) {
						results[slot].ingredients.push(...item.ingredients);
					}
					if (item.portions) {
						results[slot].portions.push(...item.portions);
					}
					if (isMeal && results[slot].fooditems) {
						results[slot].fooditems.push(item);
					}
				});
			});
		}

		return results;
	}

	/**
	 * Converts `items` quantities to grams, sums them up, and outputs totals (quantity + unit) for each time slot.
	 * If the total quantity in grams >= 200 g, it is converted to servings and output in servings.
	 */
	public calculateTotalQuantityByTimeSlot(): Record<string, { totalQuantity: number; unit: string }> {
		const totals: Record<string, { totalQuantity: number; unit: string }> = {
			morning: { totalQuantity: 0, unit: 'g' },
			afternoon: { totalQuantity: 0, unit: 'g' },
			evening: { totalQuantity: 0, unit: 'g' },
		};

		this.data.forEach((entry) => {
			const entryTime = entry.time.split('T')[1]; // Extract HH:mm:ss from ISO datetime
			const category = this.getTimeCategory(entryTime);

			if (category) {
				entry.items.forEach((item) => {
					const convertedQuantity = PortionConverter.toGrams(item.unit, item.quantity);
					// Accumulate the total for the category in grams
					totals[category].totalQuantity += convertedQuantity;
				});
			}
		});

		// Convert totals to "Servings" where necessary after summing all items
		Object.keys(totals).forEach((category) => {
			// if (totals[category].totalQuantity >= 200) {
			// Convert total grams to servings
			// const servings = totals[category].totalQuantity / 200;
			const servings = 200;
			totals[category].totalQuantity = parseFloat(servings.toFixed(2)); // Keep two decimal places
			totals[category].unit = 'Servings';
			// } else {
			//   // Retain grams for quantities less than 200g
			//   totals[category].unit = "g";
			// }
		});
		return totals;
	}
}
