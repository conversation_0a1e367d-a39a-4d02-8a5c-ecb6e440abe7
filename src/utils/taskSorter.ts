import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import { TIME_SLOT } from "@/constants/timeSlots"; // Adjust based on your actual constants

dayjs.extend(utc);
dayjs.extend(timezone);

export const sortByTime = (taskList) =>
  taskList.sort((a, b) =>
    dayjs(a.intakeTime, "HH:mm:ss").diff(dayjs(b.intakeTime, "HH:mm:ss"))
  );

export const classifyTasksByPeriod = (tasks) => {
  return {
    morning: sortByTime(
      tasks.filter((task) => {
        const taskTime = dayjs(task.intakeTime, "HH:mm:ss");
        const startTime = dayjs(
          taskTime.format("YYYY-MM-DD") + " " + TIME_SLOT.morning.startTime,
          "YYYY-MM-DD HH:mm:ss"
        );
        const endTime = dayjs(
          taskTime.format("YYYY-MM-DD") + " " + TIME_SLOT.morning.endTime,
          "YYYY-MM-DD HH:mm:ss"
        );
        return taskTime.isBetween(startTime, endTime, null, "[]");
      })
    ),
    afternoon: sortByTime(
      tasks.filter((task) => {
        const taskTime = dayjs(task.intakeTime, "HH:mm:ss");
        const startTime = dayjs(
          taskTime.format("YYYY-MM-DD") + " " + TIME_SLOT.afternoon.startTime,
          "YYYY-MM-DD HH:mm:ss"
        );
        const endTime = dayjs(
          taskTime.format("YYYY-MM-DD") + " " + TIME_SLOT.afternoon.endTime,
          "YYYY-MM-DD HH:mm:ss"
        );
        return taskTime.isBetween(startTime, endTime, null, "[]");
      })
    ),
    evening: sortByTime(
      tasks.filter((task) => {
        const taskTime = dayjs(task.intakeTime, "HH:mm:ss");
        const startTime = dayjs(
          taskTime.format("YYYY-MM-DD") + " " + TIME_SLOT.evening.startTime,
          "YYYY-MM-DD HH:mm:ss"
        );
        const endTime = dayjs(
          taskTime.format("YYYY-MM-DD") + " " + TIME_SLOT.evening.endTime,
          "YYYY-MM-DD HH:mm:ss"
        );
        return taskTime.isBetween(startTime, endTime, null, "[]");
      })
    ),
  };
};
