/** @format */

import { createContext, PropsWithChildren, useEffect, useMemo, useState } from "react";
import { Appearance, useColorScheme } from "react-native";

import { config } from "@/theme/_config";
import { generateFontSizes, generateFontColors, staticFontStyles } from "@/theme/fonts";
import { generateBorderColors, generateBorderRadius, generateBorderWidths } from "@/theme/borders";
import layout from "@/theme/layout";
import componentsGenerator from "@/theme/components";
import { generateBackgrounds } from "@/theme/backgrounds";
import { generateGutters } from "@/theme/gutters";
import generateConfig from "@/theme/ThemeProvider/generateConfig";

import type { MMKV } from "react-native-mmkv";
import type { ComponentTheme, Theme } from "@/types/theme/theme";
import type { FulfilledThemeConfiguration, Variant } from "@/types/theme/config";

// Types

type Context = Theme & {
  changeTheme: (variant: Variant, isInitial?: boolean) => void;
};

export const ThemeContext = createContext<Context | undefined>(undefined);

type Props = PropsWithChildren<{
  storage: MMKV;
}>;

function ThemeProvider({ children = false, storage }: Props) {
  const [currentTheme, setCurrentTheme] = useState<"light" | "dark">(() => {
    return Appearance.getColorScheme() || "light";
  });

  const scheme = useColorScheme();
  const [variant, setVariant] = useState<Variant>(() => {
    const savedTheme = storage.getString("theme") as Variant;
    if (savedTheme === "default") {
      return currentTheme;
    }
    return savedTheme || "dark";
  });

  // Handle system theme changes
  useEffect(() => {
    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      const newTheme = colorScheme || "light";
      setCurrentTheme(newTheme);

      // Only update variant if we're in default mode
      const savedTheme = storage.getString("theme") as Variant;
      if (savedTheme === "default") {
        setVariant(newTheme);
      }
    });

    return () => subscription.remove();
  }, [storage]);

	const changeTheme = (nextVariant: Variant, isInitial: boolean) => {
		if (!isInitial) {
		storage.set('theme', nextVariant);
		}
		if (nextVariant === 'default') {
			const appMode = Appearance.getColorScheme();
			// When switching to default, use the current system theme
			setVariant(currentTheme);
		} else {
			setVariant(nextVariant);
		}
	};

  // Flatten config with current variant
  const fullConfig = useMemo(() => {
    return generateConfig(variant) satisfies FulfilledThemeConfiguration;
  }, [variant, config]);

  const fonts = useMemo(() => {
    return {
      ...generateFontSizes(),
      ...generateFontColors(fullConfig),
      ...staticFontStyles,
    };
  }, [fullConfig]);

  const backgrounds = useMemo(() => {
    return generateBackgrounds(fullConfig);
  }, [fullConfig]);

  const borders = useMemo(() => {
    return {
      ...generateBorderColors(fullConfig),
      ...generateBorderRadius(),
      ...generateBorderWidths(),
    };
  }, [fullConfig]);

  const navigationTheme = useMemo(() => {
    return {
      dark: variant === "dark",
      colors: fullConfig.navigationColors,
    };
  }, [variant, fullConfig.navigationColors]);

  const theme = useMemo(() => {
    return {
      colors: fullConfig.colors,
      variant,
      gutters: generateGutters(),
      layout,
      fonts,
      backgrounds,
      borders,
    } satisfies ComponentTheme;
  }, [variant, layout, fonts, backgrounds, borders, fullConfig.colors]);

  const components = useMemo(() => {
    return componentsGenerator(theme);
  }, [theme]);

  const value = useMemo(() => {
    return { ...theme, components, navigationTheme, changeTheme };
  }, [theme, components, navigationTheme, changeTheme]);

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>;
}

export default ThemeProvider;
