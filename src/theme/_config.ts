/** @format */

import { DarkTheme, DefaultTheme } from "@react-navigation/native";

import type { ThemeConfiguration } from "@/types/theme/config";
import { Dimensions, Platform } from "react-native";

export const SCREEN_WIDTH = Dimensions.get("screen").width;
export const SCREEN_HEIGHT = Dimensions.get("screen").height;
export const IS_ANDROID = Platform.OS === "android";
export const IS_IOS = Platform.OS === "ios";

const colorsLight = {
  blue: "#13689e",
  red: "red",
  calendarDayText: "#3c3c3b",
  calendarDisableText: "#79777B",
  darkGray: "#F0F3F6",
  borderDisable: "#9c9b9b",
  borderDarkGray: "#9c9b9b",
  mediumGray: "#9c9b9b",
  textPrimaryWhite: "#CC0055",
  lightMediumGray: "#9C9B9B45",
  pink: "#CC0055",
  green: "#23b8b1",
  yellow: "#fbbb00",
  white: "#FFFFFF",
  black: "#000000",
  test: "#FFFFFF",
  app_bg: "#F0F3F6",
  textPrimary: "#1F1F1F",
  buttonText: "#3c3c3b",
  primary: "#CC0055",
  secondary: "#BE4A79",
  tertiary: "#13689E",
  tile_bg: "#FFFFFF",
  labsGray: "#F0F3F6",
  gray: "#9c9b9b",
  dietTagGray: "#FFFFFF",
  pheGray: "#F0F3F6",
  pheTextPrimary: "#CC0055",
  textPrimaryYellow: "#CC0055",
  transparent: "transparent",
  item_bg: "#FFFFFF",
  tabItemBg: "#FFFFFF",
  item_secondary_bg: "#F0F3F6",
  delete: "#FE4539",
  dietCard: "#353535",
  highlight: "#E8DEF8",
  lighBlack: "#252525",
  appLogo: "#1F1F1F",
  welcomeBg: "#FFFFFF",
  reverseBlack: "#FFFFFF",
  dropDownGray: "#F0F3F6",
  taskCardGray: "#FFFFFF",
  calendarBg: "#ECE6F0",
  labsCard: "#FFFFFF",
  contactCard: "#FFFFFF",
  contactItem: "#F0F3F6",
  contactAction: "#FFFFFF",
  yellowBlue: "#13689E",
  yellowPink: "#CC0055",
  lightGrey: "#3535354D",
  divider: "#9C9B9B",
  slightLightGrey:"#A7CAEB",
  lightGreen:"#6AB096",
  intentionBlackColor:"#1F1F1F",
  aiCard:"#F0F3F6",
  aiCardBottom: "#9C9B9B",
  aiBackground:'#FFFFFF',
  aiBottomButtonsSeparator: "#505050",
  aiCardBottomContainer:"#F0F3F6",
  bottomTabColor:"#F0F3F6",
  roseYellowColor:"#CC0055",
  whiteBlack: "#1F1F1F",


} as const;

const colorsDark = {
  blue: "#13689E",
  red: "red",
  calendarDayText: "#3c3c3b",
  calendarDisableText: "#79777B",
  darkGray: "#3c3c3b",
  borderDisable: "#3c3c3b",
  borderDarkGray: "#3c3c3b",
  mediumGray: "#9c9b9b",
  textPrimaryWhite: "#ffffff",
  pink: "#e5005f",
  green: "#23b8b1",
  yellow: "#fbbb00",
  white: "#FFFFFF",
  black: "#000000",
  test: "#000000",
  app_bg: "#1f1f1f",
  textPrimary: "#FFFFFF",
  primary: "#e5005f",
  secondary: "#BE4A79",
  tertiary: "#13689E",
  tile_bg: "#353535",
  gray: "#505050",
  dietTagGray: "#505050",
  pheGray: "#505050",
  pheTextPrimary: "#FFFFFF",
  textPrimaryYellow: "#fbbb00",
  transparent: "transparent",
  item_bg: "#1F1F1F",
  tabItemBg: "#1F1F1F",
  item_secondary_bg: "#1F1F1F",
  delete: "#FE4539",
  highlight: "#E8DEF8",
  appLogo: "#ffffff",
  welcomeBg: "#353535",
  dietCard: "#353535",
  reverseBlack: "#1F1F1F",
  dropDownGray: "#505050",
  taskCardGray: "#3c3c3b",
  calendarBg: "#FFFFFF",
  labsGray: "#1F1F1F",
  labsCard: "#505050",
  contactCard: "#353535",
  contactItem: "#1F1F1F",
  contactAction: "#353535",
  yellowBlue: "#fbbb00",
  yellowPink: "#fbbb00",
  divider: "#FFFFFF",
  lightGrey: '#3535354D',
  slightLightGrey:"#A7CAEB",
  lightGreen:"#6AB096",
  intentionBlackColor:"#1F1F1F",
  aiCard: "#505050",
  aiCardBottom:'#353535',
  aiBackground:'#353535',
  aiBottomButtonsSeparator: "#505050",
  aiCardBottomContainer:"#9c9b9b",
  bottomTabColor:"#1F1F1F",
  roseYellowColor:"#fbbb00",
  whiteBlack: "#FFFFFF",


} as const;

const sizes = [0, 12, 16, 20, 24, 32, 40, 80] as const;

export const config = {
  colors: colorsLight,
  fonts: {
    sizes,
    colors: colorsLight,
  },
  gutters: sizes,
  backgrounds: colorsLight,
  borders: {
    widths: [1, 2],
    radius: [4, 16],
    colors: colorsLight,
  },
  navigationColors: {
    ...DefaultTheme.colors,
    background: colorsLight.app_bg,
    card: colorsLight.app_bg,
  },
  variants: {
    dark: {
      colors: colorsDark,
      fonts: {
        colors: colorsDark,
      },
      backgrounds: colorsDark,
      navigationColors: {
        ...DarkTheme.colors,
        background: colorsDark.app_bg,
        card: colorsDark.app_bg,
      },
    },
    light: {
      colors: colorsLight,
      fonts: {
        colors: colorsLight,
      },
      backgrounds: colorsLight,
      navigationColors: {
        ...DefaultTheme.colors,
        background: colorsLight.app_bg,
        card: colorsLight.app_bg,
      },
    },
  },
} as const satisfies ThemeConfiguration;
