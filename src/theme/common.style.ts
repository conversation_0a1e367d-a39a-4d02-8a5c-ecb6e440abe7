import { ScaledSheet } from "react-native-size-matters";
import { config, SCREEN_WIDTH } from "./_config";
import { Fonts } from "@/constants";

const Common = ScaledSheet.create({
  textBold: {
    fontFamily: Fonts.RALEWAY_BOLD,
  },
  rowJustifyCenter: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  rowJustifySpace: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  tabBarItemStyle: {
    gap: "4@ms",
    height: "36@vs",
  },
  tabBarStyle: {
    elevation: 0,
    height: "90@vs",
    borderTopWidth: 4,
    alignSelf: "center",
    paddingVertical: "24@vs",
    width: SCREEN_WIDTH - 32,
    paddingHorizontal: "18@vs",
    borderTopColor: config.colors.primary,
  },
  appMarginHorizontal: {
    paddingHorizontal: "16@ms",
  },
  flexOne: {
    flex: 1,
  },
  justifySpaceFlex: {
    flex: 1,
    justifyContent: "space-between",
  },
});

export default Common;