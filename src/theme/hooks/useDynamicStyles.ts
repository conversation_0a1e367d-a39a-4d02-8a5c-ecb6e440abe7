import { useMemo } from 'react';
import { StyleProp } from 'react-native';
import { Theme } from '@/types/theme/theme';

import useTheme from './useTheme';

type StyleGenerator<T> = (theme: Theme) => T;

export function useDynamicStyles<T>(
  styleGenerator: StyleGenerator<T>,
  externalStyle?: StyleProp<T> | any
): StyleProp<T> {
  const theme = useTheme();

  const mergedStyles = useMemo(
    () =>  !externalStyle ? styleGenerator(theme): [styleGenerator(theme), externalStyle],
    [JSON.stringify(externalStyle), JSON.stringify(theme)]
  );

  return mergedStyles;
}