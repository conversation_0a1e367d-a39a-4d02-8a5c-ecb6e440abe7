
const subdomain = 'customer-ff0pyco1nz8nrgax.cloudflarestream.com/';
const manifest_hls = 'manifest/video.m3u8';
const manifest_dash = 'manifest/video.mpd';

export const getPlaybackUrl = (id: string, output_hls: boolean = true) => {
  if (output_hls) {
    return `https://${subdomain}${id}/${manifest_hls}`;
  }
  return `https://${subdomain}${id}/${manifest_dash}`;
};

export const getVideoThumbnail = (id: string) => {
  return `https://${subdomain}${id}/thumbnails/thumbnail.jpg`;
};

export const ONBOARDING_VIDEOS_IDS = {
  dashboard: 'b494cbab18a1f327c2ded0b60f41a8e8',
  diet_tracker: '01b0da4e77ab30323ee3c7d9d017e0f5',
  labs: '41b7fbcc1c88f6f1fd74898ed5da031b',
  meal_scanner: '62961d393cf6a24777940bf4a00e03ea',
};


export const ONBOARDING_VIDEOS = {
  dashboard: {
    id: ONBOARDING_VIDEOS_IDS.dashboard,
    thumbnail: getVideoThumbnail(ONBOARDING_VIDEOS_IDS.dashboard),
    playbackUrl: {
      hls: getPlaybackUrl(ONBOARDING_VIDEOS_IDS.dashboard),
      dash: getPlaybackUrl(ONBOARDING_VIDEOS_IDS.dashboard, false),
    },
  },
  diet_tracker: {
    id: ONBOARDING_VIDEOS_IDS.diet_tracker,
    thumbnail: getVideoThumbnail(ONBOARDING_VIDEOS_IDS.diet_tracker),
    playbackUrl: {
      hls: getPlaybackUrl(ONBOARDING_VIDEOS_IDS.diet_tracker),
      dash: getPlaybackUrl(ONBOARDING_VIDEOS_IDS.diet_tracker, false),
    },
  },
  labs: {
    id: ONBOARDING_VIDEOS_IDS.labs,
    thumbnail: getVideoThumbnail(ONBOARDING_VIDEOS_IDS.labs),
    playbackUrl: {
      hls: getPlaybackUrl(ONBOARDING_VIDEOS_IDS.labs),
      dash: getPlaybackUrl(ONBOARDING_VIDEOS_IDS.labs, false),
    }
  },
  meal_scanner: {
    id: ONBOARDING_VIDEOS_IDS.meal_scanner,
    thumbnail: getVideoThumbnail(ONBOARDING_VIDEOS_IDS.meal_scanner),
    playbackUrl: {
      hls: getPlaybackUrl(ONBOARDING_VIDEOS_IDS.meal_scanner),
      dash: getPlaybackUrl(ONBOARDING_VIDEOS_IDS.meal_scanner, false),
    }
  },
}