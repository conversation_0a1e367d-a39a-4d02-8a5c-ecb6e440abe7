/** @format */

import icons from "@/theme/assets/images/svgs/icons";

export const Stacks = {
  Onboarding: "Onboarding",
  Auth: "Auth",
} as const;

const Screens = {
  Onboarding: "Onboarding",
  Auth: "Auth",
  App: "App",
  Home: "Home",
} as const;

export const TabsConstant = {
  Home: icons.HomeTabIcon,
  Labs: icons.LabTabIcon,
  Tasks: icons.TaskTabIcon,
  Diet: icons.DietTabIcon,
} as const;

export type TabNames = "Home" | "Labs" | "Tasks" | "Diet";

export const getIconComponent = (tabName: TabNames) => {
  return TabsConstant[tabName];
};
type Screens = keyof typeof Screens;

export default Screens;
