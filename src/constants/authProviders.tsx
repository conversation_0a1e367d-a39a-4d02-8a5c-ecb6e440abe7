/** @format */

// src/constants/authProviders.ts
import Icons from "@/theme/assets/images/svgs/icons";
import { LOGIN_OPTIONS } from "@/constants/auth0";
import { IS_IOS } from "@/theme/_config";

export const AUTH_PROVIDERS = {
  google: {
    icon: <Icons.Google width={18} height={18} />,
    label: "Google",
    connection: LOGIN_OPTIONS.GOOGLE,
  },
  facebook: {
    icon: <Icons.Facebook width={18} height={18} />,
    label: "Facebook",
    connection: LOGIN_OPTIONS.FACEBOOK,
  },
  microsoft: {
    icon: <Icons.Microsoft width={18} height={18} />,
    label: "Microsoft",
    connection: LOGIN_OPTIONS.MICROSOFT,
  },
  ...(IS_IOS
    ? {
        apple: {
          icon: <Icons.Apple width={18} height={22.19} />,
          label: "Apple",
          connection: LOGIN_OPTIONS.APPLE,
        },
      }
    : {}),
} as const;


export type AuthProviderKey = keyof typeof AUTH_PROVIDERS;

export const AUTH_PROVIDER_LIST = Object.values(AUTH_PROVIDERS);
