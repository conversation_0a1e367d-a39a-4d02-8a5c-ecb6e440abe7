enum actionBool {
  TRUE = "true",
  FALSE = "false",
}

const CUSTOM_EVENTS_NAME = {
  USER_IS: "user_is",
  SIGN_UP_START: "sign_up_start",
  CONSENT_GIVEN: "consent_given",
  DISCLAIMER_UNDERSTOOD: "disclaimer_understood",

};

const CUSTOM_EVENTS = {
  USER_IS: (action) => ({
    event: CUSTOM_EVENTS_NAME.USER_IS,
    item_id: CUSTOM_EVENTS_NAME.USER_IS,
    action: action,
  }),
  SIGN_UP_START: (action: actionBool) => ({
    event: CUSTOM_EVENTS_NAME.SIGN_UP_START,
    item_id: CUSTOM_EVENTS_NAME.SIGN_UP_START,
    action: action,
  }),
  DISCLAIMER_UNDERSTOOD: (action: actionBool) => ({
    event: CUSTOM_EVENTS_NAME.DISCLAIMER_UNDERSTOOD,
    item_id: CUSTOM_EVENTS_NAME.DISCLAIMER_UNDERSTOOD,
    action: action,
  }),
  CONSENT_GIVEN: (action: actionBool) => ({
    event: CUSTOM_EVENTS_NAME.CONSENT_GIVEN,
    item_id: CUSTOM_EVENTS_NAME.CONSENT_GIVEN,
    action: action,
  })
};

export default { CUSTOM_EVENTS_NAME, CUSTOM_EVENTS, actionBool };
