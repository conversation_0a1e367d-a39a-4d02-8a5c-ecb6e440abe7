// ReactotronConfig.ts
import { AppState, AppStateStatus, Platform } from 'react-native';
import Reactotron, { ReactotronReactNative } from 'reactotron-react-native';
import mmkvPlugin from 'reactotron-react-native-mmkv';
import { QueryClientManager, reactotronReactQuery } from 'reactotron-react-query';

import { storage, queryClient } from './App';
import config from '../app.json';

const queryClientManager = new QueryClientManager({ queryClient });

const HOST = Platform.select({ ios: '127.0.0.1', android: '********' })!;
const PORT = 9090;

/**
 * AppState-aware createSocket with retry/backoff
 * - always assigns `socket` before returning
 * - normalizes path to avoid malformed URL
 * - will NOT schedule reconnects while app is backgrounded
 * - will reconnect when app returns to foreground
 */
const createSocketWithRetry = (path: string = '/'): WebSocket => {
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;
  const url = `ws://${HOST}:${PORT}${normalizedPath}`;
  console.log('[Reactotron] connecting to', url);

  let socket!: WebSocket; // definite assignment
  let retryDelay = 3000; // ms
  let reconnectTimer: ReturnType<typeof setTimeout> | null = null;
  let pendingReconnect = false;

  // track app state so we avoid reconnecting while backgrounded
  let appState: AppStateStatus = AppState.currentState;
  const handleAppStateChange = (next: AppStateStatus) => {
    appState = next;
    if (appState === 'active' && pendingReconnect && (!reconnectTimer)) {
      // attempt immediate reconnect when coming to foreground
      pendingReconnect = false;
      retryDelay = 3000; // reset a bit
      create();
    }
  };
  const removeListener = AppState.addEventListener
    ? AppState.addEventListener('change', handleAppStateChange)
    : // for older RN where addEventListener returns void
      { remove: () => {} };

  const clearReconnectTimer = () => {
    if (reconnectTimer) {
      clearTimeout(reconnectTimer);
      reconnectTimer = null;
    }
  };

  const create = () => {
    // create socket synchronously so we can return it
    socket = new WebSocket(url);

    socket.onopen = () => {
      console.log('[Reactotron] socket open:', url);
      // reset backoff
      retryDelay = 3000;
      clearReconnectTimer();
    };

    socket.onmessage = (m) => {
      // optional: debug messages
      // console.debug('[Reactotron] message', m.data);
    };

    socket.onerror = (err) => {
      // errors commonly followed by onclose; keep light here
      console.warn('[Reactotron] socket error', err);
    };

    socket.onclose = (ev) => {
      console.warn('[Reactotron] socket closed', ev);

      clearReconnectTimer();

      // if app is backgrounded, mark pending and wait for resume
      if (appState !== 'active') {
        pendingReconnect = true;
        // do not schedule retries while backgrounded
        return;
      }

      // schedule reconnect with exponential backoff (cap 30s)
      reconnectTimer = setTimeout(() => {
        retryDelay = Math.min(Math.floor(retryDelay * 1.5), 30000);
        reconnectTimer = null;
        create();
      }, retryDelay);
    };
  };

  // create assigns `socket` synchronously before return
  create();

  // monkey cleanup: when the runtime module is reloaded in dev, ensure we remove listener
  // (helps HMR not add duplicate listeners)
  // Note: Reactotron or your hot-reload may call this module multiple times — keep safe.
  // Expose a tiny cleanup on the socket instance so tests/tools can call it if needed.
  (socket as any).__reactotronCleanup = () => {
    clearReconnectTimer();
    try {
      removeListener.remove();
    } catch {
      // no-op
    }
    try {
      socket.close();
    } catch {
      // no-op
    }
  };

  return socket;
};

// Configure Reactotron
Reactotron.configure({
  name: config.name,
  host: HOST,
  port: PORT,
  createSocket: createSocketWithRetry,
  onDisconnect: () => {
    queryClientManager.unsubscribe();
  },
})
  .useReactNative()
  .use(mmkvPlugin<ReactotronReactNative>({ storage }))
  .use(reactotronReactQuery(queryClientManager))
  .connect();
