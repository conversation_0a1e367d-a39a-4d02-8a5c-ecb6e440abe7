import { MedicationFormula } from '@/components/molecules/FormulaCard/FormulaCard';
import { Medication } from '@/components/molecules/MedicationCard/MedicationCard';
import Icons from '@/theme/assets/images/svgs/icons';

const MEDICATION_LISTING: Medication[] = [
  {
    medicineName: 'BoneCAl Plus',
    activeIngredient:
      'Calcium carbonate 750 mg. containing calcium 300 mg., Vitamin D 31 mg.',
    intakeTime: '13:20:00',
    fromDate: '2025-01-07T09:07:07.977',
    toDate: '2025-01-07T09:07:07.977',
    iconId: 4,
    period: 'Evening',
    upcoming: '2025-01-07T09:07:07.977',
    id: 17
  },
  {
    medicineName: 'nuberol forte',
    activeIngredient: 'paracetamol 650 mg + orphenadrine 50 mg',
    intakeTime: '12:20:00',
    fromDate: '2025-01-10T11:49:40.547',
    toDate: '2025-01-10T11:49:40.547',
    iconId: 2,
    period: 'Afternoon',
    upcoming: '2025-01-10T11:49:40.547',
    id: 13
  },
  {
    medicineName: 'nuberol forte',
    activeIngredient: 'paracetamol 650 mg + orphenadrine 50 mg',
    intakeTime: '12:20:00',
    fromDate: '2025-01-06T11:49:40.547',
    toDate: '2025-01-10T11:49:40.547',
    iconId: 6,
    period: 'Afternoon',
    upcoming: '2025-01-06T12:20:00',
    id: 11
  },
  {
    medicineName: 'BoneCAl Plus-Daily-Custom',
    activeIngredient: 'BoneCAl Plus-Daily-Custom',
    intakeTime: '13:20:00',
    fromDate: '2025-01-07T09:07:07.977',
    toDate: '2025-01-09T09:07:07.977',
    iconId: 0,
    period: 'Afternoon',
    upcoming: '2025-01-09T09:07:07.977',
    id: 25
  }
];

const FORMULA_LISTING: MedicationFormula[] = [
  {
    id: 1,
    date: '11-23-2025',
    time: '11:00 PM',
    dayTime: 'Monday'
  },
  {
    id: 2,
    date: '02-23-2025',
    time: '11:00 AM',
    dayTime: 'Evening'
  }
];

const MEDICATION_CATEGORIES = [
  {
    id: 1,
    value: 'tablet',
    label: 'Tablet'
  },
  {
    id: 2,
    value: 'powder',
    label: 'Powder'
  },
  {
    id: 3,
    value: 'injection',
    label: 'Injection'
  }
];

const MEDICATION_FREQUENCIES = [
  {
    id: 1,
    value: 'Day',
    label: 'Daily'
  },
  {
    id: 2,
    value: 'Week',
    label: 'Weekly'
  },
  {
    id: 3,
    value: 'Month',
    label: 'Monthly'
  },
  {
    id: 4,
    value: 'Year',
    label: 'Yearly'
  }
];

const MEDICATION_DAILY_ROUTINE = Array.from({ length: 999 }, (_, i) => ({
  id: i + 1,
  value: `${i + 1}${(n => n % 100 >= 11 && n % 100 <= 13 ? 'th' : { 1: 'st', 2: 'nd', 3: 'rd' }[n % 10] || 'th')(i + 1)}`,
  label: `${i + 1}`
}));

const MEDICATION_WEEKLY_ROUTINE = [
  {
    id: 0,
    value: 'sunday',
    label: 'Sunday'
  },
  {
    id: 1,
    value: 'monday',
    label: 'Monday'
  },
  {
    id: 2,
    value: 'tuesday',
    label: 'Tuesday'
  },
  {
    id: 3,
    value: 'wednesday',
    label: 'Wednesday'
  },
  {
    id: 4,
    value: 'thursday',
    label: 'Thursday'
  },
  {
    id: 5,
    value: 'friday',
    label: 'Friday'
  },
  {
    id: 6,
    value: 'saturday',
    label: 'Saturday'
  }
];

const MEDICATION_YEARLY_ROUTINE = [
  {
    id: 1,
    value: 'january',
    label: 'Jan'
  },
  {
    id: 2,
    value: 'february',
    label: 'Feb'
  },
  {
    id: 3,
    value: 'march',
    label: 'Mar'
  },
  {
    id: 4,
    value: 'april',
    label: 'Apr'
  },
  {
    id: 5,
    value: 'may',
    label: 'May'
  },
  {
    id: 6,
    value: 'june',
    label: 'Jun'
  },
  {
    id: 7,
    value: 'july',
    label: 'Jul'
  },
  {
    id: 8,
    value: 'august',
    label: 'Aug'
  },
  {
    id: 9,
    value: 'september',
    label: 'Sep'
  },
  {
    id: 10,
    value: 'october',
    label: 'Oct'
  },
  {
    id: 11,
    value: 'november',
    label: 'Nov'
  },
  {
    id: 12,
    value: 'december',
    label: 'Dec'
  }
];

const MEDICATION_REMINDER = [
  {
    id: 1,
    value: '5 mins',
    label: '5 mins'
  },
  {
    id: 2,
    value: '10 mins',
    label: '10 mins'
  },
  {
    id: 3,
    value: '20 mins',
    label: '20 mins'
  }
];

const PILL_SHAPES = [
  {
    id: 1,
    icon: Icons.PillShape1
  },
  {
    id: 2,
    icon: Icons.PillShape2
  },
  {
    id: 3,
    icon: Icons.PillShape3
  },
  {
    id: 4,
    icon: Icons.PillShape4
  },
  {
    id: 5,
    icon: Icons.PillShape5
  },
  {
    id: 6,
    icon: Icons.PillShape6
  },
  {
    id: 7,
    icon: Icons.PillShape7
  },
  {
    id: 8,
    icon: Icons.PillShape8
  },
  {
    id: 9,
    icon: Icons.PillShape9
  },
  {
    id: 10,
    icon: Icons.PillShape10
  }
];

const CUSTOM_CALENDAR_WEEKS = [
  {
    id: 1,
    value: 'first',
    label: 'First'
  },
  {
    id: 2,
    value: 'second',
    label: 'Second'
  },
  {
    id: 3,
    value: 'third',
    label: 'Third'
  },
  {
    id: 4,
    value: 'fourth',
    label: 'Fourth'
  },
  {
    id: 5,
    value: 'last',
    label: 'Last'
  }
];

const CUSTOM_CALENDAR_WEEKS_DAYS = [
  {
    id: 1,
    value: 'monday',
    label: 'Monday'
  },
  {
    id: 2,
    value: 'tuesday',
    label: 'Tuesday'
  },
  {
    id: 3,
    value: 'wednesday',
    label: 'Wednesday'
  },
  {
    id: 4,
    value: 'thursday',
    label: 'Thursday'
  },
  {
    id: 5,
    value: 'friday',
    label: 'Friday'
  },
  {
    id: 6,
    value: 'saturday',
    label: 'Saturday'
  },
  {
    id: 7,
    value: 'sunday',
    label: 'Sunday'
  },
  {
    id: 8,
    value: 'weekday',
    label: 'Weekday'
  },
  {
    id: 9,
    value: 'weekend',
    label: 'Weekend'
  }
];

const MATCH_FOOD =
   [
      {
         "_id":"66fbe059a9ca2512e9911c3e",
         "analysis_id": undefined,
         "client_id":"global",
         "description":"Cheeseburger, Single Regular Patty With Condiments",
         "original_description":null,
         "category":"Fast Foods",
         "provider":"usda_legacy",
         "quantity":1,
         "unit":"sandwich",
         "gram_weight":250,
         "protein":33.725,
         "phe":1686.25,
         "nutrients":[
            {
               "name":"protein",
               "amount":13.49,
               "unit":"g"
            },
            {
               "name":"phe",
               "amount":674.5,
               "unit":"mg"
            }
         ],
         "portions":[
            {
               "name":"sandwich",
               "quantity":1,
               "gram_weight":250,
               "sequence_number":null
            },
            {
               "name":"Item",
               "quantity":1,
               "gram_weight":127,
               "sequence_number":1
            },
            {
               "name":"g",
               "quantity":1,
               "gram_weight":1,
               "sequence_number":98
            },
            {
               "name":"oz",
               "quantity":1,
               "gram_weight":28.3495,
               "sequence_number":99
            }
         ],
         "factors":{
            "proteinFactor":0.1349,
            "pheFactor":6.745
         },
         "isFreeFood":false,
         "ingredients":[
            
         ]
      },
      {
         "_id":"66fbe036a9ca2512e991169c",
         "analysis_id": undefined,
         "client_id":"global",
         "description":"Lettuce, Green Leaf, Raw",
         "original_description":null,
         "category":"Vegetables and Vegetable Products",
         "provider":"usda_legacy",
         "quantity":2,
         "unit":"Leaf Inner",
         "gram_weight":4.8,
         "protein":0.13056,
         "phe":5.28,
         "nutrients":[
            {
               "name":"protein",
               "amount":1.36,
               "unit":"g"
            },
            {
               "name":"phe",
               "amount":55,
               "unit":"mg"
            }
         ],
         "portions":[
            {
               "name":"Cup Shredded",
               "quantity":1,
               "gram_weight":36,
               "sequence_number":1
            },
            {
               "name":"Head",
               "quantity":1,
               "gram_weight":360,
               "sequence_number":2
            },
            {
               "name":"Leaf Inner",
               "quantity":1,
               "gram_weight":4.8,
               "sequence_number":3
            },
            {
               "name":"Leaf Outer",
               "quantity":1,
               "gram_weight":24,
               "sequence_number":4
            },
            {
               "name":"g",
               "quantity":1,
               "gram_weight":1,
               "sequence_number":98
            },
            {
               "name":"oz",
               "quantity":1,
               "gram_weight":28.3495,
               "sequence_number":99
            }
         ],
         "factors":{
            "proteinFactor":0.013600000000000001,
            "pheFactor":0.55
         },
         "isFreeFood":false,
         "ingredients":[
            
         ]
      },
      {
         "_id":"66fbe053a9ca2512e9911b54",
         "analysis_id": undefined,
         "client_id":"global",
         "description":"Tomatoes, Red Ripe Raw",
         "original_description":null,
         "category":"Vegetables and Vegetable Products",
         "provider":"usda_legacy",
         "quantity":2,
         "unit":"slices",
         "gram_weight":20,
         "protein":0.35200000000000004,
         "phe":10.8,
         "nutrients":[
            {
               "name":"protein",
               "amount":0.88,
               "unit":"g"
            },
            {
               "name":"phe",
               "amount":27,
               "unit":"mg"
            }
         ],
         "portions":[
            {
               "name":"slices",
               "quantity":1,
               "gram_weight":20,
               "sequence_number":null
            },
            {
               "name":"Cup Cherry Tomatoes",
               "quantity":1,
               "gram_weight":149,
               "sequence_number":1
            },
            {
               "name":"Cup, Chopped or Sliced",
               "quantity":1,
               "gram_weight":180,
               "sequence_number":2
            },
            {
               "name":"Italian Tomato",
               "quantity":1,
               "gram_weight":62,
               "sequence_number":3
            },
            {
               "name":"Cherry",
               "quantity":1,
               "gram_weight":17,
               "sequence_number":4
            },
            {
               "name":"Large Whole (3\" Dia)",
               "quantity":1,
               "gram_weight":182,
               "sequence_number":5
            },
            {
               "name":"Medium Whole (2-3/5\" Dia)",
               "quantity":1,
               "gram_weight":123,
               "sequence_number":6
            },
            {
               "name":"Slice, Medium (1/4\" Thick)",
               "quantity":1,
               "gram_weight":20,
               "sequence_number":7
            },
            {
               "name":"Plum Tomato",
               "quantity":1,
               "gram_weight":62,
               "sequence_number":8
            },
            {
               "name":"Small Whole (2-2/5\" Dia)",
               "quantity":1,
               "gram_weight":91,
               "sequence_number":9
            },
            {
               "name":"Slice, Thick/Large (1/2\" Thick)",
               "quantity":1,
               "gram_weight":27,
               "sequence_number":10
            },
            {
               "name":"Wedge (1/4 of Medium Tomato)",
               "quantity":1,
               "gram_weight":31,
               "sequence_number":11
            },
            {
               "name":"Slice, Thin/Small",
               "quantity":1,
               "gram_weight":15,
               "sequence_number":12
            },
            {
               "name":"NLEA Serving",
               "quantity":1,
               "gram_weight":148,
               "sequence_number":13
            },
            {
               "name":"g",
               "quantity":1,
               "gram_weight":1,
               "sequence_number":98
            },
            {
               "name":"oz",
               "quantity":1,
               "gram_weight":28.3495,
               "sequence_number":99
            }
         ],
         "factors":{
            "proteinFactor":0.0088,
            "pheFactor":0.27
         },
         "isFreeFood":false,
         "ingredients":[
            
         ]
      },
      {
         "_id":"675c1860bbb1d46bffff04cd",
         "analysis_id": undefined,
         "client_id":"global",
         "description":"Mature Cheddar Cheese Slices, Violife",
         "original_description":null,
         "category":"Dairy and Egg Products",
         "provider":"pkuperspectives",
         "quantity":1,
         "unit":"slice",
         "gram_weight":28,
         "protein":0,
         "phe":8.4,
         "nutrients":[
            {
               "name":"protein",
               "amount":0,
               "unit":"g"
            },
            {
               "name":"phe",
               "amount":6,
               "unit":"mg"
            }
         ],
         "portions":[
            {
               "name":"slice",
               "quantity":1,
               "gram_weight":28,
               "sequence_number":null
            },
            {
               "name":"Slice",
               "quantity":1,
               "gram_weight":20,
               "sequence_number":null
            },
            {
               "name":"g",
               "quantity":1,
               "gram_weight":1,
               "sequence_number":98
            },
            {
               "name":"oz",
               "quantity":1,
               "gram_weight":28.3495,
               "sequence_number":99
            }
         ],
         "factors":{
            "proteinFactor":0,
            "pheFactor":0.3
         },
         "isFreeFood":false,
         "ingredients":[
            
         ]
      },
      {
         "_id":"66fbe08ba9ca2512e9912477",
         "analysis_id": undefined,
         "client_id":"global",
         "description":"Rolls, Hamburger or Hotdog, Plain",
         "original_description":null,
         "category":"Baked Products",
         "provider":"usda_legacy",
         "quantity":1,
         "unit":"Roll 1 Serving",
         "gram_weight":44,
         "protein":4.2988,
         "phe":214.94,
         "nutrients":[
            {
               "name":"protein",
               "amount":9.77,
               "unit":"g"
            },
            {
               "name":"phe",
               "amount":488.5,
               "unit":"mg"
            }
         ],
         "portions":[
            {
               "name":"Roll 1 Serving",
               "quantity":1,
               "gram_weight":44,
               "sequence_number":1
            },
            {
               "name":"Oz",
               "quantity":1,
               "gram_weight":28.35,
               "sequence_number":2
            },
            {
               "name":"g",
               "quantity":1,
               "gram_weight":1,
               "sequence_number":98
            }
         ],
         "factors":{
            "proteinFactor":0.0977,
            "pheFactor":4.885
         },
         "isFreeFood":false,
         "ingredients":[
            
         ]
      }
]

export const MOCK_DATA = {
  MEDICATION_LISTING,
  FORMULA_LISTING,
  MEDICATION_CATEGORIES,
  PILL_SHAPES,
  MEDICATION_FREQUENCIES,
  MEDICATION_REMINDER,
  MEDICATION_DAILY_ROUTINE,
  MEDICATION_WEEKLY_ROUTINE,
  MEDICATION_YEARLY_ROUTINE,
  CUSTOM_CALENDAR_WEEKS,
  CUSTOM_CALENDAR_WEEKS_DAYS,
  MATCH_FOOD
};
