trigger:
  branches:
    include:
      - main  # Adjust based on your branch

pool:
  vmImage: 'macos-latest'  # Use 'ubuntu-latest' if not building iOS

steps:

  # Install dependencies
  - task: Bash@3
    inputs:
      targetType: 'inline'
      script: 'yarn install --frozen-lockfile'
    displayName: 'Install dependencies'

  # Download Keystore File from Azure Secure Files
  - task: DownloadSecureFile@1
    name: keystoreFile
    inputs:
      secureFile: 'cyclevitapku-key.keystore'  # Ensure you uploaded this to Azure DevOps Secure Files
    displayName: 'Download Keystore File'

  # Move the Keystore to the correct location
  - script: |
      mv $(keystoreFile.secureFilePath) android/app/cyclevitapku-key.keystore
    displayName: 'Move Keystore to Android App Directory'

  # Ensure Gradle wrapper is executable
  - task: Bash@3
    inputs:
      targetType: 'inline'
      script: 'chmod +x android/gradlew'
    displayName: 'Fix Gradle permissions'

  # Ensure APK Output Directory Exists (Prevents Copy Error)
  - script: |
      mkdir -p android/app/build/outputs/apk/release/
    displayName: 'Ensure APK Output Directory Exists'

  # Build APK using Gradle
  - task: Gradle@2
    inputs:
      gradleWrapperFile: 'android/gradlew'
      workingDirectory: 'android'
      gradleOptions: '-Xmx3072m'
      tasks: 'clean assembleRelease'
    displayName: 'Build Android APK'

  # Debug: Check if APK was generated
  - script: |
      ls -R android/app/build/outputs/
    displayName: 'Check APK Output Directory'

  # Copy the APK to artifact staging
  - task: CopyFiles@2
    inputs:
      sourceFolder: 'android/app/build/outputs/apk/production/release/'
      contents: '*.apk'
      targetFolder: '$(Build.ArtifactStagingDirectory)'
    displayName: 'Copy APK to Artifact Directory'

  # Publish the APK as an artifact
  - task: PublishBuildArtifacts@1
    inputs:
      pathToPublish: '$(Build.ArtifactStagingDirectory)'
      artifactName: 'drop'
    displayName: 'Publish APK'
