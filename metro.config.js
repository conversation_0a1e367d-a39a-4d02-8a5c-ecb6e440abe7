const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');

module.exports = (async () => {
  const defaultConfig = await getDefaultConfig(__dirname);

  const customConfig = {
    transformer: {
      babelTransformerPath: require.resolve("react-native-svg-transformer"),
    },
    resolver: {
      assetExts: [...defaultConfig.resolver.assetExts.filter((ext) => ext !== "svg"), "lottie"],
      sourceExts: [...defaultConfig.resolver.sourceExts, "svg", 'js', 'jsx', 'ts', 'tsx', 'cjs', 'mjs', 'json'],
    },
  };

  return mergeConfig(defaultConfig, customConfig);
})();
