#!/bin/bash

# Script to configure environment settings based on the target environment
# Usage: ./configure-env.sh [environment]
# Example: ./configure-env.sh production

ENV=${1:-"development"}
ROOT_DIR="$(pwd)"
FIREBASE_CONFIG="${ROOT_DIR}/firebase.json"
ANALYTICS_FILE="${ROOT_DIR}/src/hooks/useAnalytics.ts"
ENVIRONMENT_FILE="${ROOT_DIR}/src/utils/environment.ts"

echo "Configuring app for environment: $ENV"

# Function to update firebase.json without using jq
update_firebase_config() {
  local analytics_enabled=$1
  
  # Create a new firebase.json with the updated value
  echo '{
  "react-native": {
    "analytics_auto_collection_enabled": '$analytics_enabled'
  }
}' > "$FIREBASE_CONFIG"
  
  echo "Updated firebase.json: analytics_auto_collection_enabled = $analytics_enabled"
}

# Function to update productionEnv in useAnalytics.ts
update_analytics_env() {
  local is_production=$1
  
  # Use sed to replace the productionEnv value
  if [[ "$(uname)" == "Darwin" ]]; then
    # macOS version of sed requires an empty string for -i
    sed -i '' "s/const productionEnv = .*;/const productionEnv = $is_production;/" "$ANALYTICS_FILE"
  else
    # Linux version (which Azure DevOps typically uses)
    sed -i "s/const productionEnv = .*;/const productionEnv = $is_production;/" "$ANALYTICS_FILE"
  fi
  
  echo "Updated useAnalytics.ts: productionEnv = $is_production"
}

# Function to update BASE_URL in environment.ts
update_base_url() {
  local env=$1
  local new_url
  
  if [[ "$env" == "production" ]]; then
    new_url="https://cyclevita-pku.com/api/\${VERSION.versionTwo}"
    # Use sed to replace the BASE_URL value
    if [[ "$(uname)" == "Darwin" ]]; then
      # macOS version of sed requires an empty string for -i
      sed -i '' "s|export const BASE_URL = \`.*\`;|export const BASE_URL = \`$new_url\`;|" "$ENVIRONMENT_FILE"
    else
      # Linux version
      sed -i "s|export const BASE_URL = \`.*\`;|export const BASE_URL = \`$new_url\`;|" "$ENVIRONMENT_FILE"
    fi
  else
    # For non-production environments, use the template with Environment enum
    # Convert environment name to uppercase for the Environment enum
    local env_upper=$(echo "$env" | tr '[:lower:]' '[:upper:]')
    
    if [[ "$(uname)" == "Darwin" ]]; then
      # macOS version of sed
      sed -i '' "s|export const BASE_URL = \`.*\`;|export const BASE_URL = \`https://api-cycle-\${Environment.$env_upper}.azurewebsites.net/api/\${VERSION.versionTwo}\`;|" "$ENVIRONMENT_FILE"
    else
      # Linux version
      sed -i "s|export const BASE_URL = \`.*\`;|export const BASE_URL = \`https://api-cycle-\${Environment.$env_upper}.azurewebsites.net/api/\${VERSION.versionTwo}\`;|" "$ENVIRONMENT_FILE"
    fi
  fi
  
  echo "Updated environment.ts: BASE_URL for $env environment"
}

# Main execution
if [[ "$ENV" == "production" ]]; then
  update_firebase_config true
  update_analytics_env true
  update_base_url "production"
  echo "✅ App configured for PRODUCTION environment"
else
  update_firebase_config false
  update_analytics_env false
  
  # Convert environment name to uppercase for the Environment enum
  ENV_UPPER=$(echo "$ENV" | tr '[:lower:]' '[:upper:]')
  
  # Check if it's a valid environment
  if [[ "$ENV_UPPER" == "UAT" || "$ENV_UPPER" == "DEVELOPMENT" ]]; then
    update_base_url "$ENV"
    echo "✅ App configured for $ENV_UPPER environment"
  else
    echo "⚠️ Unknown environment: $ENV. Using DEVELOPMENT as default."
    update_base_url "DEVELOPMENT"
  fi
fi