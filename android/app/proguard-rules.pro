# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /usr/local/Cellar/android-sdk/24.3.3/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:


# -keep class com.mypackage.BuildConfig { *; }
# -keepresources string/build_config_package

#============================================================
# Basic Configuration
#============================================================
-optimizationpasses 5
-allowaccessmodification
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-dontpreverify
-verbose

#============================================================
# React Native Core
#============================================================
-keep class com.facebook.react.** { *; }
-keep class com.facebook.hermes.unicode.** { *; }
-keep class com.facebook.jni.** { *; }
-keep class com.facebook.fbreact.specs.** { *; }

# Hermes (JavaScript Engine)
-keep class com.facebook.hermes.** { *; }

#============================================================
# Application-Specific Rules (UPDATED)
#============================================================
-keep class com.cyclevita.BuildConfig { *; }
-keep class com.cyclevita.MainApplication { *; }
-keep public class com.cyclevita.** { *; }

#============================================================
# Dependency-Specific Rules
#============================================================
# React Native Reanimated
-keep class com.swmansion.reanimated.** { *; }
-keep class com.swmansion.gesturehandler.** { *; }

# React Navigation
-keep class androidx.navigation.** { *; }

# Firebase
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }
-dontwarn com.google.firebase.**
-keepattributes *Annotation*
-keepclassmembers class * {
    @com.google.firebase.database.IgnoreExtraProperties <fields>;
}

# Auth0
-keep class com.auth0.** { *; }
-keep class com.auth0.android.** { *; }
-keepnames class com.auth0.android.jwt.** { *; }

# Vision Camera
-keep class com.arthenica.ffmpegkit.** { *; }
-keep class com.arthenica.reactnative.** { *; }
-keep class com.mrousavy.camera.** { *; }

# MMKV
-keep class com.tencent.mmkv.** { *; }

# React Native SVG
-keep class com.horcrux.svg.** { *; }

# React Native Config
-keep class com.lugg.ReactNativeConfig.** { *; }

# React Native Notifee
-keep class io.invertase.notifee.** { *; }

# React Native Device Info
-keep class com.learnium.RNDeviceInfo.** { *; }

# React Native Image Picker
-keep class com.imagepicker.** { *; }

# React Native Video
-keep class com.brentvatne.react.** { *; }
-keep class com.youtube.** { *; }

#============================================================
# General Security Hardening
#============================================================
-keepclasseswithmembernames class * {
    native <methods>;
}

-keepattributes *Annotation*, Signature, InnerClasses, EnclosingMethod

-keep class androidx.** { *; }
-keep interface androidx.** { *; }

-keep public class * extends android.view.View {
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

-keepnames class * implements java.io.Serializable
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    !static !transient <fields>;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

#============================================================
# Obfuscation Protection
#============================================================
-keepclassmembers class **.R$* {
    public static <fields>;
}

-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.app.backup.BackupAgentHelper
-keep public class * extends android.preference.Preference

#============================================================
# HTTPS/SSL Protection
#============================================================
-keep class org.conscrypt.** { *; }
-keep class com.google.android.gms.security.** { *; }

-keepclassmembers class * extends android.webkit.WebViewClient {
    public void onReceivedSslError(android.webkit.WebView, android.webkit.SslErrorHandler, android.net.http.SslError);
}