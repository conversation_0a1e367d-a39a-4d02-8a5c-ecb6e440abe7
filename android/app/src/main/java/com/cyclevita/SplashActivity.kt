package com.cyclevita

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import androidx.appcompat.app.AppCompatActivity

class SplashActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Optionally set a splash screen layout
        setContentView(R.layout.launch_screen)

        // Add a delay to show the splash screen for 2 seconds
        Handler(Looper.getMainLooper()).postDelayed({
            // Navigate to MainActivity
            startActivity(Intent(this, MainActivity::class.java))
            // Finish SplashActivity to remove it from the back stack
            finish()
        }, 1800) // 1800 ms = 1.8 seconds
    }
}
