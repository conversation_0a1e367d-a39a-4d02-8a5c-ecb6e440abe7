<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFFFFF"> <!-- Set your desired background color -->

    <!-- Logo at the top horizontally centered -->
    <ImageView
        android:id="@+id/logo_image"
        android:layout_marginTop="84dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        android:src="@drawable/logo"
        android:contentDescription="Logo" />

    <!-- Bottom image takes full width and has no bottom padding -->
    <ImageView
        android:id="@+id/bottom_image"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:src="@drawable/bottom_image"
        android:contentDescription="Bottom Image"
        android:scaleType="fitXY" /> <!-- Ensures the image stretches to fit the width -->

</RelativeLayout>
